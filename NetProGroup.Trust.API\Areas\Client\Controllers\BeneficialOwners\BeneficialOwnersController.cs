﻿// <copyright file="BeneficialOwnersController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.BeneficialOwners
{
    /// <summary>
    /// Use this controller for beneficialowner related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/beneficial-owners")]
    public class BeneficialOwnersController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IBeneficialOwnersAppService _beneficialOwnersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnersController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="beneficialOwnersAppService">The service for BeneficialOwners.</param>
        public BeneficialOwnersController(
            ILogger<BeneficialOwnersController> logger,
            IConfiguration configuration,
            IBeneficialOwnersAppService beneficialOwnersAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _beneficialOwnersAppService = beneficialOwnersAppService;
        }

        /// <summary>
        /// Gets the given BeneficialOwner.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/beneficial-owners/{relationId}.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the beneficial owner to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerDTO"/>.</returns>
        [HttpGet("{relationId}")]
        [SwaggerOperation(OperationId = "Client_GetBeneficialOwner")]
        [ProducesResponseType(typeof(BeneficialOwnerDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBeneficialOwner(string relationId)
        {
            BeneficialOwnerDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _beneficialOwnersAppService.GetBeneficialOwnerAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given BeneficialOwner with the current and the prior version.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/beneficial-owners/{relationId}/comparison.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the beneficial owner to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerComparisonDTO"/>.</returns>
        [HttpGet("{relationId}/comparison")]
        [SwaggerOperation(OperationId = "Client_GetBeneficialOwnerForComparison")]
        [ProducesResponseType(typeof(BeneficialOwnerComparisonDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBeneficialOwnerForComparison(string relationId)
        {
            BeneficialOwnerComparisonDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerComparisonDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _beneficialOwnersAppService.GetBeneficialOwnerForComparisonAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Puts a confirmation for the BeneficialOwner.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/client/beneficial-owners/{relationId}/confirmation.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the beneficial owner to confirm.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerDTO"/>.</returns>
        [HttpPut("{relationId}/confirmation")]
        [SwaggerOperation(OperationId = "Client_ConfirmBeneficialOwner")]
        [ProducesResponseType(typeof(BeneficialOwnerDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> ConfirmBeneficialOwner(string relationId)
        {
            BeneficialOwnerDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                },
                executeAsync: async () =>
                {
                    item = await _beneficialOwnersAppService.SetConfirmationAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts an update request for the BeneficialOwner.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/beneficial-owners/{relationId}/update-request
        ///     {
        ///         requestUpdateDTO
        ///     }
        ///
        /// UpdateRequestType is one of the following:
        ///
        ///   MissingBeneficialOwners = 101
        ///   ChangeOfBeneficialOwners = 201
        ///   ChangeOfBeneficialOwnersAddress = 202
        ///   ChangeOfBeneficialOwnersParticulars = 203
        ///   OtherUpdateOfBeneficialOwners = 301.
        /// </remarks>
        /// <param name="relationId">The unique relationId of the beneficial owner to create an update request for.</param>
        /// <param name="requestUpdateDTO">The model for the request for update.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerDTO"/>.</returns>
        [HttpPost("{relationId}/update-request")]
        [SwaggerOperation(OperationId = "Client_RequestBeneficialOwnerUpdate")]
        [ProducesResponseType(typeof(BeneficialOwnerDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestBeneficialOwnerUpdate(string relationId, RequestUpdateDTO requestUpdateDTO)
        {
            BeneficialOwnerDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                    requestUpdateDTO.UniqueRelationId = relationId;
                },
                executeAsync: async () =>
                {
                    item = await _beneficialOwnersAppService.RequestUpdateAsync(requestUpdateDTO);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Simulates an update to a beneficial owner's name and returns a comparison of the changes.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/beneficial-owners/{relationId}/sync-simulation.
        /// </remarks>
        /// <param name="relationId">The unique relationId of the beneficial owner to simulate update for.</param>
        /// <param name="newName">The new name to simulate for the beneficial owner.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="BeneficialOwnerComparisonDTO"/>.</returns>
        [HttpPost("{relationId}/sync-simulation")]
        [SwaggerOperation(OperationId = "Client_BeneficialOwnerUpdateSimulation")]
        [ProducesResponseType(typeof(BeneficialOwnerComparisonDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> BeneficialOwnerUpdateSimulation(string relationId, string newName)
        {
            BeneficialOwnerComparisonDTO item = null;

            var result = await ProcessRequestAsync<BeneficialOwnerComparisonDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                    Check.NotNullOrWhiteSpace(newName, nameof(newName));
                },
                executeAsync: async () =>
                {
                    await _beneficialOwnersAppService.SimulateUpdateSync(relationId, newName);

                    item = await _beneficialOwnersAppService.GetBeneficialOwnerForComparisonAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
