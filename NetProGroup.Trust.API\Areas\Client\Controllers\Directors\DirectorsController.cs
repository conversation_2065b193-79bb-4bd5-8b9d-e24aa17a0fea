﻿// <copyright file="DirectorsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Directors
{
    /// <summary>
    /// Use this controller for Director related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class DirectorsController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IDirectorsAppService _directorsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="directorsAppService">The service for Directors.</param>
        public DirectorsController(
            ILogger<DirectorsController> logger,
            IConfiguration configuration,
            IDirectorsAppService directorsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _directorsAppService = directorsAppService;
        }

        /// <summary>
        /// Gets the given Director.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/directors/{relationId}.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the director to confirm.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="DirectorDTO"/>.</returns>
        [HttpGet("{relationId}")]
        [SwaggerOperation(OperationId = "Client_GetDirector")]
        [ProducesResponseType(typeof(DirectorDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDirector(string relationId)
        {
            DirectorDTO item = null;

            var result = await ProcessRequestAsync<DirectorDTO>(
                validate: () =>
                {
                },
            executeAsync: async () =>
            {
                item = await _directorsAppService.GetDirectorAsync(relationId);
            },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given Director with the current and the prior version.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/directors/{relationId}/comparison.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the director to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="DirectorComparisonDTO"/>.</returns>
        [HttpGet("{relationId}/comparison")]
        [SwaggerOperation(OperationId = "Client_GetDirectorForComparison")]
        [ProducesResponseType(typeof(DirectorComparisonDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDirectorForComparison(string relationId)
        {
            DirectorComparisonDTO item = null;

            var result = await ProcessRequestAsync<DirectorComparisonDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _directorsAppService.GetDirectorForComparisonAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Puts a confirmation for the Director.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/client/directors/{relationId}/confirmation.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the director to confirm.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="DirectorDTO"/>.</returns>
        [HttpPut("{relationId}/confirmation")]
        [SwaggerOperation(OperationId = "Client_ConfirmDirector")]
        [ProducesResponseType(typeof(DirectorDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> ConfirmDirector(string relationId)
        {
            DirectorDTO item = null;

            var result = await ProcessRequestAsync<DirectorDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                },
                executeAsync: async () =>
                {
                    item = await _directorsAppService.SetConfirmationAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts an update request for the Director.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/directors/{relationId}/update-request
        ///     {
        ///         requestUpdateDTO
        ///     }
        ///
        /// UpdateRequestType is one of the following:
        ///
        ///   MissingDirectors = 102
        ///   ChangeOfDirectors = 211
        ///   ChangeOfDirectorsAddress = 212
        ///   ChangeOfDirectorsParticulars = 213
        ///   OtherUpdateOfDirectors = 302.
        /// </remarks>
        /// <param name="relationId">The unique relationId of the director to create an update request for.</param>
        /// <param name="requestUpdateDTO">The model for the request for update.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="DirectorDTO"/>.</returns>
        [HttpPost("{relationId}/update-request")]
        [SwaggerOperation(OperationId = "Client_RequestDirectorUpdate")]
        [ProducesResponseType(typeof(DirectorDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestDirectorUpdate(string relationId, RequestUpdateDTO requestUpdateDTO)
        {
            DirectorDTO item = null;

            var result = await ProcessRequestAsync<DirectorDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                    requestUpdateDTO.UniqueRelationId = relationId;
                },
                executeAsync: async () =>
                {
                    item = await _directorsAppService.RequestUpdateAsync(requestUpdateDTO);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Simulates the directors update.
        /// </summary>
        /// <param name="relationId">The ID of the reatoin to change.</param>
        /// <param name="newName">New director name.</param>
        /// <returns>The updated directory DTO.</returns>
        [HttpPost("{relationId}/sync-simulation")]
        [SwaggerOperation(OperationId = "Client_DirecctorUpdateSimulation")]
        [ProducesResponseType(typeof(DirectorComparisonDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> DirectorUpdateSimulation(string relationId, string newName)
        {
            DirectorComparisonDTO item = null;

            var result = await ProcessRequestAsync<DirectorComparisonDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                    Check.NotNullOrWhiteSpace(newName, nameof(newName));
                },
                executeAsync: async () =>
                {
                    await _directorsAppService.SimulateUpdateSync(relationId, newName);

                    item = await _directorsAppService.GetDirectorForComparisonAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
