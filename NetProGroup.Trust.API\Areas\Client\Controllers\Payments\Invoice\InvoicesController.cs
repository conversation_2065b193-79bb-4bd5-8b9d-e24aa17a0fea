// <copyright file="InvoicesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Payments.Invoice
{
    /// <summary>
    /// Use this controller for Invoices related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/invoices")]
    public class InvoicesController : TrustAPIControllerBase
    {
        private readonly IInvoiceAppService _invoiceAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="InvoicesController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="invoiceAppService">An instance of IInvoiceAppService.</param>
        public InvoicesController(
            ILogger<InvoicesController> logger,
            IInvoiceAppService invoiceAppService)
            : base(logger)
        {
            _invoiceAppService = invoiceAppService;
        }

        /// <summary>
        /// Gets a paged list of invoices based on the provided request parameters.
        /// </summary>
        /// <param name="invoiceListRequestDto">The request parameters for listing invoices.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting paged list of invoices.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/Invoices?pageNumber=1&amp;pageSize=10
        ///
        /// Sample response:
        ///
        ///     {
        ///         "items": [
        ///             {
        ///                 "id": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
        ///                 "companyName": "Acme Inc.",
        ///                 "amount": 1000.00,
        ///                 "financialYear": 2023,
        ///                 "incorporationNr": "ABC123",
        ///                 "file": "invoice_123.pdf",
        ///                 "CurrencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
        ///                 "status": 0,
        ///                 "currencySymbol": "$",
        ///                 "paidDate": "2023-05-18T10:30:00Z",
        ///                 "transactionId": 987654,
        ///                 "txId": "TX98765",
        ///                 "layout": "TRIDENTTRUST"
        ///             },
        ///             ...
        ///         ],
        ///         "pageNumber": 1,
        ///         "totalPages": 3,
        ///         "totalCount": 25,
        ///         "pageSize": 10,
        ///         "hasPreviousPage": false,
        ///         "hasNextPage": true
        ///     }.
        /// </remarks>
        [HttpGet]
        [SwaggerOperation(OperationId = "Client_GetInvoices")]
        [ProducesResponseType(typeof(PaginatedResponse<InvoiceDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetInvoices([FromQuery] InvoiceListRequestDTO invoiceListRequestDto)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                new PagingInfo(invoiceListRequestDto.PageNumber, invoiceListRequestDto.PageSize),
                validate: () => { },
                executeAsync: async (pagingInfo) =>
                {
                    return await _invoiceAppService.ListInvoicesAsync(invoiceListRequestDto);
                });
            return result.AsResponse();
        }

        /// <summary>
        /// Gets an invoice by its ID.
        /// </summary>
        /// <param name="id">The ID of the invoice to retrieve.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the invoice details.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/invoices/{id}
        ///
        /// Sample response:
        ///
        ///     {
        ///         "id": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
        ///         "companyName": "Acme Inc.",
        ///         "amount": 1000.00,
        ///         "financialYear": 2023,
        ///         "incorporationNr": "ABC123",
        ///         "file": "invoice_123.pdf",
        ///         "status": 0,
        ///         "currencySymbol": "$",
        ///         "paidDate": "2023-05-18T10:30:00Z",
        ///         "transactionId": 987654,
        ///         "txId": "TX98765",
        ///         "currencyId": "6ca5809f-06a3-4e99-9e0a-df570d3a482f",
        ///         "layout": "TRIDENTTRUST",
        ///         "invoiceLines": [
        ///             {
        ///                 "invoiceId": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
        ///                 "currencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
        ///                 "description": "Late filing fee",
        ///                 "sequence": 2,
        ///                 "amount": 75.00,
        ///                 "id": "504c0de4-2ba9-41ad-825c-b45d25f9bf6b"
        ///             },
        ///             {
        ///                 "invoiceId": "a0f43f9c-2d1c-4d4e-b74d-e89cc5fbe1c7",
        ///                 "currencyId": "d1b8f5c3-3c3f-4f32-9d1f-a56450e77c1e",
        ///                 "description": "Submission fee for year 2020",
        ///                 "sequence": 1,
        ///                 "amount": 200.00,
        ///                 "id": "415dced3-d7e4-43d3-b0c8-ce9160976c2a"
        ///             }
        ///         ]
        ///     }.
        /// </remarks>
        [HttpGet("{id:guid}")]
        [SwaggerOperation(OperationId = "Client_GetInvoiceById")]
        [ProducesResponseType(typeof(InvoiceDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetInvoiceById(Guid id)
        {
            InvoiceDTO item = null;

            var result = await ProcessRequestAsync<InvoiceDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _invoiceAppService.GetInvoiceByIdAsync(id);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}