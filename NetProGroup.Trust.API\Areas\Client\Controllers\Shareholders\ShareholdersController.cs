﻿// <copyright file="ShareholdersController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Shareholders
{
    /// <summary>
    /// Use this controller for shareholder related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/shareholders")]
    public class ShareholdersController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IShareholdersAppService _shareholdersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ShareholdersController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="shareholdersAppService">The service for Shareholders.</param>
        public ShareholdersController(
            ILogger<ShareholdersController> logger,
            IConfiguration configuration,
            IShareholdersAppService shareholdersAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _shareholdersAppService = shareholdersAppService;
        }

        /// <summary>
        /// Gets the given Shareholder.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/shareholders/{relationId}.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the Shareholder to confirm.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="ShareholderDTO"/>.</returns>
        [HttpGet("{relationId}")]
        [SwaggerOperation(OperationId = "Client_GetShareholder")]
        [ProducesResponseType(typeof(ShareholderDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetShareholder(string relationId)
        {
            ShareholderDTO item = null;

            var result = await ProcessRequestAsync<ShareholderDTO>(
                validate: () =>
                {
                },
                executeAsync: async () =>
                {
                    item = await _shareholdersAppService.GetShareholderAsync(relationId);
                },
                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Puts a confirmation for the Shareholder data.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/client/shareholders/{relationId}/confirmation.
        ///
        /// </remarks>
        /// <param name="relationId">The unique relationId of the Shareholder to confirm.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="ShareholderDTO"/>.</returns>
        [HttpPut("{relationId}/confirmation")]
        [SwaggerOperation(OperationId = "Client_ConfirmShareholder")]
        [ProducesResponseType(typeof(ShareholderDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> ConfirmShareholder(string relationId)
        {
            ShareholderDTO item = null;

            var result = await ProcessRequestAsync<ShareholderDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                },
                executeAsync: async () =>
                {
                    item = await _shareholdersAppService.SetConfirmationAsync(relationId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts an update request for the Shareholder.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/client/shareholders/{relationId}/update-request
        ///     {
        ///         requestUpdateDTO
        ///     }
        ///
        /// UpdateRequestType is one of the following:
        ///
        ///   MissingShareholders = 103
        ///   ChangeOfShareholders = 221
        ///   ChangeOfShareholdersAddress = 222
        ///   ChangeOfShareholdersParticulars = 223
        ///   OtherUpdateOfShareholders = 303.
        /// </remarks>
        /// <param name="relationId">The unique relationId of the Shareholder to create an update request for.</param>
        /// <param name="requestUpdateDTO">The model for the request for update.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="ShareholderDTO"/>.</returns>
        [HttpPost("{relationId}/update-request")]
        [SwaggerOperation(OperationId = "Client_RequestShareholderUpdate")]
        [ProducesResponseType(typeof(ShareholderDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestShareholderUpdate(string relationId, RequestUpdateDTO requestUpdateDTO)
        {
            ShareholderDTO item = null;

            var result = await ProcessRequestAsync<ShareholderDTO>(
                validate: () =>
                {
                    Check.NotNullOrWhiteSpace(relationId, nameof(relationId));
                    requestUpdateDTO.UniqueRelationId = relationId;
                },
                executeAsync: async () =>
                {
                    item = await _shareholdersAppService.RequestUpdateAsync(requestUpdateDTO);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
