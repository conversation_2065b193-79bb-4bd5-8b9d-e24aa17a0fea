﻿// <copyright file="FormTemplatesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.AppServices.Forms;
using NetProGroup.Trust.Application.Contracts.Forms;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Forms
{
    /// <summary>
    /// Controller for form templates.
    /// </summary>
    [ApiController]
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/form-templates")]
    public class FormTemplatesController : TrustAPIControllerBase
    {
        private readonly ILogger<FormTemplatesController> _logger;
        private readonly IFormTemplatesAppService _formTemplatesAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="FormTemplatesController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="formTemplatesAppService">The service for form templates.</param>
        public FormTemplatesController(
            ILogger<FormTemplatesController> logger,
            IFormTemplatesAppService formTemplatesAppService)
            : base(logger)
        {
            _logger = logger;
            _formTemplatesAppService = formTemplatesAppService;
        }

        /// <summary>
        /// Gets a list with form templates.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/form-templates.
        ///
        /// </remarks>
        /// <param name="jurisdictionId">Id of the jurisdiction to get the templates for.</param>
        /// <param name="moduleId">Optional id of the module to get the templates for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the form templates.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetFormTemplates")]
        [ProducesResponseType(typeof(ListFormTemplatesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFormTemplate(
            Guid jurisdictionId,
            Guid? moduleId)
        {
            ListFormTemplatesDTO item = null;

            var result = await ProcessRequestAsync<ListFormTemplatesDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));
                },

                executeAsync: async () =>
                {
                    item = await _formTemplatesAppService.GetFormTemplatesAsync(jurisdictionId, moduleId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the given form template.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/form-templates/{templateid}.
        ///
        /// </remarks>
        /// <param name="templateId">The id of the template to get.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the form template.</returns>
        [HttpGet("{templateId}")]
        [SwaggerOperation(OperationId = "GetFormTemplate")]
        [ProducesResponseType(typeof(FormTemplateDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFormTemplate(Guid templateId)
        {
            FormTemplateDTO item = null;

            var result = await ProcessRequestAsync<FormTemplateDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(templateId, nameof(templateId));
                },

                executeAsync: async () =>
                {
                    item = await _formTemplatesAppService.GetFormTemplateAsync(templateId, false);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts a new version for the form template.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/management/form-templates/{templateid}/template-versions.
        ///
        /// </remarks>
        /// <param name="templateId">The id of the template to add the version to.</param>
        /// <param name="model">The model holding the data for the new version.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the updated form template.</returns>
        [HttpPost("{templateId}/template-versions")]
        [SwaggerOperation(OperationId = "CreateFormTemplateVersion")]
        [ProducesResponseType(typeof(FormTemplateWithVersionsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateFormTemplateVersion(
            Guid templateId,
            CreateFormTemplateVersionDTO model)
        {
            FormTemplateWithVersionsDTO item = null;

            var result = await ProcessRequestAsync<FormTemplateWithVersionsDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(templateId, nameof(templateId));
                },

                executeAsync: async () =>
                {
                    item = await _formTemplatesAppService.CreateFormTemplateVersionAsync(model);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Puts an update for the version for the form template.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/management/form-templates/{templateid}/template-versions/{versionId}.
        ///
        /// </remarks>
        /// <param name="templateId">The id of the template that the version belongs to.</param>
        /// <param name="versionId">The id of the version that is to be updated.</param>
        /// <param name="model">The model holding the data for the new version.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the updated form template.</returns>
        [HttpPost("{templateId}/template-versions/{versionId}")]
        [SwaggerOperation(OperationId = "UpdateFormTemplateVersion")]
        [ProducesResponseType(typeof(FormTemplateWithVersionsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateFormTemplateVersion(
            Guid templateId,
            Guid versionId,
            UpdateFormTemplateVersionDTO model)
        {
            FormTemplateWithVersionsDTO item = null;

            var result = await ProcessRequestAsync<FormTemplateWithVersionsDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(templateId, nameof(templateId));
                    Check.NotDefaultOrNull<Guid>(versionId, nameof(versionId));

                    model.Id = versionId;
                },

                executeAsync: async () =>
                {
                    item = await _formTemplatesAppService.UpdateFormTemplateVersionAsync(model);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
