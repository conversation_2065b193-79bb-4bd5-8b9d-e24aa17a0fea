﻿// <copyright file="JurisdictionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Domain.Shared.Consts;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Jurisdictions
{
    /// <summary>
    /// Controller for Jurisdictions.
    /// </summary>
    [ApiController]
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[Controller]")]
    public class JurisdictionsController : TrustAPIControllerBase
    {
        private readonly ILogger<JurisdictionsController> _logger;
        private readonly IJurisdictionsAppService _jurisdictionsAppService;
        private readonly IJurisdictionTaxRatesAppService _jurisdictionTaxRatesAppService;
        private readonly IModulesAppService _modulesAppService;
        private readonly ISettingsAppService _settingsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionsController"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="jurisdictionsAppService">The service for jurisdictions.</param>
        /// <param name="jurisdictionTaxRatesAppService">The service for jurisdiction taxrates.</param>
        /// <param name="modulesAppService">The service for modules.</param>
        /// <param name="settingsAppService">The appservice for settings.</param>
        public JurisdictionsController(
            ILogger<JurisdictionsController> logger,
            IJurisdictionsAppService jurisdictionsAppService,
            IJurisdictionTaxRatesAppService jurisdictionTaxRatesAppService,
            IModulesAppService modulesAppService,
            ISettingsAppService settingsAppService)
            : base(logger)
        {
            _logger = logger;
            _jurisdictionsAppService = jurisdictionsAppService;
            _jurisdictionTaxRatesAppService = jurisdictionTaxRatesAppService;
            _modulesAppService = modulesAppService;
            _settingsAppService = settingsAppService;
        }

        /// <summary>
        /// Gets the jurisdictions with pagination.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/jurisdictions?pageNumber={pageNumber}&amp;pageSize={pageSize}.
        /// </remarks>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the jurisdictions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetJurisdictions", Summary = "Get jurisdictions with pagination.")]
        [ProducesResponseType(typeof(PaginatedResponse<JurisdictionDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetJurisdictions(
            int pageNumber = PagingSettings.DefaultPageNumber,
            int pageSize = PagingSettings.DefaultPageSize)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(pageNumber, pageSize),
                validate: () =>
                {
                    Check.Positive(pageNumber, nameof(pageNumber));
                    Check.Positive(pageSize, nameof(pageSize));
                },
                executeAsync: async (pagingInfo) =>
                {
                    return await _jurisdictionsAppService.GetJurisdictionsAsync(pageNumber, pageSize);
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of modules for the given jurisdiction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/jurisdictions/{jurisdictionid}/modules.
        ///
        /// </remarks>
        /// <param name="jurisdictionId">The id of the jurisdiction to get the modules for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the modules.</returns>
        [HttpGet("{jurisdictionId}/modules")]
        [SwaggerOperation(OperationId = "GetJurisdictionModules")]
        [ProducesResponseType(typeof(ListModulesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetJurisdictionModules(Guid jurisdictionId)
        {
            ListModulesDTO item = null;

            var result = await ProcessRequestAsync<ListModulesDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));
                },

                executeAsync: async () =>
                {
                    item = await _modulesAppService.GetJurisdictionModulesAsync(jurisdictionId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Sets the list of modules for the given jurisdiction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/v1/management/jurisdictions/{jurisdictionid}/modules.
        ///
        /// </remarks>
        /// <param name="jurisdictionId">The id of the jurisdiction to set the modules for.</param>
        /// <param name="setModulesDTO">The model with modules to set.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the modules.</returns>
        [HttpPut("{jurisdictionId}/modules")]
        [SwaggerOperation(OperationId = "SetJurisdictionModules")]
        [ProducesResponseType(typeof(ListModulesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> SetJurisdictionModules(
            Guid jurisdictionId,
            SetModulesDTO setModulesDTO)
        {
            ListModulesDTO item = null;

            var result = await ProcessRequestAsync<ListModulesDTO>(
                validate: () =>
                {
                    Check.NotNull(setModulesDTO, nameof(setModulesDTO));
                },

                executeAsync: async () =>
                {
                    item = await _modulesAppService.SetJurisdictionModulesAsync(jurisdictionId, setModulesDTO.Modules);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets tax rates for a specific jurisdiction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///      GET /api/v1/management/jurisdictions/{jurisdictionid}/tax-rates.
        /// </remarks>
        /// <param name="jurisdictionId">The ID of the jurisdiction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with tax rates for the jurisdiction.</returns>
        [HttpGet("{jurisdictionId}/tax-rates")]
        [SwaggerOperation(OperationId = "GetJurisdictionTaxRates")]
        [ProducesResponseType(typeof(PaginatedResponse<JurisdictionTaxRateDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetJurisdictionTaxRates(Guid jurisdictionId)
        {
            ListJurisdictionTaxRatesDTO item = null;

            var result = await ProcessRequestAsync<ListJurisdictionTaxRatesDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));
                },

                executeAsync: async () =>
                {
                    var data = await _jurisdictionTaxRatesAppService.GetTaxRatesByJurisdictionAsync(jurisdictionId, 1, int.MaxValue);
                    item = new ListJurisdictionTaxRatesDTO { TaxRates = data };
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets a set of settings.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/management/jurisdictions/{jurisdictionId}/settings.
        ///
        /// </remarks>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpGet("{jurisdictionId}/settings")]
        [SwaggerOperation(OperationId = "GetJurisdictionSettings")]
        [ProducesResponseType(typeof(SettingsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetJurisdictionSettings(
            Guid jurisdictionId)
        {
            SettingsDTO item = null;

            var result = await ProcessRequestAsync<SettingsDTO>(null,
                executeAsync: async () =>
                {
                    item = await _settingsAppService.ReadSettingsForJurisdictionAsync(jurisdictionId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Posts a set of settings for a specific purpose, identied by the type.
        /// </summary>
        /// <remarks>
        /// Valid values for 'key':
        ///   - documents
        ///   - str-late-payment-fees
        ///   - fees
        ///
        /// Sample request:
        ///
        ///     POST /api/management/jurisdictions/{jurisdictionid}/settings/{key}
        ///     {
        ///
        ///     }.
        /// </remarks>
        /// <param name="jurisdictionId">Id of the jurisdiction.</param>
        /// <param name="key">The type of settings to set.</param>
        /// <param name="data">The setting data. </param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpPost("{jurisdictionId}/settings/{key}")]
        [SwaggerOperation(OperationId = "SetJurisdictionSettingsByKey")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SetJurisdictionSettingsByKey(
            Guid jurisdictionId,
            string key,
            object data)
        {
            var result = await ProcessRequestAsync<object>(null,
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));
                    Check.NotNullOrEmpty(key, nameof(key));
                },
            executeAsync: async () =>
            {
                await _settingsAppService.SaveSettingsForJurisdictionAsync(jurisdictionId, key, data.ToString());
            });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Posts a set of settings.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/management/jurisdictions/{jurisdictionid}/settings
        ///     {
        ///
        ///     }.
        ///
        /// </remarks>
        /// <param name="jurisdictionId">Id of the juridiction.</param>
        /// <param name="settings">The settings.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the settings.</returns>
        [HttpPost("{jurisdictionId}/settings")]
        [SwaggerOperation(OperationId = "SetJurisdictionSettings")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> SetJurisdictionSettings(
            Guid jurisdictionId,
            SettingsDTO settings)
        {
            var result = await ProcessRequestAsync<object>(null,
                executeAsync: async () =>
                {
                    await _settingsAppService.SaveSettingsForJurisdictionAsync(jurisdictionId, settings);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Gets the list of form templates for the given jurisdiction.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/jurisdictions/{jurisdictionid}/form-templates.
        ///
        /// </remarks>
        /// <param name="jurisdictionId">The id of the jurisdiction to get the form templates for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the form templates.</returns>
        [HttpGet("{jurisdictionId}/form-templates")]
        [SwaggerOperation(OperationId = "GetJurisdictionFormTemplates")]
        [ProducesResponseType(typeof(ListFormTemplatesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetJurisdictionFormTemplates(Guid jurisdictionId)
        {
            ListFormTemplatesDTO item = null;

            var result = await ProcessRequestAsync<ListFormTemplatesDTO>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));
                },

                executeAsync: async () =>
                {
                    item = await _jurisdictionsAppService.GetFormTemplatesByJurisdictionAsync(jurisdictionId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
