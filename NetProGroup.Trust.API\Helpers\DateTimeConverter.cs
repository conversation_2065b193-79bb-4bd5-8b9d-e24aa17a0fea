﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.API.Helpers
{
    /// <summary>
    /// A custom JSON converter for <see cref="DateTime"/> that allows specifying a custom date-time format for serialization and deserialization.
    /// </summary>
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        private readonly string _format;

        /// <summary>
        /// Initializes a new instance of the <see cref="DateTimeConverter"/> class.
        /// </summary>
        /// <param name="format">The date-time format string to use for serialization and deserialization.</param>
        public DateTimeConverter(string format)
        {
            _format = format;
        }

        /// <summary>
        /// Reads and converts the JSON to a <see cref="DateTime"/> object.
        /// </summary>
        /// <param name="reader">The <see cref="Utf8JsonReader"/> to read from.</param>
        /// <param name="typeToConvert">The type to convert.</param>
        /// <param name="options">Options to control the conversion.</param>
        /// <returns>The deserialized <see cref="DateTime"/> object.</returns>
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
            => DateTime.Parse(reader.GetString());

        /// <summary>
        /// Writes a <see cref="DateTime"/> object as a JSON string using the specified format.
        /// </summary>
        /// <param name="writer">The <see cref="Utf8JsonWriter"/> to write to.</param>
        /// <param name="value">The <see cref="DateTime"/> value to write.</param>
        /// <param name="options">Options to control the conversion.</param>
        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
            => writer?.WriteStringValue(value.ToString(_format));
    }
}
