{"version": 1, "dependencies": {"net8.0": {"Microsoft.ApplicationInsights.AspNetCore": {"type": "Direct", "requested": "[2.23.0, )", "resolved": "2.23.0", "contentHash": "we/RsIn0Mwf/4ZNGXZixJ0lVD3pqjx2yVeKfqJybgYY/Lib8nnf+8YGJp+ULN3kOk39I0pI/7ZnF9LFy6hS3lw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.23.0", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.22", "Microsoft.Extensions.Configuration.Json": "3.1.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.23.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.EntityFrameworkCore.Design": {"type": "Direct", "requested": "[8.0.10, )", "resolved": "8.0.10", "contentHash": "uGNjfKvAsql2KHRqxlK5wHo8mMC60G/FecrFKEjJgeIxtUAbSXGOgKGw/gD9flO5Fzzt1C7uxfIcr6ZsMmFkeg==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.10", "Microsoft.Extensions.DependencyModel": "8.0.2", "Mono.TextTemplating": "2.2.1"}}, "Microsoft.EntityFrameworkCore.SqlServer": {"type": "Direct", "requested": "[8.0.10, )", "resolved": "8.0.10", "contentHash": "DvhBEk44UjWMebFKwIFDIdEsG8gzbgflWIZljDCpIkZVpId+PKs0ufzJxnTQ94InPO+pS7+wE45cRsPRt9B0Iw==", "dependencies": {"Microsoft.Data.SqlClient": "5.1.5", "Microsoft.EntityFrameworkCore.Relational": "8.0.10"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Direct", "requested": "[8.3.0, )", "resolved": "8.3.0", "contentHash": "kwHswQYvRbggxanPOdE99bkOCELQkgEjg/GNAGTBcrmAZff849DIEBG/utPVNbVDFNDmR+xi0ublSfofWfNTww==", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.3.0", "System.IdentityModel.Tokens.Jwt": "8.3.0"}}, "NetPro.StyleCop.Configuration.Package": {"type": "Direct", "requested": "[1.1.22, )", "resolved": "1.1.22", "contentHash": "qO/PlMrOAX0p2Ax0lD6oKB9fQTKUIvqOZICxUTQP5i3cCxNgPRIQ63bbZDqv91nxtpfbNYwW1n5+T9PUjLIpww=="}, "NetProGroup.Framework": {"type": "Direct", "requested": "[1.4.5, )", "resolved": "1.4.5", "contentHash": "9PhvXgIX1NI0zlVdkeTA1uAceJ6ONC4xH/toQJkdtGW9ToaeGynzADJMg+715Sn9ukrvgc+SBjKcCrqA9JHQ1w==", "dependencies": {"AutoMapper": "13.0.1", "AutoMapper.Extensions.ExpressionMapping": "7.0.0", "Azure.Identity": "1.12.0", "Azure.Storage.Blobs": "12.21.0", "EntityFramework": "6.4.4", "FastExcel": "3.0.13", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "6.0.21", "Microsoft.EntityFrameworkCore": "7.0.10", "Microsoft.EntityFrameworkCore.Relational": "7.0.10", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Graph": "5.56.0", "Microsoft.Identity.Web": "2.13.3", "Microsoft.Kiota.Abstractions": "1.9.9", "Microsoft.Kiota.Authentication.Azure": "1.9.9", "Microsoft.Kiota.Http.HttpClientLibrary": "1.9.9", "Microsoft.Kiota.Serialization.Form": "1.9.9", "Microsoft.Kiota.Serialization.Json": "1.9.9", "Microsoft.Kiota.Serialization.Text": "1.9.9", "NetCore.AutoRegisterDi": "2.1.0", "NetPro.StyleCop.Configuration.Package": "1.1.6", "Newtonsoft.Json": "13.0.3", "QRCoder": "1.4.1", "SendGrid": "9.29.3", "Serilog": "3.0.1", "Serilog.Extensions.Hosting": "7.0.0", "Serilog.Settings.Configuration": "7.0.1", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.File": "5.0.0", "SkiaSharp": "2.88.9", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "System.Data.SqlClient": "4.8.6", "System.IdentityModel.Tokens.Jwt": "8.0.0", "System.Linq.Dynamic.Core": "1.3.2", "Twilio": "6.14.1", "X.PagedList": "8.4.7"}}, "OneOf": {"type": "Direct", "requested": "[3.0.271, )", "resolved": "3.0.271", "contentHash": "pqpqeK8xQGggExhr4tesVgJkjdn+9HQAO0QgrYV2hFjE3y90okzk1kQMntMiUOGfV7FrCUfKPaVvPBD4IANqKg=="}, "OneOf.SourceGenerator": {"type": "Direct", "requested": "[3.0.271, )", "resolved": "3.0.271", "contentHash": "I7e5kx5prSV99g+rM/xO5KMhwBQL/b+NlUY76RhbutXJ0tgiuMoqgOFnlTHOxCLrlKmwaMzp3VI8oz05PSlcIA=="}, "StyleCop.Analyzers": {"type": "Direct", "requested": "[1.1.118, )", "resolved": "1.1.118", "contentHash": "Onx6ovGSqXSK07n/0eM3ZusiNdB6cIlJdabQhWGgJp3Vooy9AaLS/tigeybOJAobqbtggTamoWndz72JscZBvw=="}, "Swashbuckle.AspNetCore": {"type": "Direct", "requested": "[6.5.0, )", "resolved": "6.5.0", "contentHash": "FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Annotations": {"type": "Direct", "requested": "[6.5.0, )", "resolved": "6.5.0", "contentHash": "EcHd1z2pEdnpaBMTI9qjVxk6mFVGVMZ1n0ySC3fjrkXCQQ8O9fMdt9TxPJRKyjiTiTjvO9700jKjmyl+hPBinQ==", "dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "6.5.0"}}, "Asp.Versioning.Abstractions": {"type": "Transitive", "resolved": "8.1.0", "contentHash": "mpeNZyMdvrHztJwR1sXIUQ+3iioEU97YMBnFA9WLbsPOYhGwDJnqJMmEd8ny7kcmS9OjTHoEuX/bSXXY3brIFA==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Asp.Versioning.Http": {"type": "Transitive", "resolved": "8.1.0", "contentHash": "Xu4xF62Cu9JqYi/CTa2TiK5kyHoa4EluPynj/bPFWDmlTIPzuJQbBI5RgFYVRFHjFVvWMoA77acRaFu7i7Wzqg==", "dependencies": {"Asp.Versioning.Abstractions": "8.1.0"}}, "Asp.Versioning.Mvc": {"type": "Transitive", "resolved": "8.1.0", "contentHash": "BMAJM2sGsTUw5FQ9upKQt6GFoldWksePgGpYjl56WSRvIuE3UxKZh0gAL+wDTIfLshUZm97VCVxlOGyrcjWz9Q==", "dependencies": {"Asp.Versioning.Http": "8.1.0"}}, "Asp.Versioning.Mvc.ApiExplorer": {"type": "Transitive", "resolved": "8.1.0", "contentHash": "a90gW/4TF/14Bjiwg9LqNtdKGC4G3gu02+uynq3bCISfQm48km5chny4Yg5J4hixQPJUwwJJ9Do1G+jM8L9h3g==", "dependencies": {"Asp.Versioning.Mvc": "8.1.0"}}, "AutoBogus": {"type": "Transitive", "resolved": "2.13.1", "contentHash": "9bvwy/Y53M/AWXzYaClv0jU9Xg/Ycyth3cQuUFWZRw5Dv4UsHV00iSrDplcheG6l5mL5SI/eif7X2INhuBvjQw==", "dependencies": {"Bogus": "31.0.3", "System.Data.DataSetExtensions": "4.5.0"}}, "AutoMapper": {"type": "Transitive", "resolved": "13.0.1", "contentHash": "/Fx1SbJ16qS7dU4i604Sle+U9VLX+WSNVJggk6MupKVkYvvBm4XqYaeFuf67diHefHKHs50uQIS2YEDFhPCakQ==", "dependencies": {"Microsoft.Extensions.Options": "6.0.0"}}, "AutoMapper.Extensions.ExpressionMapping": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "78Aq0Wh+34ET7qHAzwzQ72ECNeddvEVUpahAHYyMPuLy1j/TLXliVdjyy+ml2HPcleLnehSsddJCtUy8IB20NQ==", "dependencies": {"AutoMapper": "[13.0.0, 14.0.0)"}}, "AWSSDK.Core": {"type": "Transitive", "resolved": "3.7.100.14", "contentHash": "gnEgxBlk4PFEfdPE8Lkf4+D16MZFYSaW7/o6Wwe5e035QWUkTJX0Dn4LfTCdV5QSEL/fOFxu+yCAm55eIIBgog=="}, "AWSSDK.SecurityToken": {"type": "Transitive", "resolved": "3.7.100.14", "contentHash": "dGCVuVo0CFUKWW85W8YENO+aREf8sCBDjvGbnNvxJuNW4Ss+brEU9ltHhq2KfZze2VUNK1/wygbPG1bmbpyXEw==", "dependencies": {"AWSSDK.Core": "[3.7.100.14, 4.0.0)"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.41.0", "contentHash": "7OO8rPCVSvXj2IQET3NkRf8hU2ZDCCvCIUhlrE089qkLNpNfWufJnBwHRKLAOWF3bhKBGJS/9hPBgjJ8kupUIg==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Identity": {"type": "Transitive", "resolved": "1.12.0", "contentHash": "OBIM3aPz8n9oEO5fdnee+Vsc5Nl4W3FeslPpESyDiyByntQI5BAa76KD60eFXm9ulevnwxGZP9YXL8Y+paI5Uw==", "dependencies": {"Azure.Core": "1.40.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Security.KeyVault.Certificates": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "I+irkjO1JBzJWYBLhW835/b7GllxkjMbQwrirhxUJsf6FQnH+eIGin4T/jBLgyuu1zPsn2AxiUFju6Shb/uiNA==", "dependencies": {"Azure.Core": "1.0.2", "System.Memory": "4.5.3", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}}, "Azure.Security.KeyVault.Secrets": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "SK4XTPEaXI5UTdtSkr8TN4ZKnvazNHhybnnkxHgOyRAhV9ObcIjbrNlhS4ZeR8XtI+HidL+v/WIIVfR1+jKB8Q==", "dependencies": {"Azure.Core": "1.0.2", "System.Memory": "4.5.3", "System.Text.Json": "4.6.0", "System.Threading.Tasks.Extensions": "4.5.2"}}, "Azure.Storage.Blobs": {"type": "Transitive", "resolved": "12.21.0", "contentHash": "W1aSEH11crU3CscfuICUPXScTO9nKwSof3YFsdxmbdi+P+JARYzntkGJuZ685gvmyUse7isBNncNlVEjB5LT0g==", "dependencies": {"Azure.Storage.Common": "12.20.0", "System.Text.Json": "4.7.2"}}, "Azure.Storage.Common": {"type": "Transitive", "resolved": "12.20.0", "contentHash": "C0uTY4E1NSGiNf/dlLMQ/d85a2CDazEg4YYtNJOYnLSb8ZXJ5RBPHYGW7a46kN5Xn5Bc9BKMvs8fME285TfEpw==", "dependencies": {"Azure.Core": "1.41.0", "System.IO.Hashing": "6.0.0"}}, "Bogus": {"type": "Transitive", "resolved": "31.0.3", "contentHash": "CMVbZndfEihsd7A5GgdfMQTNiUzjTTng2IPoTe4J2cyxUaEqCW5M5HO8uGF/Qkq1NL219AbLHX+4dx9MxWDSiA=="}, "ClosedXML": {"type": "Transitive", "resolved": "0.104.1", "contentHash": "RVm2fUNWJlBJlg07shrfeWzrHPG5ypI/vARqdUOUbUdaog8yBw8l4IbCHf2MXt0AXtzaZqGNqhFaCAHigCBdfw==", "dependencies": {"ClosedXML.Parser": "[1.2.0, 2.0.0)", "DocumentFormat.OpenXml": "[3.0.1, 4.0.0)", "ExcelNumberFormat": "1.1.0", "RBush": "3.2.0", "SixLabors.Fonts": "1.0.0", "System.IO.Packaging": "8.0.0"}}, "ClosedXML.Parser": {"type": "Transitive", "resolved": "1.2.0", "contentHash": "w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw=="}, "DnsClient": {"type": "Transitive", "resolved": "1.6.1", "contentHash": "4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}}, "DocumentFormat.OpenXml": {"type": "Transitive", "resolved": "3.0.1", "contentHash": "DCK1cwFUJ1FGGyYyo++HWl9H1RkqMWIu+FGOLRy6E4L4y0/HIhlJ7N/n1HKboFfOwKn1cMBRxt1RCuDbIEy5YQ==", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.0.1"}}, "DocumentFormat.OpenXml.Framework": {"type": "Transitive", "resolved": "3.0.1", "contentHash": "ifyI7OW7sggz7LQMIAD2aUsY/zVUON9QaHrpZ4MK33iVMeHlTG4uhUE2aLWb31nry+LCs2ALDAwf8OfUJGjgBg==", "dependencies": {"System.IO.Packaging": "8.0.0"}}, "EFCore.BulkExtensions": {"type": "Transitive", "resolved": "8.1.1", "contentHash": "igRcnfb9tgZqzGM8Pw3KTuWcE0WiGS1C7HSFedHSY+UuSzKT3UYJUPh5e10PW3tXPO0ZtDzNF0MlPUIAC7sybQ==", "dependencies": {"EFCore.BulkExtensions.MySql": "8.1.1", "EFCore.BulkExtensions.PostgreSql": "8.1.1", "EFCore.BulkExtensions.SqlServer": "8.1.1", "EFCore.BulkExtensions.Sqlite": "8.1.1"}}, "EFCore.BulkExtensions.Core": {"type": "Transitive", "resolved": "8.1.1", "contentHash": "1fFI65GRCBKftMkm9/zhYHBgJQB8n83rPuNdKvGVUQcPO61sa6BRJocGRzd9Kyyf6yTNaWvM1tTrH1mDtoQbDg==", "dependencies": {"MedallionTopologicalSort": "1.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "NetTopologySuite": "2.5.0"}}, "EFCore.BulkExtensions.MySql": {"type": "Transitive", "resolved": "8.1.1", "contentHash": "n3Jucix2V72JIDAOJcnWqyBxv4ed+DyHS2f4jrx4USf3jlmYb5l5SKaJtdd1RkFksJAPHWAo3C0N7lf6KwYvrQ==", "dependencies": {"EFCore.BulkExtensions.Core": "8.1.1", "Pomelo.EntityFrameworkCore.MySql": "8.0.2"}}, "EFCore.BulkExtensions.PostgreSql": {"type": "Transitive", "resolved": "8.1.1", "contentHash": "sDc3daXb7ThPotrn4RzoitIzJfcvz9acU3Vitpe2N1qYVFODYzIJJ3X61YocWwIujOWyjx0/lWmGTsdE0ljZLA==", "dependencies": {"EFCore.BulkExtensions.Core": "8.1.1", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.4"}}, "EFCore.BulkExtensions.Sqlite": {"type": "Transitive", "resolved": "8.1.1", "contentHash": "acPpdRte2u2eu1XExyczQrgxCG9K74HhNZtIDg9PpmSlIJmNcZ85Xj/A2nxjJxpIW04n1i+qljq+gvZHlqNPSA==", "dependencies": {"EFCore.BulkExtensions.Core": "8.1.1", "Microsoft.EntityFrameworkCore.Sqlite.Core": "8.0.8", "NetTopologySuite.IO.SpatiaLite": "2.0.0"}}, "EFCore.BulkExtensions.SqlServer": {"type": "Transitive", "resolved": "8.1.1", "contentHash": "A0POenqUwYS8YDYweXqBtr/w4zB4B/AM6sBgS2Rpqh+N16CKyurhIT9YxsFgAikWA5i2MFsA+3scc8R+70bs4w==", "dependencies": {"EFCore.BulkExtensions.Core": "8.1.1", "Microsoft.Data.SqlClient": "5.2.2", "Microsoft.EntityFrameworkCore.SqlServer.HierarchyId": "8.0.8", "NetTopologySuite.IO.SqlServerBytes": "2.1.0"}}, "EntityFramework": {"type": "Transitive", "resolved": "6.4.4", "contentHash": "yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "dependencies": {"Microsoft.CSharp": "4.7.0", "System.CodeDom": "4.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.SqlClient": "4.8.1"}}, "ExcelNumberFormat": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA=="}, "FastExcel": {"type": "Transitive", "resolved": "3.0.13", "contentHash": "v0gF9hMHBiAucW4xWXbB/KGQurTTbSCSQY2Y9C9oVNlR+9QCEyWAXhHtG+LhpLZPq+BML8IXYqP0Mkdpzj9TKQ=="}, "Hangfire.Core": {"type": "Transitive", "resolved": "1.8.15", "contentHash": "+w8gT6CFH4jicVEsJ8WlMRJMNV2MG52JNtvKoXPFHFs6nkDTND6iDeCjydyHgp+85lZPRXc+s9/vkxD2vbPrLg==", "dependencies": {"Newtonsoft.Json": "11.0.1"}}, "Hangfire.InMemory": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "56H71lfcqn5sN/8Bjj9hOLGTG5HIERLRuMsRJTFpw0Tsq5ck5OUkNvtUw92s7bwD3PRKOo4PkDGqNs9KugaqoQ==", "dependencies": {"Hangfire.Core": "1.8.0"}}, "Hangfire.NetCore": {"type": "Transitive", "resolved": "1.8.15", "contentHash": "HNACpklY1FGcsCr/xlPvmh5R5JqH2eEBxOp63Dwph6H6LdGWWqHoMpxjxkpYkZXM2mNpmk+j0Dk8lizadfnD+A==", "dependencies": {"Hangfire.Core": "[1.8.15]", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.0.0", "Microsoft.Extensions.Hosting.Abstractions": "3.0.0", "Microsoft.Extensions.Logging.Abstractions": "3.0.0"}}, "Humanizer.Core": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw=="}, "JetBrains.Annotations": {"type": "Transitive", "resolved": "2022.3.1", "contentHash": "11nsS3+lFICGkztVs9PP2NRItxf9fF+qD6xEW/T0YGto52zj07wseUeBdMAU1ah9HNVTDZyRC1u4NWdtJScwhw=="}, "MedallionTopologicalSort": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "dcAqM8TcyZQ/T466CvqNMUUn/G0FQE+4R7l62ngXH7hLFP9yA7yoP/ySsLgiXx3pGUQC3J+cUvXmJOOR/eC+oQ=="}, "Microsoft.ApplicationInsights": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "nWArUZTdU7iqZLycLKWe0TDms48KKGE6pONH2terYNa8REXiqixrMOkf1sk5DHGMaUTqONU2YkS4SAXBhLStgw==", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.DependencyCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "9YRdl9SNbTxd4AafJckyoJLr5gJdnvqFivjo+PY0lQTPEncPB+z3ZABG4iDfxN9HI1aLqyRINr1/7de9Wg8ZuQ==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.EventCounterCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "gGt0JPw2dcSeIAIefyORJBdeMz8KgAFIktu8HV/NwkiGmLyw+YtifLm6B5gvGxO15AeMsGPbmvWEIvLfq88XPw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0"}}, "Microsoft.ApplicationInsights.PerfCounterCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "q9ApjZfBS9O8m3aQM2oVjsGBmlE8BCFywT7UR+8aqdNuz7HpoIxw4jHy0XOBergiFX/olrJF4OyPkGxc3H5JHg==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "2B8CGfnB/tribkQAqRBhMvJYJK5TkEPMG/BB0QrlxdwVGEufayNLMveXjkQCqld9arXd6wKR1ve2XmkA0+xXKQ==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.23.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "798Dudr4tkujslk1w+XcXOcCErmVsk+nhp+QCHLa3lcgi25vkAxBmzPUeQlRJVCNL/1f4x/YF+vQZ8RSuTXWCw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "System.IO.FileSystem.AccessControl": "4.7.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"type": "Transitive", "resolved": "7.0.1", "contentHash": "2pZZ1GQExpBV8xdRoS1RbopSXBnZBN6INA4KBb8utk6BDLzqHzfF7PCVPRS9YuPx/UHVOU8SJiPjWpcxTsvghw==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.15.1"}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect": {"type": "Transitive", "resolved": "7.0.1", "contentHash": "HTEPkaslzcrlMnqwc08yoU0TU5lNmgWVWgRsPJomvMbKzNozf3acsvA2hAeFVDHoJNBZmJm7Jhy3+pHmbzK0uQ==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.15.1"}}, "Microsoft.AspNetCore.Cryptography.Internal": {"type": "Transitive", "resolved": "7.0.10", "contentHash": "AhNj547biaNSjp3z0DY4um1ILtadaiwYuzR1oShNMfkaRQ8cy8VVIbM458GvQi0TYg8AdP6g80bSLBMVbZmv3g=="}, "Microsoft.AspNetCore.Cryptography.KeyDerivation": {"type": "Transitive", "resolved": "6.0.21", "contentHash": "PhqdH0xiFqUqXe30Z1FI/YaD+c4lZFIQRcW3oBb9pnANmAVbXw+vz8f6BIvdwU/HIDUEaaB47TxUsfiXPhihMw==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "6.0.21"}}, "Microsoft.AspNetCore.DataProtection": {"type": "Transitive", "resolved": "7.0.10", "contentHash": "aeY3GFpCwE194Qe+LtUdG+Iqs44VPzrPsIOl5Ua1Si4rByPT99rLOgnldvLV5CgrBzz8W1gP3H16kYcvxmB7ZQ==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "7.0.10", "Microsoft.AspNetCore.DataProtection.Abstractions": "7.0.10", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1", "System.Security.Cryptography.Xml": "7.0.1"}}, "Microsoft.AspNetCore.DataProtection.Abstractions": {"type": "Transitive", "resolved": "7.0.10", "contentHash": "TpHg6+CJ/WcXQyMajzPeKFP9sIN4GNYTHQgMZjF6Re1hRZYtuD+C+RR7rT/5psHF4obffhsktIstIMxSuo+HAQ=="}, "Microsoft.AspNetCore.Hosting": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "MqYc0DUxrhAPnb5b4HFspxsoJT+gJlLsliSxIgovf4BsbmpaXQId0/pDiVzLuEbmks2w1/lRfY8w0lQOuK1jQQ==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "76cKcp2pWhvdV2TXTqMg/DyW7N6cDzTEhtL8vVWFShQN+Ylwv3eO/vUQr2BS3Hz4IZHEpL+FOo2T+MtymHDqDQ==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "+vD7HJYzAXNq17t+NgRkpS38cxuAyOBu8ixruOiA3nWsybozolUdALWiZ5QFtGRzajSLPFA2YsbO3NPcqoUwcw==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1"}}, "Microsoft.AspNetCore.Http": {"type": "Transitive", "resolved": "2.1.22", "contentHash": "+Blk++1JWqghbl8+3azQmKhiNZA5wAepL9dY2I6KVmu2Ri07MAcvAVC888qUvO7yd7xgRgZOMfihezKg14O/2A==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "kQUEVOU4loc8CPSb2WoHFTESqwIa8Ik7ysCBfTwzHAd0moWovc9JQLmhDIHlYLjHbyexqZAlkq/FPRUZqokebw==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "ncAgV+cqsWSqjLXFUTyObGh4Tr7ShYYs3uW8Q/YpRwZn7eLV7dux5Z6GLY+rsdzmIHiia3Q2NWbLULQi7aziHw==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.1.1", "Microsoft.Extensions.FileProviders.Abstractions": "2.1.1", "Microsoft.Net.Http.Headers": "2.1.1", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "VklZ7hWgSvHBcDtwYYkdMdI/adlf7ebxTZ9kdzAhX+gUs5jSHE9mZlTamdgf9miSsxc1QjNazHXTDJdVPZKKTw==", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.1"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"type": "Transitive", "resolved": "6.0.21", "contentHash": "rIqPnAf8L15cveZy5TRcrZWk0gOUWxd73F7hbaTjKR2XwUAGIBIrLZCEw0COfshqyf9vU6/MmAtGEtEDqcLkXw==", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.21", "Microsoft.Extensions.Identity.Stores": "6.0.21"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "PGKIZt4+412Z/XPoSjvYu/QIbTxcAQuEFNoA1Pw8a9mgmO0ZhNBmfaNyhgXFf7Rq62kP0tT/2WXpxdcQhkFUPA==", "dependencies": {"Microsoft.Net.Http.Headers": "2.1.1", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg=="}, "Microsoft.CodeAnalysis.Analyzers": {"type": "Transitive", "resolved": "3.3.3", "contentHash": "j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ=="}, "Microsoft.CodeAnalysis.Common": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}}, "Microsoft.CodeAnalysis.CSharp": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.5.0]"}}, "Microsoft.CodeAnalysis.CSharp.Workspaces": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.5.0]", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.5.0]"}}, "Microsoft.CodeAnalysis.Workspaces.Common": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.Data.SqlClient": {"type": "Transitive", "resolved": "5.2.2", "contentHash": "mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}}, "Microsoft.Data.SqlClient.SNI.runtime": {"type": "Transitive", "resolved": "5.2.0", "contentHash": "po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w=="}, "Microsoft.Data.Sqlite.Core": {"type": "Transitive", "resolved": "8.0.8", "contentHash": "qHInO2EvOcPhjgboP0TGnXM7rASdvWXrw6jAH8Yuz5YP82VTje7d/NKiX1i+dVbE3+G3JuW1kqNVB8yLvsqgYA==", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}}, "Microsoft.EntityFrameworkCore": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "HNn+NPKCm7rR7ij7IRCCbuImaMulFJGloyIbMwi3Ews77RsthM8gxpTZciFLgRYPsBtszKpdIClEwnWmP0vjUg==", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.14", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.14", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.1"}}, "Microsoft.EntityFrameworkCore.Abstractions": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "Om8/jdWyx9eKFkA1YEgxk13KjGIzA8teLgG7iNFunsI2+MT6UT54Eb4t6oe4NQlIaACj5voUe6szVAQe9GKwDA=="}, "Microsoft.EntityFrameworkCore.Analyzers": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "lzNb3s4t5JDMHGoUFuX/f977dFythvmzGFJxvjlhExdiATPKQfquo2NM0uX8Kelfq04jRljpdbRzcsSsK1q9Tw=="}, "Microsoft.EntityFrameworkCore.Relational": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "cPEeIk9nFO3+hxj9tp5AvTFdcTZkVPJCOFUiagbf37KhPGtiG0ZWpl15xOzLYTDAYjF5kxH/jcuDYGlLACJEmA==", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.14", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core": {"type": "Transitive", "resolved": "8.0.8", "contentHash": "w5k/ENj3+BPbmggqh83RRuPhhKcJmW7CmdJuGwdX1eFrmptJwnzKiHfQCPkJAu9df16PSs5YFeWrDgepfqnltA==", "dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.8", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "Microsoft.Extensions.DependencyModel": "8.0.1"}}, "Microsoft.EntityFrameworkCore.SqlServer.Abstractions": {"type": "Transitive", "resolved": "8.0.8", "contentHash": "Sehx7A2hZUopVlXKY6YoAGijn5KydyLLbqAcnDczRodVFJKa4Is/cwqvv5pCP7V659u2gS18TNctzgqNGfosvw==", "dependencies": {"Microsoft.SqlServer.Types": "160.1000.6", "System.Text.Json": "8.0.4"}}, "Microsoft.EntityFrameworkCore.SqlServer.HierarchyId": {"type": "Transitive", "resolved": "8.0.8", "contentHash": "q7BrgSSzqdYUTGsQWrpDq1f09JDATd0iNo0khWhmENukH9wUUV7pml6VhDQPQmMnuks/7qEGOHLWOKfeY1NiNg==", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": "8.0.8", "Microsoft.EntityFrameworkCore.SqlServer.Abstractions": "8.0.8"}}, "Microsoft.Extensions.ApiDescription.Server": {"type": "Transitive", "resolved": "6.0.5", "contentHash": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw=="}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "Lu41BWNmwhKr6LgyQvcYBOge0pPvmiaK8R5UHXX4//wBhonJyWcT2OK1mqYfEM5G7pTf31fPrpIHOT6sN7EGOA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "tgU4u7bZsoS9MKVRiotVMAwHtbREHr5/5zSEV+JPhg46+ox47Au84E3D2IacAaB0bk5ePNaNieTlPrfjbbRJkg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "6xMxFIfKL+7J/jwlk8zV8I61sF3+DRG19iKQxnSfYQU+iMMjGbcWNCHFF/3MHf3o4sTZPZ8D6Io+GwKFc3TIZA==", "dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "OjRJIkVxUFiVkr9a39AqVThft9QHoef4But5pDCydJOXJ4D/SkmzuW1tm6J2IXynxj6qfeAz9QTnzQAvOcGvzg==", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.FileProviders.Physical": "3.1.0"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "gBpBE1GoaCf1PKYC7u0Bd4mVZ/eR2bnOvn7u8GBXEy3JGar6sC3UVpVfTB9w+biLPtzcukZynBG9uchSBbLTNQ==", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg=="}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "mUBDZZRgZrSyFOsJ2qJJ9fXfqd/kXJwf3AiDoqLD9m6TjY5OO/vLNOb9fb4juC0487eq4hcGN/M2Rh/CKS7QYw=="}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "elH2vmwNmsXuKmUeMQ4YW9ldXiF+gSGDgg1vORksob5POnpaI6caj1Hu8zaYbEuibhqCoWg0YRWDazBY3zjBfg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "obv82U5+okAtAP8K2Ne027Y8rfvseUPUNZUMVUffRB+Unom8mjzvqL/GzUx7rPj6f9e/hQbGwF5ya5RZq7327Q==", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "8.0.14", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "se5sdveMiA3PUOWchOZXY/sGA50MrJ/Mg/G6CdQBtyA4MLySNRilVCi23YT90RAwqvI2uQEk5+buxYdpAfuwpA=="}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"type": "Transitive", "resolved": "8.0.14", "contentHash": "4b/wu7E9oNd994GQyehsJkoLAC8BVrRkO6rzWuWTmHm0w0A5m4giPx35BWd7nJ5h0mq2Cfk0ueHlBQo/ICyfJA==", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.14", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.14", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "8.0.14"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "KsvgrYp2fhNXoD9gqSu8jPK9Sbvaa7SqNtsLqHugJkCwFmgRvdz76z6Jz2tlFlC7wyMTZxwwtRF8WAorRQWTEA==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.0", "Microsoft.Extensions.FileSystemGlobbing": "3.1.0"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "tK5HZOmVv0kUYkonMjuSsxR0CBk+Rd/69QU3eOMv9FvODGZ2d0SR+7R+n8XIgBcCCoCHJBSsI4GPRaoN3Le4rA=="}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "nHwq9aPBdBPYXPti6wYEEfgXddfBrYC+CQLn+qISiwQq5tpfaqDZSKOJNxoe9rfQxGf1c+2wC/qWFe1QYJPYqw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "3.1.3", "contentHash": "QV5ODk9CFm+//AsZQSm8sH33Li1a19PTy44Ln+FCd7YnNRcxxXgZGNCdQtJpKFBpaLjfdfaGUfqWWq3LID1GdA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.3", "Microsoft.Extensions.Logging": "3.1.3", "Microsoft.Extensions.Options": "3.1.3"}}, "Microsoft.Extensions.Identity.Core": {"type": "Transitive", "resolved": "6.0.21", "contentHash": "yYbRf/iLYM8jwjLJtbX3iDsHjSB+6tr/tmnSkjM/ZUUIbv/76OUD8+RNcxB0r3LobG/+rZqXNwZTAvtDWeg7Jg==", "dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "6.0.21", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Identity.Stores": {"type": "Transitive", "resolved": "6.0.21", "contentHash": "pkEwazi7GhaGF1+MNdQ27wGXNyZh/r/ssMZT40yp+coua4bQz2xGevoiXYuLunayt+tKBpbpVFKnAcIlX+1IiQ==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Identity.Core": "6.0.21", "Microsoft.Extensions.Logging": "6.0.0"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "4x+pzsQEbqxhNf1QYRr5TDkLP9UsLT3A6MdRKDDEgrW7h1ljiEPgTNhKYUhNCCAaVpQECVQ+onA91PTPnIp6Lw==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "8.0.3", "contentHash": "dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Logging.ApplicationInsights": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "JLEabPz445i1yRB0hKZVzJJE35QatRIzWlrMOiBQXr9kBJod0jkpkrBf94ln6kXu+jlEGohnXtuXacPPhybJDw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Extensions.Logging": "2.1.1"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "SErON45qh4ogDp6lr6UvVmFYW0FERihW+IQ+2JyFv1PUyWktcJytFaWH5zarufJvZwhci7Rf1IyGXr9pVEadTw=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "95UnxZkkFdXxF6vSrtJsMHCzkDeSMuUWGs2hDT54cX+U5eVajrCJ3qLyQRW+CtpTt5OJ8bmTvpQVHu1DLhH+cA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.Graph": {"type": "Transitive", "resolved": "5.56.0", "contentHash": "HUB0OLHvtohA2+ngnrGfXnwvMWos/3yVibQDoB9HSSgl7kH6bZ1GBElecQsg1nKWX9E0emxhC6DvznzCFneOMw==", "dependencies": {"Microsoft.Graph.Core": "3.1.12"}}, "Microsoft.Graph.Core": {"type": "Transitive", "resolved": "3.1.12", "contentHash": "J5o9fwDefrJHwfbp7aI3fs83EC0vFfwJnLaVl8sbylBREXorgK3MvLmA/a6QqUoE7P1Veq7xhHpi5zCBbKc2Bw==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.6.0", "Microsoft.Kiota.Abstractions": "[1.9.1, 2.0.0)", "Microsoft.Kiota.Authentication.Azure": "[1.1.7, 2.0.0)", "Microsoft.Kiota.Http.HttpClientLibrary": "[1.4.3, 2.0.0)", "Microsoft.Kiota.Serialization.Form": "[1.2.4, 2.0.0)", "Microsoft.Kiota.Serialization.Json": "[1.3.3, 2.0.0)", "Microsoft.Kiota.Serialization.Multipart": "[1.1.5, 2.0.0)", "Microsoft.Kiota.Serialization.Text": "[1.2.2, 2.0.0)"}}, "Microsoft.Identity.Abstractions": {"type": "Transitive", "resolved": "4.0.0", "contentHash": "hw2Ur9h1/1rX3ZYOyKWwmkO3XLVRl+pm/IFFySHQwyfdAWxAs5J42H/avS2nkZ0AZgjwCIon5Ib+mzdJ6bhKYg=="}, "Microsoft.Identity.Client": {"type": "Transitive", "resolved": "4.61.3", "contentHash": "naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.61.3", "contentHash": "PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.Identity.Web": {"type": "Transitive", "resolved": "2.13.3", "contentHash": "dtWH55j9sGasNKhdG9awtJAa7KUqW2MhjR35K8ssdfaIRidZaq5gDavQFK+br9pa2FUvFjsbN5IMcTOVIHBbeQ==", "dependencies": {"Microsoft.Extensions.Http": "3.1.3", "Microsoft.Identity.Web.Certificate": "2.13.3", "Microsoft.Identity.Web.Certificateless": "2.13.3", "Microsoft.Identity.Web.TokenAcquisition": "2.13.3", "Microsoft.Identity.Web.TokenCache": "2.13.3", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.32.0", "Microsoft.IdentityModel.Validators": "6.32.0", "System.Drawing.Common": "4.7.2", "System.IdentityModel.Tokens.Jwt": "6.32.0", "System.Text.Encodings.Web": "7.0.0"}}, "Microsoft.Identity.Web.Certificate": {"type": "Transitive", "resolved": "2.13.3", "contentHash": "+a70TtgqDLxFH+lT3e1K+liHmHOPZRsbWVcQM3CyX4PQSqyny5yB4ahSltC4Ljrsn6jwsigqxf207L65S4spHw==", "dependencies": {"Azure.Identity": "1.3.0", "Azure.Security.KeyVault.Certificates": "4.1.0", "Azure.Security.KeyVault.Secrets": "4.1.0", "Microsoft.Identity.Abstractions": "4.0.0", "Microsoft.Identity.Web.Certificateless": "2.13.3", "Microsoft.Identity.Web.Diagnostics": "2.13.3", "System.Text.Encodings.Web": "7.0.0"}}, "Microsoft.Identity.Web.Certificateless": {"type": "Transitive", "resolved": "2.13.3", "contentHash": "MtRd82q+tG5EgRHRyPtxLec+SOTmvL+NbOmq+b62iOTVr5W4AZy/gzObTL3TAFJb9gdCEmq83Z8032UQ6cGj6A==", "dependencies": {"Azure.Identity": "1.3.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.IdentityModel.JsonWebTokens": "6.32.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Identity.Web.Diagnostics": {"type": "Transitive", "resolved": "2.13.3", "contentHash": "1S3pngCEmzWjURvUnjPlIPzTxM7eqvtncb7EjzB67+J2Njxg7r/iSCRJDOFrt5T/r+0s1gG8O5w9HLwSTMsOtA=="}, "Microsoft.Identity.Web.TokenAcquisition": {"type": "Transitive", "resolved": "2.13.3", "contentHash": "U57PBVE9+2aw2AuUKqJ+90PM+MdUR6dveq9gGok9AuY5DpuUOeQ+BUwJMb0uGY0mFQFqSZ5ZfMUsFE+di3fBZQ==", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "7.0.1", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "7.0.1", "Microsoft.Extensions.Http": "3.1.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "7.0.0", "Microsoft.Identity.Abstractions": "4.0.0", "Microsoft.Identity.Web.Certificate": "2.13.3", "Microsoft.Identity.Web.Certificateless": "2.13.3", "Microsoft.Identity.Web.TokenCache": "2.13.3", "Microsoft.IdentityModel.Logging": "6.32.0", "Microsoft.IdentityModel.LoggingExtensions": "6.32.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.32.0", "System.IdentityModel.Tokens.Jwt": "6.32.0", "System.Text.Encodings.Web": "7.0.0"}}, "Microsoft.Identity.Web.TokenCache": {"type": "Transitive", "resolved": "2.13.3", "contentHash": "KwzfNus23ZGwhHcutM7EOZNcapu5EeR50wUic/sXjDOg+2twa8/4iLIE4RdTBKcPuUmeOq2o7RBfAMEcOR0HRA==", "dependencies": {"Microsoft.AspNetCore.DataProtection": "7.0.10", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Identity.Client": "4.55.0", "Microsoft.Identity.Web.Diagnostics": "2.13.3", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "7.0.2", "System.Security.Cryptography.Xml": "7.0.1", "System.Text.Encodings.Web": "7.0.0"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.3.0", "contentHash": "jNin7yvWZu+K3U24q+6kD+LmGSRfbkHl9Px8hN1XrGwq6ZHgKGi/zuTm5m08G27fwqKfVXIWuIcUeq4Y1VQUOg=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "8.3.0", "contentHash": "4SVXLT8sDG7CrHiszEBrsDYi+aDW0W9d+fuWUGdZPBdan56aM6fGXJDjbI0TVGEDjJhXbACQd8F/BnC7a+m2RQ==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.3.0"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.3.0", "contentHash": "4w4pSIGHhCCLTHqtVNR2Cc/zbDIUWIBHTZCu/9ZHm2SVwrXY3RJMcZ7EFGiKqmKZMQZJzA0bpwCZ6R8Yb7i5VQ==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.3.0"}}, "Microsoft.IdentityModel.LoggingExtensions": {"type": "Transitive", "resolved": "6.32.0", "contentHash": "HdArLDsUd9ddNFKo7FA2K5BhYvT8s2ibIJxAq9V3REz88LITSOVzvFA/QOgpH9al59QfXteQG+Gsyil2gep/XA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.IdentityModel.Abstractions": "6.32.0"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "8.3.0", "contentHash": "NNJajNK9cgH61BVY1T/yaHROjCn+KKGQHPk5gxzMBiSit6PW1teiqPBBfSO/y6hVkGMOVKc5hNJfqJsn+5jHjQ==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.3.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.3.0", "contentHash": "yGzqmk+kInH50zeSEH/L1/J0G4/yqTQNq4YmdzOhpE7s/86tz37NS2YbbY2ievbyGjmeBI1mq26QH+yBR6AK3Q==", "dependencies": {"Microsoft.IdentityModel.Logging": "8.3.0"}}, "Microsoft.IdentityModel.Validators": {"type": "Transitive", "resolved": "6.32.0", "contentHash": "MSKEMxjgeUZbuUQfT0+V3pS3sInBHe/X6mGTQVE7YYd4R0OkCSjRQ2FBIWPfmw1Fng859gkH16QtoCYCuwyXow==", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.32.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.32.0", "Microsoft.IdentityModel.Tokens": "6.32.0", "System.IdentityModel.Tokens.Jwt": "6.32.0"}}, "Microsoft.Kiota.Abstractions": {"type": "Transitive", "resolved": "1.9.9", "contentHash": "KZpiJ5LkBDW5pSdMe0vfx6h501QH4tKdbHfo2e802rcJZTpgJkyuzja+lbx42wHXDUSpJ5QS3HThNuRVuKsSWA==", "dependencies": {"Std.UriTemplate": "1.0.3"}}, "Microsoft.Kiota.Authentication.Azure": {"type": "Transitive", "resolved": "1.9.9", "contentHash": "oGQ5NtGFzst3dwAXzt0VNdd53HaLR3s0kI7FeDu3zjjivmcrisl1ERapqi6Ti9Ao6FAv/cgW3aRSTxh9652mnQ==", "dependencies": {"Azure.Core": "1.41.0", "Microsoft.Kiota.Abstractions": "1.9.9"}}, "Microsoft.Kiota.Http.HttpClientLibrary": {"type": "Transitive", "resolved": "1.9.9", "contentHash": "99g0my0v283eMR/BOEZFmjlk6yQx+m0HTiKcdPUArG7sciDWxU7WgUQ3VVfjodwqs7LnBauhctTw9cXzy/Y88w==", "dependencies": {"Microsoft.Kiota.Abstractions": "1.9.9"}}, "Microsoft.Kiota.Serialization.Form": {"type": "Transitive", "resolved": "1.9.9", "contentHash": "dfyTh9cZairHMKGZIiMOxs9KOAAkzZx5pER7sC/ZulBfDQ+Qezqs/4Co2h1cz7EakNomliu5y3PnZ+LsUrp+gw==", "dependencies": {"Microsoft.Kiota.Abstractions": "1.9.9"}}, "Microsoft.Kiota.Serialization.Json": {"type": "Transitive", "resolved": "1.9.9", "contentHash": "8pwI5Q+VYoBejv65LheOms/jQFSijrH2IYUB/p2xMlsYhsLxPiXSb1iRe3uEGomugDmUiZ2e3+n51i90VTAshg==", "dependencies": {"Microsoft.Kiota.Abstractions": "1.9.9"}}, "Microsoft.Kiota.Serialization.Multipart": {"type": "Transitive", "resolved": "1.1.5", "contentHash": "oe2f6v3z/NemTlGvge6fJIftsx9bHQoAP2XGnp1z7MkDgMO8KRqJh5a1wSIyZZE5QMZedLlevT6zfsPiqNWEtg==", "dependencies": {"Microsoft.Kiota.Abstractions": "[1.9.1, 2.0.0)"}}, "Microsoft.Kiota.Serialization.Text": {"type": "Transitive", "resolved": "1.9.9", "contentHash": "GRN6vROLbuvTqV76ciOAw53RNVApAabw/a8Bn+mKuDIUmOo87kjR/df9PyRHrfmKf7lDbcgdC9kRFuPSQuhOuA==", "dependencies": {"Microsoft.Kiota.Abstractions": "1.9.9"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "lPNIphl8b2EuhOE9dMH6EZDmu7pS882O+HMi5BJNsigxHaWlBrYxZHFZgE18cyaPp6SSZcTkKkuzfjV/RRQKlA==", "dependencies": {"Microsoft.Extensions.Primitives": "2.1.1", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.6.14", "contentHash": "tTaBT8qjk3xINfESyOPE2rIellPvB7qpVqiWiyA/lACVvz+xOGiXhFUfohcx82NLbi5avzLW0lx+s6oAqQijfw=="}, "Microsoft.SqlServer.Server": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug=="}, "Microsoft.SqlServer.Types": {"type": "Transitive", "resolved": "160.1000.6", "contentHash": "4kk+rz5vnIPr9ENzm8KttUbhBKftv0uHSSFDVjc5OuKPtRP5q0lDbYUy5ZsoYCAG3RrZdJoxPnWN2JNozZAiGA==", "dependencies": {"Microsoft.SqlServer.Server": "1.0.0"}}, "Microsoft.Win32.Registry": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "MongoDB.Bson": {"type": "Transitive", "resolved": "2.19.2", "contentHash": "YpwSLKillPRj+BYEZU0dFgVHdCuj7uGIkLcyEc5a9zwJQYMbZkAc2CRVzobnDY9HMSCtyefXnkrz9CQ6C91KQA==", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}}, "MongoDB.Driver": {"type": "Transitive", "resolved": "2.19.2", "contentHash": "xT2VA1KmjzxXQYuA8VbnM3r+DVVjja6ntD7YjesxkODAX3GkQLSgLLH+tS7R0uy/zZzA7YWEF1AKIJyxC7jfmA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.19.2", "MongoDB.Driver.Core": "2.19.2", "MongoDB.Libmongocrypt": "1.7.0"}}, "MongoDB.Driver.Core": {"type": "Transitive", "resolved": "2.19.2", "contentHash": "MjZmAZLNCV+d/eW+X1YPNztEtEBosIX8qMFCoQAhJOoHotuJfTePt+Lp8sjxTDTCyaOtLhClIgefy3P9JaxUHw==", "dependencies": {"AWSSDK.SecurityToken": "3.7.100.14", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "2.0.0", "MongoDB.Bson": "2.19.2", "MongoDB.Libmongocrypt": "1.7.0", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.6.2"}}, "MongoDB.Libmongocrypt": {"type": "Transitive", "resolved": "1.7.0", "contentHash": "p9+peTZX63nGHskOLhvhfBtrknxNg1RzXepE07rPozuCGz27bMjCcQyvn2YByg0L3YEcNWdTmI4BlnG/5RF+5Q=="}, "Mono.TextTemplating": {"type": "Transitive", "resolved": "2.2.1", "contentHash": "KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "dependencies": {"System.CodeDom": "4.4.0"}}, "MySqlConnector": {"type": "Transitive", "resolved": "2.3.5", "contentHash": "AmEfUPkFl+Ev6jJ8Dhns3CYHBfD12RHzGYWuLt6DfG6/af6YvOMyPz74ZPPjBYQGRJkumD2Z48Kqm8s5DJuhLA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.1"}}, "NCrontab": {"type": "Transitive", "resolved": "3.3.3", "contentHash": "2yzZXZLI0YpxrNgWnW/4xoo7ErLgWJIwTljRVEJ3hyjc7Kw9eGdjbFZGP1AhBuTUEZQ443PgZifG1yox6Qo1/A=="}, "NetCore.AutoRegisterDi": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "loGy3NdGb0NT/ufvdFRRstQrtiEoA8XhD0iwrV/egRlB00v/t5wbty6sBgaTgEUsz5da2a/5yGRTxnrNpli9KQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "2.1.0"}}, "NetTopologySuite": {"type": "Transitive", "resolved": "2.5.0", "contentHash": "5/+2O2ADomEdUn09mlSigACdqvAf0m/pVPGtIPEPQWnyrVykYY0NlfXLIdkMgi41kvH9kNrPqYaFBTZtHYH7Xw==", "dependencies": {"System.Memory": "4.5.4"}}, "NetTopologySuite.IO.SpatiaLite": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "nT8ep51zgrTPEtRG+Cxeoi585uFlzmCmJz+xl35ima1xWCk4KuRuLtCEswy8RQkApmeaawAAfsTsa83sgmT4xw==", "dependencies": {"NetTopologySuite": "[2.0.0, 3.0.0-A)"}}, "NetTopologySuite.IO.SqlServerBytes": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "R4BcV19f2l6EjHSjh/EHwLhYQHrOov9vig1EW5oBm0iqlZOgaIJm5tBnlbFnYlvdYOPuf5p0Qtf8PCVwH77Wbg==", "dependencies": {"NetTopologySuite": "[2.0.0, 3.0.0-A)"}}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Npgsql": {"type": "Transitive", "resolved": "8.0.3", "contentHash": "6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"type": "Transitive", "resolved": "8.0.4", "contentHash": "/hHd9MqTRVDgIpsToCcxMDxZqla0HAQACiITkq1+L9J2hmHKV6lBAPlauF+dlNSfHpus7rrljWx4nAanKD6qAw==", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.4", "Microsoft.EntityFrameworkCore.Abstractions": "8.0.4", "Microsoft.EntityFrameworkCore.Relational": "8.0.4", "Npgsql": "8.0.3"}}, "Pomelo.EntityFrameworkCore.MySql": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "XjnlcxVBLnEMbyEc5cZzgZeDyLvAniACZQ04W1slWN0f4rmfNzl98gEMvHnFH0fMDF06z9MmgGi/Sr7hJ+BVnw==", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "[8.0.2, 8.0.999]", "MySqlConnector": "2.3.5"}}, "QRCoder": {"type": "Transitive", "resolved": "1.4.1", "contentHash": "W8KwCwsOJe9SI7Cm7XeIMawd08F/US6xNw34lg2Qnx6m7GYYakxbyBQaNoW1Q75oJOmX/32sJUZr6Cix2B6GUQ==", "dependencies": {"System.Drawing.Common": "4.7.0"}}, "RBush": {"type": "Transitive", "resolved": "3.2.0", "contentHash": "ijGh9N0zZ7JfXk3oQkWCwK8SwSSByexbyh/MjbCjNxOft9eG5ZqKC1vdgiYq78h4IZRFmN4s3JZ/b10Jipud5w=="}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g=="}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw=="}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg=="}, "runtime.native.System": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Compression": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ=="}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA=="}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ=="}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w=="}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg=="}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw=="}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w=="}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.2", "contentHash": "leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg=="}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg=="}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ=="}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA=="}, "SendGrid": {"type": "Transitive", "resolved": "9.29.3", "contentHash": "nb/zHePecN9U4/Bmct+O+lpgK994JklbCCNMIgGPOone/DngjQoMCHeTvkl+m0Nglvm0dqMEshmvB4fO8eF3dA==", "dependencies": {"Newtonsoft.Json": "13.0.1", "starkbank-ecdsa": "[1.3.3, 2.0.0)"}}, "Serilog": {"type": "Transitive", "resolved": "3.0.1", "contentHash": "E4UmOQ++eNJax1laE+lws7E3zbhKgHsGJbO7ra0yE5smUh+5FfUPIKKBxM3MO1tK4sgpQke6/pLReDxIc/ggNw=="}, "Serilog.Extensions.Hosting": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "AWsDTs6TeCtyXYDWakzLXCOZA3/IdIfBWBwkYAF0ZvVktVr3E15oYP9pfI7GzKaGVmHaJF9TgFQnFEfcnzEkcw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Logging": "7.0.0"}}, "Serilog.Extensions.Logging": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "9faU0zNQqU7I6soVhLUMYaGNpgWv6cKlKb2S5AnS8gXxzW/em5Ladm/6FMrWTnX41cdbdGPOWNAo6adi4WaJ6A==", "dependencies": {"Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.12.0"}}, "Serilog.Settings.Configuration": {"type": "Transitive", "resolved": "7.0.1", "contentHash": "FpUWtc0YUQvCfrKRI73KbmpWK3RvWTQr9gMDfTPEtmVI6f7KkY8Egj6r1BQA1/4oyTjxRbTn5yKX+2+zaWTwrg==", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Serilog": "2.12.0"}}, "Serilog.Sinks.ApplicationInsights": {"type": "Transitive", "resolved": "4.0.0", "contentHash": "AlYq1JFqh+RFKwLKZ3X224Zbe1gnkMbqSELp2FApLN0iMyRPdwwxMJBCCrk49C8qOefBd4zN+J/1Tq3i75DunA==", "dependencies": {"Microsoft.ApplicationInsights": "2.20.0", "Serilog": "2.11.0"}}, "Serilog.Sinks.Console": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "dependencies": {"Serilog": "2.10.0"}}, "Serilog.Sinks.File": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "dependencies": {"Serilog": "2.10.0"}}, "SharpCompress": {"type": "Transitive", "resolved": "0.30.1", "contentHash": "XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw=="}, "SixLabors.Fonts": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg=="}, "SkiaSharp": {"type": "Transitive", "resolved": "2.88.9", "contentHash": "3MD5VHjXXieSHCleRLuaTXmL2pD0mB7CcOB1x2kA1I4bhptf4e3R27iM93264ZYuAq6mkUyX5XbcxnZvMJYc1Q==", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.9", "SkiaSharp.NativeAssets.macOS": "2.88.9"}}, "SkiaSharp.NativeAssets.macOS": {"type": "Transitive", "resolved": "2.88.9", "contentHash": "Nv5spmKc4505Ep7oUoJ5vp3KweFpeNqxpyGDWyeEPTX2uR6S6syXIm3gj75dM0YJz7NPvcix48mR5laqs8dPuA=="}, "SkiaSharp.NativeAssets.Win32": {"type": "Transitive", "resolved": "2.88.9", "contentHash": "wb2kYgU7iy84nQLYZwMeJXixvK++GoIuECjU4ECaUKNuflyRlJKyiRhN1MAHswvlvzuvkrjRWlK0Za6+kYQK7w=="}, "Snappier": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA=="}, "SQLitePCLRaw.core": {"type": "Transitive", "resolved": "2.1.6", "contentHash": "wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "dependencies": {"System.Memory": "4.5.3"}}, "starkbank-ecdsa": {"type": "Transitive", "resolved": "1.3.3", "contentHash": "OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog=="}, "Std.UriTemplate": {"type": "Transitive", "resolved": "1.0.3", "contentHash": "0C9WPYRHGHI4L1X3w7mdrr2WKz//6d/T000SqX5HNNPScFGfQYRuL8FBhn4Ny1WGcCnuMZXb6kfCnMW5OtCdTw=="}, "Swashbuckle.AspNetCore.Swagger": {"type": "Transitive", "resolved": "6.7.0", "contentHash": "qI1ntGtNnt1Nksi/oDNkmCULVrImHyLWodJhQzghGj9W6uKYMqVl8Y7M2oU8VbHTQZFImD2kYR9ay8LZkJwaqA==", "dependencies": {"Microsoft.OpenApi": "1.6.14"}}, "Swashbuckle.AspNetCore.SwaggerGen": {"type": "Transitive", "resolved": "6.7.0", "contentHash": "e2eRUBYnMBWzyl0esJfcB4YHuG/boD6nmuDcKdRa+nYPU/57+kbZ3Ot4TGpRDTJcZ0BrWoQiYe5mWRy6whvTkQ==", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.7.0"}}, "Swashbuckle.AspNetCore.SwaggerUI": {"type": "Transitive", "resolved": "6.5.0", "contentHash": "OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw=="}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg=="}, "System.ClientModel": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}}, "System.CodeDom": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ=="}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "qt4+ClsZYKzar/Rw1zbt0t9iuBUCLVNsjXicEuVQyKL7143CwhWzI2gTwzz8AhSaFYVHnpcb8SBGA+zaY4weUg=="}, "System.Collections.NonGeneric": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel.Annotations": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ=="}, "System.Composition": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w=="}, "System.Composition.Convention": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "dependencies": {"System.Composition.AttributedModel": "6.0.0"}}, "System.Composition.Hosting": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "dependencies": {"System.Composition.Runtime": "6.0.0"}}, "System.Composition.Runtime": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg=="}, "System.Composition.TypedParts": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}}, "System.Configuration.ConfigurationManager": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Data.DataSetExtensions": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw=="}, "System.Data.SqlClient": {"type": "Transitive", "resolved": "4.8.6", "contentHash": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "n1ZP7NM2Gkn/MgD8+eOT5MulMj6wfeQMNS2Pizvq5GHCZfjlFMXV2irQlQmJhwA2VABC57M0auudO89Iu2uRLg=="}, "System.Diagnostics.PerformanceCounter": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}}, "System.Diagnostics.Tracing": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common": {"type": "Transitive", "resolved": "4.7.2", "contentHash": "I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.SystemEvents": "4.7.0"}}, "System.Formats.Asn1": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "+nfpV0afLmvJW8+pLlHxRjz3oZJw4fkyU9MMEaMhCsHi/SN9bGF9q79ROubDiwTiCHezmK0uCWkPP7tGFP/4yg=="}, "System.Formats.Nrbf": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "emQJI8AXZQHUqCFkMHPWP48HnlkQnte5OVdkvGPJHhxCAJBsNMO6uNkqytgqLBe103MenD+7hzLIf9AGENTu5g==", "dependencies": {"System.Reflection.Metadata": "9.0.1"}}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Transitive", "resolved": "8.3.0", "contentHash": "9GESpDG0Zb17HD5mBW/uEWi2yz/uKPmCthX2UhyLnk42moGH2FpMgXA2Y4l2Qc7P75eXSUTA6wb/c9D9GSVkzw==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.3.0", "Microsoft.IdentityModel.Tokens": "8.3.0"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "vMToiarpU81LR1/KZtnT7VDPvqAZfw9oOS5nY6pPP78nGYz3COLsQH3OfzbR+SjTgltd31R6KmKklz/zDpTmzw==", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.IO.FileSystem.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Hashing": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g=="}, "System.IO.Packaging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "8g1V4YRpdGAxFcK8v9OjuMdIOJSpF30Zb1JGicwVZhly3I994WFyBdV6mQEo8d3T+URQe55/M0U0eIH0Hts1bg=="}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "6.0.3", "contentHash": "ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw=="}, "System.Linq": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core": {"type": "Transitive", "resolved": "1.3.2", "contentHash": "7/RIS6rG69UaIxvJeq/ZxxrXKwLZbYr+Xn5Xe1j/iLA15QNiPwiLuWbI6GCXZesAMe9kHkz5JIdVEQXqS0MiAA=="}, "System.Memory": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}}, "System.Net.Http": {"type": "Transitive", "resolved": "4.3.4", "contentHash": "aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "XGkA0wiS4vbZu708VtlgSh40stW2ucDriDItkiJUnnp867x5HaBfmoTqdA00DYG0Ld7Kg0cLvEPkUEUxputgzQ==", "dependencies": {"System.Collections.Immutable": "9.0.1"}}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.Extensions": {"type": "Transitive", "resolved": "9.0.1", "contentHash": "Bf0i8S1inJam8700ORlbC11CmmRHkvaKj1YbK8mgojbrUyIfz+8ibUO+O8E/MjDwnYEa0j769P3kgJ6AkLcc5Q==", "dependencies": {"System.Formats.Nrbf": "9.0.1"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.Numerics": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs": {"type": "Transitive", "resolved": "7.0.2", "contentHash": "xhFNJOcQSWhpiVGLLBQYoxAltQSQVycMkwaX1z7I7oEdT9Wr0HzSM1yeAbfoHaERIYd5s6EpLSOLs2qMchSKlA==", "dependencies": {"System.Formats.Asn1": "7.0.0"}}, "System.Security.Cryptography.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg=="}, "System.Security.Cryptography.X509Certificates": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Xml": {"type": "Transitive", "resolved": "7.0.1", "contentHash": "MCxBCtH0GrDuvU63ZODwQHQZPchb24pUAX3MfZ6b13qg246ZD10PRdOvay8C9HBPfCXkymUNwFPEegud7ax2zg==", "dependencies": {"System.Security.Cryptography.Pkcs": "7.0.0"}}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA=="}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "System.Text.Json": {"type": "Transitive", "resolved": "8.0.4", "contentHash": "bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q=="}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "Twilio": {"type": "Transitive", "resolved": "6.14.1", "contentHash": "IshB3wYl8sQID43cU22aQLKqU15lKAi5CyAX7SXn0XvjWkS2+NMlUgWwJgPHnPyayv0x+xcqswVyzlKrJZcnlA==", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.19.0", "Newtonsoft.Json": "13.0.1", "System.Collections.Specialized": "4.3.0", "System.IdentityModel.Tokens.Jwt": "6.19.0"}}, "TwoFactorAuth.Net": {"type": "Transitive", "resolved": "1.4.0", "contentHash": "dtBcTQAVauQqhD5SvO8YVFM2exiC+Bk1idx+Gh0kOHdvXsPdHICY5qTMLbpc5Mxd+Sy+d9XeF3qFqkyN/V9X+w==", "dependencies": {"System.Net.Http": "4.3.4"}}, "X.PagedList": {"type": "Transitive", "resolved": "8.4.7", "contentHash": "ZA1WcyNviMUhivCgwKAm/5/YsNP3Yf/n81SzrPaHSJOcFzavA9MrXTT8M+ZX4fvHwLCL1bY6BPXvwZF5pRtgTg==", "dependencies": {"JetBrains.Annotations": "2022.3.1"}}, "ZstdSharp.Port": {"type": "Transitive", "resolved": "0.6.2", "contentHash": "jPao/LdUNLUz8rn3H1D8W7wQbZsRZM0iayvWI4xGejJg3XJHT56gcmYdgmCGPdJF1UEBqUjucCRrFB+4HbJsbw=="}, "netprogroup.trust.application": {"type": "Project", "dependencies": {"Asp.Versioning.Mvc.ApiExplorer": "[8.1.0, )", "AutoBogus": "[2.13.1, )", "Hangfire.Core": "[1.8.15, )", "Hangfire.InMemory": "[1.0.0, )", "Hangfire.NetCore": "[1.8.15, )", "Microsoft.Extensions.DependencyInjection.Abstractions": "[8.0.2, )", "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": "[8.0.14, )", "NCrontab": "[3.3.3, )", "NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.Application.Contracts": "[1.0.0, )", "NetProGroup.Trust.DataManager": "[1.0.0, )", "NetProGroup.Trust.DataMigration": "[1.0.0, )", "NetProGroup.Trust.Domain": "[1.0.0, )", "NetProGroup.Trust.Import": "[1.0.0, )", "NetProGroup.Trust.Payment": "[1.0.0, )", "NetProGroup.Trust.Reports": "[1.0.0, )", "Serilog.Sinks.ApplicationInsights": "[4.0.0, )", "Swashbuckle.AspNetCore.SwaggerGen": "[6.7.0, )", "System.Configuration.ConfigurationManager": "[8.0.1, )", "System.IO.Compression.ZipFile": "[4.3.0, )", "System.Resources.Extensions": "[9.0.1, )"}}, "netprogroup.trust.application.contracts": {"type": "Project", "dependencies": {"NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.Domain": "[1.0.0, )", "NetProGroup.Trust.Domain.Shared": "[1.0.0, )", "NetProGroup.Trust.Forms": "[1.0.0, )"}}, "netprogroup.trust.datamanager": {"type": "Project", "dependencies": {"AutoMapper": "[13.0.1, )", "ClosedXML": "[0.104.1, )", "EFCore.BulkExtensions": "[8.1.1, )", "Microsoft.Kiota.Abstractions": "[1.9.9, )", "Microsoft.Kiota.Authentication.Azure": "[1.9.9, )", "Microsoft.Kiota.Http.HttpClientLibrary": "[1.9.9, )", "Microsoft.Kiota.Serialization.Form": "[1.9.9, )", "Microsoft.Kiota.Serialization.Json": "[1.9.9, )", "Microsoft.Kiota.Serialization.Text": "[1.9.9, )", "NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.Application.Contracts": "[1.0.0, )", "NetProGroup.Trust.Domain": "[1.0.0, )", "NetProGroup.Trust.Domain.Repository": "[1.0.0, )", "NetProGroup.Trust.Payment": "[1.0.0, )", "TwoFactorAuth.Net": "[1.4.0, )"}}, "netprogroup.trust.datamigration": {"type": "Project", "dependencies": {"Microsoft.ApplicationInsights": "[2.23.0, )", "Microsoft.EntityFrameworkCore": "[8.0.10, )", "Microsoft.EntityFrameworkCore.SqlServer": "[8.0.10, )", "MongoDB.Driver": "[2.19.2, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.DataManager": "[1.0.0, )", "NetProGroup.Trust.Domain": "[1.0.0, )", "NetProGroup.Trust.Domain.Repository": "[1.0.0, )"}}, "netprogroup.trust.domain": {"type": "Project", "dependencies": {"NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.Domain.Shared": "[1.0.0, )"}}, "netprogroup.trust.domain.repository": {"type": "Project", "dependencies": {"Microsoft.Data.SqlClient": "[5.2.2, )", "Microsoft.EntityFrameworkCore": "[8.0.10, )", "Microsoft.EntityFrameworkCore.SqlServer": "[8.0.10, )", "NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.Application.Contracts": "[1.0.0, )", "NetProGroup.Trust.Domain": "[1.0.0, )"}}, "netprogroup.trust.domain.shared": {"type": "Project", "dependencies": {"NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )"}}, "netprogroup.trust.forms": {"type": "Project", "dependencies": {"NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "Newtonsoft.Json": "[13.0.3, )"}}, "netprogroup.trust.import": {"type": "Project", "dependencies": {"FastExcel": "[3.0.13, )", "NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.DataManager": "[1.0.0, )", "NetProGroup.Trust.Domain.Repository": "[1.0.0, )"}}, "netprogroup.trust.payment": {"type": "Project", "dependencies": {"NetProGroup.Framework": "[1.4.5, )", "NetProGroup.Trust.Application.Contracts": "[1.0.0, )"}}, "netprogroup.trust.reports": {"type": "Project", "dependencies": {"ClosedXML": "[0.104.1, )", "NetPro.StyleCop.Configuration.Package": "[1.1.22, )", "NetProGroup.Trust.DataManager": "[1.0.0, )", "NetProGroup.Trust.Domain": "[1.0.0, )"}}}}}