// <copyright file="SyncDetailsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Status.Models
{
    /// <summary>
    /// DTO for sync details of an entity type.
    /// </summary>
    public class SyncDetailsDTO
    {
        /// <summary>
        /// Gets or sets the timestamp of the last successful sync.
        /// </summary>
        public DateTime? LastSuccessfulSync { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction codes used in the sync.
        /// </summary>
        public IEnumerable<string> JurisdictionsUsed { get; set; }

        /// <summary>
        /// Gets or sets the count of updated records.
        /// </summary>
        public int UpdatedCount { get; set; }

        /// <summary>
        /// Gets or sets the count of deleted records.
        /// </summary>
        public int DeletedCount { get; set; }
    }
}
