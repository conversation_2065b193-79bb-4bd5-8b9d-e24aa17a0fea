﻿// <copyright file="MasterClientsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.AppServices.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.DataManager.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.MasterClients
{
    /// <summary>
    /// Application service.
    /// </summary>
    public class MasterClientsAppService : IMasterClientsAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly IMasterClientsDataManager _dataManager;
        private readonly ILegalEntitiesDataManager _legalEntityDataManager;
        private readonly IUsersDataManager _usersDataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientsAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="workContext">The workcontext for the request.</param>
        /// <param name="securityManager">The security manager for permissions.</param>
        /// <param name="legalEntityDataManager">The DataManager for legal entities.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="usersDataManager">Datamanager for the users.</param>
        public MasterClientsAppService(ILogger<JurisdictionsAppService> logger,
                                       IMapper mapper,
                                       IWorkContext workContext,
                                       ISecurityManager securityManager,
                                       ILegalEntitiesDataManager legalEntityDataManager,
                                       IMasterClientsDataManager dataManager,
                                       IUsersDataManager usersDataManager)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _securityManager = securityManager;
            _dataManager = dataManager;
            _legalEntityDataManager = legalEntityDataManager;
            _usersDataManager = usersDataManager;
        }

        /// <summary>
        /// Checks if the email/masterclientcode relation exists.
        /// </summary>
        /// <param name="masterClientCode">The MasterClientCode to check for.</param>
        /// <param name="email">The emailaddress to check for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> EmailExistsForMasterClientCodeAsync(string masterClientCode, string email)
        {
            ArgumentException.ThrowIfNullOrWhiteSpace(email, nameof(email));
            ArgumentException.ThrowIfNullOrWhiteSpace(masterClientCode, nameof(masterClientCode));

            return await _dataManager.EmailExistsForMasterClientCodeAsync(masterClientCode, email);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<MasterClientDTO>> GetMasterClientsAsync(string searchTerm, Guid? jurisdictionId, int pageNumber, int pageSize)
        {
            // Authorization
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_Search);

            var response = await _dataManager.ListMasterClientsAsync(new ListMasterClientsRequest
            {
                SearchTerm = searchTerm,
                JurisdictionId = jurisdictionId,
                PageNumber = pageNumber,
                PageSize = pageSize,
            });
            return response.MasterClientItems;
        }

        /// <inheritdoc/>
        public async Task<MasterClientsSearchResultsDTO> SearchMasterClientsAsync(string searchTerm)
        {
            await _securityManager.RequireClientUserAsync();

            var response = await _dataManager.SearchMasterClientsAsync(new SearchMasterClientsRequest
            {
                SearchTerm = searchTerm,
                UserId = _workContext.IdentityUserId.Value
            });

            return new MasterClientsSearchResultsDTO { MasterClients = response.MasterClientItems };
        }

        /// <inheritdoc/>
        public async Task<CompaniesSearchResultsDTO> SearchCompaniesAsync(Guid masterClientId, string searchTerm, bool? active, OnboardingStatus? onboardingStatus)
        {
            await _securityManager.RequireClientUserAsync();

            var response = await _legalEntityDataManager.SearchCompaniesAsync(new SearchCompaniesRequest
            {
                SearchTerm = searchTerm,
                MasterClientId = masterClientId,
                UserId = _workContext.IdentityUserId.Value,
                Active = active,
                OnboardingStatus = onboardingStatus
            });
            return new CompaniesSearchResultsDTO { Companies = response.CompanyItems };
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListMasterClientUserDTO>> ListUsersAsync(Guid masterClientId, ListUsersRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_View_Trident_Users);

            return await _usersDataManager.ListMasterClientUsersAsync(new DataManager.Users.RequestResponses.ListUsersRequest
            {
                MasterClientId = masterClientId,
                PagingInfo = request.ToPagingInfo(),
                SortingInfo = request.ToSortingInfo()
            });
        }

        /// <summary>
        /// Adds one or multiple users by emailaddress to a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<MasterClientUserDTO> CreateUserToMasterClientAsync(CreateMasterClientUserDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_Add_Trident_Users);

            return await _dataManager.CreateUserToMasterClientAsync(model, true);
        }

        /// <summary>
        /// Removes a user from a master client.
        /// </summary>
        /// <param name="model">The data transfer object containing the user and master client information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task RemoveUserFromMasterClientAsync(RemoveMasterClientUserDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_Remove_Trident_Users);

            await _dataManager.RemoveUserFromMasterClientAsync(model, true);
        }

        /// <inheritdoc/>
        public async Task<MasterClientDTO> GetMasterClientAsync(Guid masterClientId)
        {
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.MasterClients_View);

            return await _dataManager.GetMasterClientAsync(masterClientId);
        }
    }
}
