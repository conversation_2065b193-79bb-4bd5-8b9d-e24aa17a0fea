﻿// <copyright file="ModulesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Shared.Permissions;

namespace NetProGroup.Trust.Application.AppServices.Modules
{
    /// <summary>
    /// Application service for modules.
    /// </summary>
    public class ModulesAppService : IModulesAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly IModulesDataManager _dataManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ModulesAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="workContext">The workcontext for the request.</param>
        /// <param name="securityManager">The security manager for permissions.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        public ModulesAppService(ILogger<ModulesAppService> logger,
                                 IMapper mapper,
                                 IWorkContext workContext,
                                 ISecurityManager securityManager,
                                 IModulesDataManager dataManager)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _securityManager = securityManager;
            _dataManager = dataManager;
        }

        /// <inheritdoc/>
        public async Task<ListModulesDTO> GetJurisdictionModulesAsync(Guid jurisdictionId)
        {
            Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));

            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var response = await _dataManager.GetModulesAsync(new DataManager.Modules.RequestResponses.ListModulesRequest { JurisdictionId = jurisdictionId });

            return new ListModulesDTO { Modules = response.ModuleItems };
        }

        /// <inheritdoc/>
        public async Task<ListCompanyModulesDTO> GetCompanyModulesAsync(Guid companyId, bool forClientUI)
        {
            Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));

            if (!forClientUI)
            {
                // Authorization
                await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_View_Available_Modules, companyId);
            }
            else
            {
                await _securityManager.RequireClientAccessToCompanyAsync(companyId);
            }

            var response = await _dataManager.GetModulesAsync(new DataManager.Modules.RequestResponses.ListModulesRequest { CompanyId = companyId, ForClientUI = forClientUI });

            return new ListCompanyModulesDTO { Modules = response.CompanyModuleItems };
        }

        /// <inheritdoc/>
        public async Task<ListModulesDTO> GetAllModulesAsync(bool? isActive = null)
        {
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Companies_View_Available_Modules);

            var response = await _dataManager.GetModulesAsync(new DataManager.Modules.RequestResponses.ListModulesRequest { IsActive = isActive });

            return new ListModulesDTO { Modules = response.ModuleItems };
        }

        /// <inheritdoc/>
        public async Task<ListModulesDTO> SetJurisdictionModulesAsync(Guid jurisdictionId,
            IReadOnlyCollection<SetModuleDTO> modules)
        {
            Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));

            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            var response = await _dataManager.SetModulesAsync(new DataManager.Modules.RequestResponses.SetModulesRequest { JurisdictionId = jurisdictionId, ModuleItems = modules.ToList() });

            return new ListModulesDTO { Modules = response.ModuleItems };
        }

        /// <inheritdoc/>
        public async Task<ListCompanyModulesDTO> SetCompanyModulesAsync(Guid companyId,
            IReadOnlyCollection<SetCompanyModuleDTO> modules)
        {
            Check.NotDefaultOrNull<Guid>(companyId, nameof(companyId));

            // Authorization
            await _securityManager.RequireManagementPermissionForLegalEntityAsync(WellKnownPermissionNames.Companies_Set_Available_Modules, companyId);

            var response = await _dataManager.SetModulesAsync(new DataManager.Modules.RequestResponses.SetModulesRequest { CompanyId = companyId, CompanyModuleItems = modules.ToList() });

            return new ListCompanyModulesDTO { Modules = response.CompanyModuleItems };
        }
    }
}
