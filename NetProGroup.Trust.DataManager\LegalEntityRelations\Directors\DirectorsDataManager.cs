﻿// <copyright file="DirectorsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Communication;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.DataManager.Jurisdictions;
using NetProGroup.Trust.DataManager.LegalEntityRelations.Directors.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.DomainShared.Enums;
using System.Diagnostics;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.LegalEntityRelations
{
    /// <summary>
    /// Manager for Directors data.
    /// </summary>
    public class DirectorsDataManager : IDirectorsDataManager
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;

        private readonly ILegalEntitiesRepository _legalEntityRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;

        private readonly IDirectorsRepository _directorsRepository;
        private readonly IDirectorHistoryRepository _directorHistoryRepository;

        private readonly ICommunicationAppService _communicationAppService;

        private readonly ILockManager _lockManager;
        private readonly IBulkOperationProvider _bulkOperationProvider;
        private LockDTO _jobLock;

        private List<Director> _directorsToInsert = new List<Director>();
        private List<Director> _directorsToDelete = new List<Director>();
        private List<Director> _directorsToUpdate = new List<Director>();
        private List<DirectorHistory> _directorsHistoryToInsert = new List<DirectorHistory>();
        private List<DirectorHistory> _directorsHistoryToDelete = new List<DirectorHistory>();
        private List<Domain.Sync.SyncMessage> _syncMessagesToInsert = new List<Domain.Sync.SyncMessage>();
        private List<Guid> _syncingJurisdictionIds = new List<Guid>();

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorsDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="systemAuditManager">Instance of the manager for audits.</param>
        /// <param name="repository">Instance of the repository.</param>
        /// <param name="jurisdictionsRepository">Instance of the Jurisdiction repository.</param>
        /// <param name="masterClientsRepository">Instance of the MasterClient repository.</param>
        /// <param name="directorsRepository">Instance of the Directors repository.</param>
        /// <param name="directorHistoryRepository">Instance of the DirectorHistory repository.</param>
        /// <param name="communicationAppService">AppService for communication.</param>
        /// <param name="lockManager">Instance of the manager for locks.</param>
        /// <param name="bulkOperationProvider">Provider for bulk operations.</param>
        public DirectorsDataManager(ILogger<DirectorsDataManager> logger,
                                    IMapper mapper,
                                    IWorkContext workContext,
                                    ISystemAuditManager systemAuditManager,
                                    ILegalEntitiesRepository repository,
                                    IJurisdictionsRepository jurisdictionsRepository,
                                    IMasterClientsRepository masterClientsRepository,
                                    IDirectorsRepository directorsRepository,
                                    IDirectorHistoryRepository directorHistoryRepository,
                                    ICommunicationAppService communicationAppService,
                                    ILockManager lockManager,
                                    IBulkOperationProvider bulkOperationProvider)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;

            _legalEntityRepository = repository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _masterClientsRepository = masterClientsRepository;

            _directorsRepository = directorsRepository;
            _directorHistoryRepository = directorHistoryRepository;

            _communicationAppService = communicationAppService;
            _lockManager = lockManager;
            _bulkOperationProvider = Check.NotNull(bulkOperationProvider, nameof(bulkOperationProvider));
        }

        private async Task<string> ToUniqueRelationIdAsync(string value)
        {
            if (Guid.TryParse(value, out var id))
            {
                var item = await _directorsRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == id);
                if (item == null)
                {
                    throw new Framework.Exceptions.BadRequestException("Invalid id for Director)");
                }

                return item.ExternalUniqueId;
            }
            else
            {
                return value;
            }
        }

        /// <inheritdoc/>
        public async Task<DirectorDTO> GetDirectorAsync(string uniqueRelationId)
        {
            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _directorsRepository.FindFirstOrDefaultByConditionAsync(x => x.ExternalUniqueId == uniqueRelationId,
                                                                                     q => q.Include(d => d.LegalEntity));

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{uniqueRelationId}' was not found");
            }

            var result = _mapper.Map<DirectorDTO>(item);

            var history = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(result.UniqueRelationCode);
            if (history != null)
            {
                var metaData = _mapper.Map<LegalEntityRelationMetaData>(history);
                result.MetaData = metaData;
            }

            CheckMissingInformation(result);

            return result;
        }

        /// <inheritdoc/>
        public async Task<DirectorDTO> GetDirectorAsync(Guid directorId)
        {
            var item = await _directorsRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == directorId,
                                                                                     q => q.Include(d => d.LegalEntity));

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{directorId}' was not found");
            }

            var result = _mapper.Map<DirectorDTO>(item);

            var history = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(result.UniqueRelationCode);
            if (history != null)
            {
                var metaData = _mapper.Map<LegalEntityRelationMetaData>(history);
                result.MetaData = metaData;
            }

            CheckMissingInformation(result);

            return result;
        }

        /// <inheritdoc />
        public async Task<DirectorDTO> FindDirectorAsync(string uniqueRelationId)
        {
            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _directorsRepository.FindFirstOrDefaultByConditionAsync(x => x.ExternalUniqueId == uniqueRelationId,
                q => q.Include(d => d.LegalEntity));

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{uniqueRelationId}' was not found");
            }

            var result = _mapper.Map<DirectorDTO>(item);
            return result;
        }

        /// <inheritdoc />
        public async Task<DirectorDTO> CheckDirectorByIdAsync(Guid id)
        {
            var item = await _directorsRepository.CheckDirectorByIdAsync(id);

            var result = _mapper.Map<DirectorDTO>(item);

            return result;
        }

        /// <inheritdoc/>
        public async Task<ListDirectorsResponse> ListDirectorsAsync(ListDirectorsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var items = await _directorsRepository.FindByConditionAsync(x => x.LegalEntityId == request.LegalEntityId,
                                                                        q => q.Include(d => d.LegalEntity));

            var itemsPaged = new PagedList<Director>(items, request.PageNumber, request.PageSize);

            var dtos = _mapper.Map<List<DirectorDTO>>(itemsPaged);

            var dtosPaged = new StaticPagedList<DirectorDTO>(dtos, request.PageNumber, request.PageSize, items.Count());

            // Augment with metadata
            foreach (var dto in dtosPaged)
            {
                CheckMissingInformation(dto);

                if (request.IncludeMetaData)
                {
                    var history = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(dto.UniqueRelationCode);
                    if (history != null)
                    {
                        var metaData = _mapper.Map<LegalEntityRelationMetaData>(history);
                        dto.MetaData = metaData;
                    }
                }
            }

            var result = new ListDirectorsResponse { DirectorItems = dtosPaged };
            return result;
        }

        /// <inheritdoc/>
        public async Task<ConfirmationResponse> ConfirmDataAsync(ConfirmationRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var uniqueRelationId = await ToUniqueRelationIdAsync(request.UniqueRelationId);

            var item = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{uniqueRelationId}' was not found");
            }

            item = Clone(item);
            item.ConfirmData(request);
            await _directorHistoryRepository.InsertAsync(item);

            await _systemAuditManager.AddActivityLogAsync(item, ActivityLogActivityTypes.DirectorDataConfirmed, "Data confirmed.", $"The data for Beneficial Owner {item.Name} is confirmed.");

            await _directorHistoryRepository.SaveChangesAsync();

            return new ConfirmationResponse();
        }

        /// <inheritdoc/>
        public async Task<RequestUpdateResponse> RequestUpdateAsync(RequestUpdateRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var uniqueRelationId = await ToUniqueRelationIdAsync(request.UniqueRelationId);

            var item = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{uniqueRelationId}' was not found");
            }

            item = Clone(item);
            item.RequestUpdate(request);

            await _directorHistoryRepository.InsertAsync(item, saveChanges: false);

            await _systemAuditManager.AddActivityLogAsync(item, ActivityLogActivityTypes.DirectorDataUpdateRequested, "Update requested.", $"An update is requested for Director {item.Name}.");

            // Setup tokens for the email
            var tokens = CreateTokens(item);

            var productionOffice = string.Empty;

            if (item.LegalEntity != null)
            {
                productionOffice = item.LegalEntity.ProductionOffice == null ? string.Empty : item.LegalEntity.ProductionOffice;
            }

            // Send an email
            await _communicationAppService.SendRequestForUpdateAsync(productionOffice, tokens);

            await _directorHistoryRepository.SaveChangesAsync();

            return new RequestUpdateResponse();
        }

        /// <inheritdoc/>
        public async Task<RequestAssistanceResponse> RequestAssistanceAsync(RequestAssistanceRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var legalEntity = await _legalEntityRepository.GetByIdAsync(request.LegalEntityId, q => q.Include(le => le.MasterClient));
            if (legalEntity == null)
            {
                throw new NetProGroup.Framework.Exceptions.NotFoundException(ApplicationErrors.COMPANY_NOT_FOUND.ToErrorCode(), "Company not found");
            }

            await _systemAuditManager.AddActivityLogAsync(legalEntity, ActivityLogActivityTypes.ShareholderAssistanceRequested, "Assistance requested.", $"Assistance is requested for Directors of '{legalEntity.Name}'.");

            var tokens = CreateTokens(legalEntity, request);

            var productionOffice = legalEntity.ProductionOffice == null ? string.Empty : legalEntity.ProductionOffice;

            // Send an email
            await _communicationAppService.SendRequestForAssistanceAsync(productionOffice, tokens);

            await _legalEntityRepository.SaveChangesAsync();

            return new RequestAssistanceResponse();
        }

        /// <inheritdoc/>
        public async Task<DirectorComparisonDTO> GetDirectorForComparisonAsync(string uniqueRelationId)
        {
            var result = new DirectorComparisonDTO();

            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{uniqueRelationId}' was not found");
            }

            result.CurrentVersion = _mapper.Map<DirectorDTO>(item);

            if (item.Status == LegalEntityRelationStatus.Refreshed || item.Status == LegalEntityRelationStatus.UpdateReceived)
            {

                // And get the last with one of the following states
                var statuses = new LegalEntityRelationStatus[] { LegalEntityRelationStatus.Confirmed, LegalEntityRelationStatus.PendingUpdateRequest, LegalEntityRelationStatus.Initial };

                item = await _directorHistoryRepository.GetLastDirectorByUniqueRelationIdAndStatusAsync(item.ExternalUniqueId, statuses);
                result.PriorVersion = _mapper.Map<DirectorDTO>(item);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<DirectorComparisonDTO> GetDirectorForComparisonAsync(Guid directorId)
        {
            var result = new DirectorComparisonDTO();

            var itemById = await _directorsRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == directorId);

            if (itemById == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"Director '{directorId}' was not found");
            }

            var item = await _directorHistoryRepository.GetCurrentDirectorByUniqueRelationIdAsync(itemById.ExternalUniqueId);

            result.CurrentVersion = _mapper.Map<DirectorDTO>(item);

            if (item.Status == LegalEntityRelationStatus.Refreshed || item.Status == LegalEntityRelationStatus.UpdateReceived)
            {

                // And get the last with one of the following states
                var statuses = new LegalEntityRelationStatus[] { LegalEntityRelationStatus.Confirmed, LegalEntityRelationStatus.PendingUpdateRequest, LegalEntityRelationStatus.Initial };

                item = await _directorHistoryRepository.GetLastDirectorByUniqueRelationIdAndStatusAsync(item.ExternalUniqueId, statuses);
                result.PriorVersion = _mapper.Map<DirectorDTO>(item);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task SyncDirectorsAsync(ICollection<SyncDirector> directors)
        {
            var legalEntitiesById = new Dictionary<Guid, LegalEntity>();
            var legalEntities = new Dictionary<string, LegalEntity>(StringComparer.OrdinalIgnoreCase);
            var directorsByLegalEntity = new Dictionary<Guid, IList<SyncDirector>>();

            foreach (var director in directors)
            {
                var key = director.CompanyNumber;

                if (!legalEntities.TryGetValue(key, out LegalEntity legalEntity))
                {
                    legalEntity = await _legalEntityRepository.FindFirstOrDefaultByConditionAsync(le => le.Code == key);
                    if (legalEntity == null)
                    {
                        throw new ConstraintException($"LegalEntiry with code '{key}' does not exist");
                    }

                    legalEntities[key] = legalEntity;
                    legalEntitiesById[legalEntity.Id] = legalEntity;
                }

                if (!directorsByLegalEntity.TryGetValue(legalEntity.Id, out IList<SyncDirector> value))
                {
                    value = new List<SyncDirector>();
                    directorsByLegalEntity.Add(legalEntity.Id, value);
                }

                value.Add(director);
            }

            foreach (var key in directorsByLegalEntity.Keys)
            {
                await SyncDirectorsAsync(legalEntitiesById[key],
                                         directorsByLegalEntity[key],
                                         new List<SyncDirector>(),
                                         null);
            }
        }

        /// <inheritdoc/>
        public async Task SyncDirectorsAsync(SyncDirectorRequest request, LockDTO jobLock = null, Func<DbContext, Task> beforeCommitAsync = null)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            _logger.LogInformation("Found {CountChanged} changed directors and {CountRemoved} removed directors", request.ChangedDirectors.Count, request.RemovedDirectors.Count);

            _jobLock = jobLock;

            ClearBulkData();

            _syncingJurisdictionIds = await GetSyncingJurisdictionIdsAsync();

            var allDirectors = await _directorsRepository.FindAllAsync();
            var allDirectorsByUniqueCode = allDirectors.ToDictionary(e => e.ExternalUniqueId);

            var allLegalEntitiesByCode = (await _legalEntityRepository.FindAllAsync()).ToDictionary(e => e.Code);

            var changedDirectorsByEntityCode = new Dictionary<string, List<SyncDirector>>(StringComparer.OrdinalIgnoreCase);
            var removedDirectorsByEntityCode = new Dictionary<string, List<SyncDirector>>(StringComparer.OrdinalIgnoreCase);

            // We need the EntityCode for the removed directors
            var removedExternalUniqueIds = request.RemovedDirectors.Select(rd => rd.UniqueRelationId);
            var directorsToRemove = (await _directorsRepository.FindByConditionAsync(d => removedExternalUniqueIds.Contains(d.ExternalUniqueId), q => q.Include(d => d.LegalEntity))).ToDictionary(d => d.ExternalUniqueId);
            foreach (var director in request.RemovedDirectors)
            {
                if (directorsToRemove.TryGetValue(director.UniqueRelationId, out var value))
                {
                    director.EntityCode = value.LegalEntity.Code;
                }
            }

            // Collect all code for the entities that we need to check the Dirs for
            var entityCodes = request.ChangedDirectors.Select(le => le.EntityCode).Distinct().ToList();
            entityCodes.AddRange(request.RemovedDirectors.Select(le => le.EntityCode.GetValueOrDefault("NULL")).Distinct().ToList());
            entityCodes = entityCodes.Distinct().ToList();

            foreach (var entityCode in entityCodes)
            {
                if (!allLegalEntitiesByCode.ContainsKey(entityCode))
                {
                    // ToDo: Log error?
                    _logger.LogWarning("Entity {EntityCode} not found (check status of entity in staging)", entityCode);
                }

                changedDirectorsByEntityCode[entityCode] = new List<SyncDirector>();
                removedDirectorsByEntityCode[entityCode] = new List<SyncDirector>();
            }

            var processedCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            foreach (var bo in request.ChangedDirectors)
            {
                if (!processedCodes.Contains(bo.UniqueRelationId))
                {
                    changedDirectorsByEntityCode[bo.EntityCode].Add(bo);
                    processedCodes.Add(bo.UniqueRelationId);
                }
            }

            foreach (var bo in request.RemovedDirectors)
            {
                removedDirectorsByEntityCode[bo.EntityCode].Add(bo);
            }

            foreach (var entityCode in entityCodes)
            {
                if (allLegalEntitiesByCode.TryGetValue(entityCode, out var legalEntity))
                {
                    if (_syncingJurisdictionIds.Contains(legalEntity.JurisdictionId.Value))
                    {
                        await SyncDirectorsAsync(legalEntity,
                                                 changedDirectorsByEntityCode[entityCode],
                                                 removedDirectorsByEntityCode[entityCode],
                                                 allDirectorsByUniqueCode);
                    }
                }
                else
                {
                    var notSyncedUpdates = changedDirectorsByEntityCode[entityCode];
                    var notSyncedDeletes = removedDirectorsByEntityCode[entityCode];
                    var updatedDirectorsCodes = string.Join(',', notSyncedUpdates.Select(dir => dir.ClientCode));
                    var deletedDirectorsCodes = string.Join(',', notSyncedDeletes.Select(dir => dir.ClientCode));
                    _logger.LogWarning("Entity {EntityCode} not found, " +
                                       "not updating directors: {NotSyncedDirectors}, " +
                                       "not deleting directors: {DeletedDirectorsCodes}",
                        entityCode, updatedDirectorsCodes, deletedDirectorsCodes);
                }
            }

            // Start a transaction for 'all or nothing'
            using var transaction = await _directorsRepository.DbContext.Database.BeginTransactionAsync();
            {
                try
                {
                    if (_directorsRepository.DbContext.Database.IsRelational())
                    {
                        _directorsRepository.DbContext.Database.SetCommandTimeout(300);
                    }

                    _logger.LogInformation("Start saving director data (in a Tx)...");

                    if (_directorsToInsert.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkInsert for {Count} directors...", _directorsToInsert.Count);
                        await _bulkOperationProvider.BulkInsertAsync(_directorsToInsert, _directorsRepository.DbContext);
                        _logger.LogInformation("BulkInsert finished");
                    }
                    else
                    {
                        _logger.LogInformation("No directors to insert");
                    }

                    if (_directorsToUpdate.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkUpdate for {Count} directors...", _directorsToUpdate.Count);
                        await _bulkOperationProvider.BulkUpdateAsync(_directorsToUpdate, _directorsRepository.DbContext);
                        _logger.LogInformation("BulkUpdate finished");
                    }
                    else
                    {
                        _logger.LogInformation("No directors to update");
                    }

                    if (_directorsToDelete.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkDelete for {Count} directors...", _directorsToDelete.Count);
                        await _bulkOperationProvider.BulkDeleteAsync(_directorsToDelete, _directorsRepository.DbContext);
                        _logger.LogInformation("BulkDelete finished");
                    }
                    else
                    {
                        _logger.LogInformation("No directors to delete");
                    }

                    if (_directorsHistoryToInsert.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkInsert for {Count} director history...", _directorsHistoryToInsert.Count);
                        await _bulkOperationProvider.BulkInsertAsync(_directorsHistoryToInsert, _directorHistoryRepository.DbContext);
                        _logger.LogInformation("BulkInsert finished");
                    }
                    else
                    {
                        _logger.LogInformation("No directorhistory to insert");
                    }

                    if (_directorsHistoryToDelete.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkDelete for {Count} director history...", _directorsHistoryToDelete.Count);
                        await _bulkOperationProvider.BulkDeleteAsync(_directorsHistoryToDelete, _directorsRepository.DbContext);
                        _logger.LogInformation("BulkDelete finished");
                    }
                    else
                    {
                        _logger.LogInformation("No director history to delete");
                    }

                    if (_syncMessagesToInsert.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkInsert for {Count} messages...", _syncMessagesToInsert.Count);
                        await _bulkOperationProvider.BulkInsertAsync(_syncMessagesToInsert, _directorsRepository.DbContext);
                        _logger.LogInformation("BulkInsert finished");
                    }
                    else
                    {
                        _logger.LogInformation("No messages to insert");
                    }

                    if (beforeCommitAsync != null)
                    {
                        await beforeCommitAsync(_directorsRepository.DbContext);
                    }

                    await transaction.CommitAsync();

                    _logger.LogInformation("Done saving data");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, "Error saving data (Tx rolled back)");
                    throw;
                }
                finally
                {
                    ClearBulkData();
                }
            }
        }

        private void ClearBulkData()
        {
            _directorsToInsert.Clear();
            _directorsToUpdate.Clear();
            _directorsToDelete.Clear();
            _directorsHistoryToInsert.Clear();
        }

        private async Task SyncDirectorsAsync(LegalEntity legalEntity,
                                              IList<SyncDirector> changedDirectorsSync,
                                              IList<SyncDirector> removedDirectorsSync,
                                              Dictionary<string, Director> allDirectorsByUniqueCode)
        {
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));
            ArgumentNullException.ThrowIfNull(changedDirectorsSync, nameof(changedDirectorsSync));
            ArgumentNullException.ThrowIfNull(removedDirectorsSync, nameof(removedDirectorsSync));

            // Get the current Directors for the LegalEntity if not passed.
            List<Director> currentDirectors = null;
            if (allDirectorsByUniqueCode == null)
            {
                currentDirectors = (await _directorsRepository.FindByConditionAsync(x => x.LegalEntityId == legalEntity.Id)).ToList();
            }

            // List for the Directors history for the LegalEntity.
            List<DirectorHistory> historyDirectors = null;

            var sw = new Stopwatch();
            sw.Start();

            var processedCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            // Get each Director from the request and compare to the current if any.
            // If ExternalUniqueId not in current then add one now and add a copy to history with status 'INITIAL'.
            // If exists in current but changed then add a record to history with the status from the last history ('REFRESHED' or 'VP DATA RECEIVED', if 'INITIAL' then 'REFRESHED').
            foreach (var syncDirector in changedDirectorsSync)
            {
                if (_jobLock != null && sw.Elapsed.TotalSeconds > 60)
                {
                    await _lockManager.RefreshLockAsync(_jobLock.Id.Value);
                    sw.Restart();
                }

                if (processedCodes.Contains(syncDirector.UniqueRelationId))
                {
                    //_logger.LogDebug("Duplicate UniqueRelationId {UniqueRelationId} (code {DirCode})", syncDirector.UniqueRelationId, syncDirector.Code);
                    continue;
                }

                processedCodes.Add(syncDirector.UniqueRelationId);

                // Get current and last history
                Director currentDirector = null;
                DirectorHistory lastDirectorHistory = null;
                if (allDirectorsByUniqueCode == null)
                {
                    currentDirector = currentDirectors.Where(x => x.ExternalUniqueId.Equals(syncDirector.UniqueRelationId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                }
                else
                {
                    allDirectorsByUniqueCode.TryGetValue(syncDirector.UniqueRelationId, out currentDirector);
                }

                if (currentDirector == null)
                {
                    //_logger.LogDebug("Creating new Director {DirectorCode}", syncDirector.Code);

                    // Initial creation of Director and DirectorHistory
                    currentDirector = UpsertDirector(legalEntity.Id, null, syncDirector);
                    CreateDirectorHistory(legalEntity.Id, syncDirector, LegalEntityRelationStatus.Initial, currentDirector);
                }
                else
                {
                    // First call?
                    if (historyDirectors == null)
                    {
                        historyDirectors = await _directorHistoryRepository.ListCurrentDirectorsByLegalEntityIdAsync(legalEntity.Id);
                    }

                    lastDirectorHistory = historyDirectors.Where(x => x.ExternalUniqueId.Equals(syncDirector.UniqueRelationId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();

                    // Should not happen but just in case...
                    bool forceUpsert = false;
                    if (lastDirectorHistory == null)
                    {
                        lastDirectorHistory = CreateDirectorHistory(legalEntity.Id, syncDirector, LegalEntityRelationStatus.Initial, currentDirector);
                        forceUpsert = true;
                    }

                    switch (lastDirectorHistory.Status)
                    {
                        // If last history has status 'INITIAL', 'REFRESHED' or 'VP DATA RECEIVED' and any change then create new history and update the BODirector.
                        case LegalEntityRelationStatus.Initial:
                        case LegalEntityRelationStatus.Refreshed:
                        case LegalEntityRelationStatus.UpdateReceived:
                            {
                                if (RelationChanged(lastDirectorHistory, syncDirector) ||
                                    MetaDataChanged(lastDirectorHistory, syncDirector))
                                {
                                    UpsertDirector(legalEntity.Id, currentDirector, syncDirector);

                                    var status = lastDirectorHistory.Status;
                                    if (status == LegalEntityRelationStatus.Initial)
                                    {
                                        status = LegalEntityRelationStatus.Refreshed;
                                    }

                                    CreateDirectorHistory(legalEntity.Id, syncDirector, status, currentDirector);
                                }
                                else if (forceUpsert)
                                {
                                    UpsertDirector(legalEntity.Id, currentDirector, syncDirector);
                                }

                                break;
                            }

                        // If last history has 'PendingUpdateRequest' or 'Confirmed' then set to 'UpdateReceived' if data changed
                        case LegalEntityRelationStatus.PendingUpdateRequest:
                        case LegalEntityRelationStatus.Confirmed:
                            {
                                if (RelationChanged(lastDirectorHistory, syncDirector))
                                {
                                    // Director data changed. Create a new history and set status to 'VP DATA RECEIVED'
                                    UpsertDirector(legalEntity.Id, currentDirector, syncDirector);
                                    CreateDirectorHistory(legalEntity.Id, syncDirector, LegalEntityRelationStatus.UpdateReceived, currentDirector);
                                }
                                else if (MetaDataChanged(lastDirectorHistory, syncDirector))
                                {
                                    // Only 'metadata' changed. Create a new history but keep the status
                                    UpsertDirector(legalEntity.Id, currentDirector, syncDirector);
                                    CreateDirectorHistory(legalEntity.Id, syncDirector, lastDirectorHistory.Status, currentDirector);
                                }

                                break;
                            }
                    }
                }
            }

            // Delete the Directors no longer in the sync, history is also removed
            // Do only when the entity is in the jurisdiction that we are syncing for
            if (_syncingJurisdictionIds.Contains(legalEntity.JurisdictionId.Value))
            {
                if (allDirectorsByUniqueCode != null && removedDirectorsSync != null)
                {
                    foreach (var bo in removedDirectorsSync)
                    {
                        if (allDirectorsByUniqueCode.TryGetValue(bo.UniqueRelationId, out var director))
                        {
                            _directorsToDelete.Add(director);

                            var history = await _directorHistoryRepository.FindByConditionAsync(dh => dh.ExternalUniqueId == bo.UniqueRelationId);
                            _directorsHistoryToDelete.AddRange(history);
                        }
                    }
                }
            }
        }

        #region Utils

        private static DirectorHistory Clone(DirectorHistory source)
        {
            ArgumentNullException.ThrowIfNull(source, nameof(source));

            var result = new DirectorHistory(Guid.NewGuid());
            source.CopyProperties(result);

            result.CreatedAt = DateTime.UtcNow;
            result.UpdatedAt = DateTime.UtcNow;

            return result;
        }

        /// <summary>
        /// Create the list of tokens for the email for an UpdateRequest and get info from the history entry.
        /// </summary>
        /// <param name="relation">The relation to add the tokens for.</param>
        /// <returns>The created tokenlist.</returns>
        private Framework.Messaging.Tokens.TokenList CreateTokens(DirectorHistory relation)
        {
            var result = new Framework.Messaging.Tokens.TokenList();

            result.Add("company.name", relation.LegalEntity.Name);
            result.Add("company.code", relation.LegalEntity.Code);

            result.Add("masterclient.code", relation.LegalEntity.MasterClient.Code);

            result.Add("masterfile.label", "Director");
            result.Add("masterfile.code", relation.ExternalUniqueId);

            result.Add("requestor", relation.UpdateRequestedByUser.GetDisplayName());

            result.Add("position", "Director");
            result.Add("request", relation.UpdateRequestType.Value.GetDisplayText());

            result.Add("comment", relation.UpdateRequestComments);

            return result;
        }

        /// <summary>
        /// Create the list of tokens for the email for an UpdateRequest.
        /// </summary>
        /// <param name="legalEntity">The entity (company) add the tokens for.</param>
        /// <param name="request">The request for assistance.</param>
        /// <returns>The created tokenlist.</returns>
        private Framework.Messaging.Tokens.TokenList CreateTokens(LegalEntity legalEntity, RequestAssistanceRequest request)
        {
            var result = new Framework.Messaging.Tokens.TokenList();

            result.Add("company.name", legalEntity.Name);
            result.Add("company.code", legalEntity.Code);

            result.Add("masterclient.code", legalEntity.MasterClient.Code);

            result.Add("requestor", _workContext.User?.DisplayName);
            result.Add("request", request.AssistanceRequestType.GetDisplayText());

            result.Add("position", "Director");

            result.Add("comment", request.AssistanceRequestComments);

            return result;
        }

        /// <summary>
        /// Returns true if any of the Director fields does not match.
        /// </summary>
        /// <param name="current">The current DirectorHistory.</param>
        /// <param name="syncDirector">The importing syncDirector.</param>
        /// <returns>True if at least 1 DirectorHistory field changed.</returns>
        private static bool RelationChanged(DirectorHistory current, SyncDirector syncDirector)
        {
            var isIndividual = syncDirector.FileType.Equals("individual", StringComparison.OrdinalIgnoreCase);

            if (CompareHelper.ValueChanged(current.Code, syncDirector.Code) ||
                CompareHelper.ValueChanged(current.Name, syncDirector.Name) ||
                CompareHelper.ValueChanged(current.FormerName, syncDirector.FormerName) ||
                CompareHelper.ValueChanged(current.Nationality, syncDirector.Nationality) ||
                CompareHelper.ValueChanged(current.TIN, syncDirector.TIN) ||
                CompareHelper.ValueChanged(current.RelationType, syncDirector.RelationType) ||
                CompareHelper.ValueChanged(current.OfficerTypeName, syncDirector.OfficerTypeName) ||

                CompareHelper.ValueChanged(current.Country, syncDirector.Country) ||

                CompareHelper.ValueChanged(current.AppointmentDate, syncDirector.FromDate) ||
                CompareHelper.ValueChanged(current.CessationDate, syncDirector.ToDate) ||

                CompareHelper.ValueChanged(current.ServiceAddress, syncDirector.ServiceAddress) ||

                CompareHelper.ValueChanged(current.FileType, syncDirector.FileType) ||
                CompareHelper.ValueChanged(current.IsIndividual, isIndividual) ||

                CompareHelper.ValueChanged(current.VPDirectorID, syncDirector.DirectorID) ||
                CompareHelper.ValueChanged(current.DirectorCapacity, syncDirector.DirectorCapacity) ||
                CompareHelper.ValueChanged(current.LicenseeEntityCode, syncDirector.LicenseeEntityCode) ||
                CompareHelper.ValueChanged(current.LicenseeEntityName, syncDirector.LicenseeEntityName))

            {
                return true;
            }

            if (isIndividual)
            {
                if (CompareHelper.ValueChanged(current.DateOfBirth.AsNullable(), syncDirector.DateOfBirthOrIncorp.AsNullable()) ||
                    CompareHelper.ValueChanged(current.PlaceOfBirth, syncDirector.PlaceOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.CountryOfBirth, syncDirector.CountryOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.CountryOfBirthCode, syncDirector.CountryCodeOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.ResidentialAddress, syncDirector.ResidentialOrRegisteredAddress))
                {
                    return true;
                }
            }
            else
            {
                if (CompareHelper.ValueChanged(current.IncorporationDate.AsNullable(), syncDirector.DateOfBirthOrIncorp.AsNullable()) ||
                    CompareHelper.ValueChanged(current.IncorporationPlace, syncDirector.PlaceOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.IncorporationCountry, syncDirector.CountryOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.IncorporationCountryCode, syncDirector.CountryCodeOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.IncorporationNr, syncDirector.IncorporationNumberOrPassportNr) ||
                    CompareHelper.ValueChanged(current.Address, syncDirector.ResidentialOrRegisteredAddress))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Returns true if any of the MetaData fields does not match.
        /// </summary>
        /// <param name="current">The current Director.</param>
        /// <param name="syncDirector">The importing SyncDirector.</param>
        /// <returns>True if at least 1 MetData field changed.</returns>
        private static bool MetaDataChanged(DirectorHistory current, SyncDirector syncDirector)
        {
            //if (CompareHelper.ValueChanged(current.ma, importBODirector.MasterClientCode) ||
            //    CompareHelper.ValueChanged(current.CompanyNumber, importBODirector.CompanyNumber) ||
            //    CompareHelper.ValueChanged(current.EntityCode, importBODirector.EntityCode) ||
            //    CompareHelper.ValueChanged(current.EntityName, importBODirector.EntityName) ||
            //    CompareHelper.ValueChanged(current.Code, importBODirector.Code))
            //{
            //    return true;
            //}

            return false;
        }

        /// <summary>
        /// Creates an entry in DirectorHistory based on the importing SyncDirector.
        /// </summary>
        /// <param name="legalEntityId"></param>
        /// <param name="syncDirector">The importing SyncDirector.</param>
        /// <param name="status">The status to give to the history.</param>
        /// <param name="director"></param>
        /// <returns>The created DirectorHistory.</returns>
        private DirectorHistory CreateDirectorHistory(Guid legalEntityId, SyncDirector syncDirector, LegalEntityRelationStatus status, Director director)
        {
            var result = new DirectorHistory
            {
                ReceivedAt = DateTime.UtcNow,

                LegalEntityId = legalEntityId,
                ExternalUniqueId = syncDirector.UniqueRelationId,

                Status = status,

                Name = syncDirector.Name == null ? string.Empty : syncDirector.Name,
                FormerName = syncDirector.FormerName,

                Code = syncDirector.Code,
                CompanyNumber = syncDirector.CompanyNumber,

                Nationality = syncDirector.Nationality,
                Country = syncDirector.Country,
                ServiceAddress = syncDirector.ServiceAddress,

                TIN = syncDirector.TIN,

                RelationType = syncDirector.RelationType,
                OfficerTypeName = syncDirector.OfficerTypeName,
                AppointmentDate = syncDirector.FromDate,
                CessationDate = syncDirector.ToDate,

                DirectorIsAlternateToCode = syncDirector.DirectorIsAlternateToCode,
                DirectorId = director.Id == Guid.Empty ? null : director.Id,

                FileType = syncDirector.FileType,
                IsIndividual = syncDirector.FileType.Equals("individual", StringComparison.OrdinalIgnoreCase),

                DirectorCapacity = syncDirector.DirectorCapacity,
                VPDirectorID = syncDirector.DirectorID,
                LicenseeEntityCode = syncDirector.LicenseeEntityCode,
                LicenseeEntityName = syncDirector.LicenseeEntityName
            };

            if (result.IsIndividual)
            {
                result.DateOfBirth = syncDirector.DateOfBirthOrIncorp.AsNullable();
                result.PlaceOfBirth = syncDirector.PlaceOfBirthOrIncorp;
                result.CountryOfBirth = syncDirector.CountryOfBirthOrIncorp;
                result.CountryOfBirthCode = syncDirector.CountryCodeOfBirthOrIncorp;
                result.ResidentialAddress = syncDirector.ResidentialOrRegisteredAddress;
            }
            else
            {
                result.IncorporationDate = syncDirector.DateOfBirthOrIncorp.AsNullable();
                result.IncorporationPlace = syncDirector.PlaceOfBirthOrIncorp;
                result.IncorporationCountry = syncDirector.CountryOfBirthOrIncorp;
                result.IncorporationCountryCode = syncDirector.CountryCodeOfBirthOrIncorp;
                result.IncorporationNr = syncDirector.IncorporationNumberOrPassportNr;
                result.Address = syncDirector.ResidentialOrRegisteredAddress;
            }

            _directorsHistoryToInsert.Add(result);

            return result;
        }

        /// <summary>
        /// Updates the current Director with the importing data.
        /// </summary>
        /// <param name="currentDirector">The current Director. Can be null, then it is created.</param>
        /// <param name="syncDirector">The importing Director.</param>
        /// <returns>The updated or created BODirector.</returns>
        private Director UpsertDirector(Guid legalEnityId, Director currentDirector, SyncDirector syncDirector)
        {
            bool isNew = false;
            if (currentDirector == null)
            {
                currentDirector = new Director(Guid.NewGuid());
                currentDirector.LegalEntityId = legalEnityId;
                currentDirector.ExternalUniqueId = syncDirector.UniqueRelationId;
                isNew = true;
            }

            currentDirector.Name = syncDirector.Name == null ? string.Empty : syncDirector.Name;
            currentDirector.FormerName = syncDirector.FormerName;
            currentDirector.Code = syncDirector.Code;

            currentDirector.CompanyNumber = syncDirector.CompanyNumber;

            currentDirector.TIN = syncDirector.TIN;

            currentDirector.RelationType = syncDirector.RelationType;
            currentDirector.OfficerTypeName = syncDirector.OfficerTypeName;
            currentDirector.AppointmentDate = syncDirector.FromDate.AsNullable();
            currentDirector.CessationDate = syncDirector.ToDate.AsNullable();
            currentDirector.Nationality = syncDirector.Nationality;
            currentDirector.ServiceAddress = syncDirector.ServiceAddress;

            currentDirector.FileType = syncDirector.FileType;
            currentDirector.IsIndividual = syncDirector.FileType.Equals("individual", StringComparison.OrdinalIgnoreCase);

            currentDirector.DirectorCapacity = syncDirector.DirectorCapacity;
            currentDirector.DirectorID = syncDirector.DirectorID;
            currentDirector.LicenseeEntityCode = syncDirector.LicenseeEntityCode;
            currentDirector.LicenseeEntityName = syncDirector.LicenseeEntityName;

            if (currentDirector.IsIndividual)
            {
                currentDirector.DateOfBirth = syncDirector.DateOfBirthOrIncorp.AsNullable();
                currentDirector.PlaceOfBirth = syncDirector.PlaceOfBirthOrIncorp;
                currentDirector.CountryOfBirth = syncDirector.CountryOfBirthOrIncorp;
                currentDirector.CountryOfBirthCode = syncDirector.CountryCodeOfBirthOrIncorp;
                currentDirector.ResidentialAddress = syncDirector.ResidentialOrRegisteredAddress;
            }
            else
            {
                currentDirector.IncorporationDate = syncDirector.DateOfBirthOrIncorp.AsNullable();
                currentDirector.IncorporationPlace = syncDirector.PlaceOfBirthOrIncorp;
                currentDirector.IncorporationCountry = syncDirector.CountryOfBirthOrIncorp;
                currentDirector.IncorporationCountryCode = syncDirector.CountryCodeOfBirthOrIncorp;
                currentDirector.IncorporationNr = syncDirector.IncorporationNumberOrPassportNr;
                currentDirector.Address = syncDirector.ResidentialOrRegisteredAddress;
            }

            if (isNew)
            {
                _directorsToInsert.Add(currentDirector);
            }
            else
            {
                _directorsToUpdate.Add(currentDirector);
            }

            return currentDirector;
        }

        /// <summary>
        /// Gets the list of jurisdictions that we are syncing for.
        /// </summary>
        /// <returns></returns>
        private async Task<List<Guid>> GetSyncingJurisdictionIdsAsync()
        {
            var pcpCodes = SyncHelper.JurisdictionCodes.Select(c => CodeConverter.ViewPointCodeToPCPCode(c));
            return (await _jurisdictionsRepository.FindByConditionAsync(j => pcpCodes.Contains(j.Code))).Select(j => j.Id).ToList();
        }

        #endregion

        #region Data Validation

        private static void CheckMissingInformation(DirectorDTO director)
        {
            if (director.IsIndividual)
            {
                // Individual
                CheckMissing(director, director.DirectorType, nameof(director.DirectorType));
                CheckMissing(director, director.Name, nameof(director.Name));
                CheckMissing(director, director.AppointmentDate, nameof(director.AppointmentDate));
                CheckMissing(director, director.ResidentialAddress, nameof(director.ResidentialAddress));
                CheckMissing(director, director.DateOfBirth, nameof(director.DateOfBirth));
                CheckMissing(director, director.CountryOfBirth, nameof(director.CountryOfBirth));
                CheckMissing(director, director.Nationality, nameof(director.Nationality));
            }
            else
            {
                // Company
                CheckMissing(director, director.DirectorType, nameof(director.DirectorType));
                CheckMissing(director, director.Name, nameof(director.Name));
                CheckMissing(director, director.IncorporationNumber, nameof(director.IncorporationNumber));
                CheckMissing(director, director.AppointmentDate, nameof(director.AppointmentDate));
                CheckMissing(director, director.Address, nameof(director.Address));
                CheckMissing(director, director.DateOfIncorporation, nameof(director.DateOfIncorporation));
                CheckMissing(director, director.IncorporationCountry, nameof(director.IncorporationCountry));
            }
        }


        private static void CheckMissing(DirectorDTO model, string value, string fieldName)
        {
            if (string.IsNullOrEmpty(value))
            {
                if (model.MetaData == null)
                {
                    model.MetaData = new LegalEntityRelationMetaData();
                }

                model.MetaData.MissingDataFields.Add(fieldName.ToCamelCase());
            }
        }

        private static void CheckMissing(DirectorDTO model, DateTime? value, string fieldName)
        {
            if (!value.HasValue || value.Value == DateTime.MinValue)
            {
                if (model.MetaData == null)
                {
                    model.MetaData = new LegalEntityRelationMetaData();
                }

                model.MetaData.MissingDataFields.Add(fieldName.ToCamelCase());
            }
        }

        #endregion
    }
}
