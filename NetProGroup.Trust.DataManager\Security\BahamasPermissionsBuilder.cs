﻿// <copyright file="BahamasPermissionsBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Builder for permissions for Panama.
    /// </summary>
    public class BahamasPermissionsBuilder : PermissionsBuilderBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BahamasPermissionsBuilder"/> class.
        /// </summary>
        public BahamasPermissionsBuilder()
        {
            SetupCompanyPermissions();
            SetupBODIRPermissions();
            SetupAnnouncementPermissions();
            SetupEconomicSubstancePermissions();
            SetupStatusPermissions();
        }

        /// <summary>
        /// Setup the company permissions for bahamas roles.
        /// </summary>
        private void SetupCompanyPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Bahamas_Owner,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.ESBahamasModule_Companies_View_Custom_ES_Fee,
                WellKnownPermissionNames.ESBahamasModule_Companies_Set_Custom_ES_Fee,
                WellKnownPermissionNames.Companies_View_Log,
                WellKnownPermissionNames.Companies_Move_Delete_Submissions,
                WellKnownPermissionNames.Companies_Set_Back_To_Onboarding_No_Submissions);

            SetupPermissions(WellKnownRoleNames.Bahamas_CMU_SuperUser,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.ESBahamasModule_Companies_View_Custom_ES_Fee,
                WellKnownPermissionNames.ESBahamasModule_Companies_Set_Custom_ES_Fee,
                WellKnownPermissionNames.Companies_View_Log,
                WellKnownPermissionNames.Companies_Set_Back_To_Onboarding_No_Submissions);

            SetupPermissions(WellKnownRoleNames.Bahamas_Basic_User,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules);
        }

        /// <summary>
        /// Setup the BODIR permissions for bahamas roles.
        /// </summary>
        private void SetupBODIRPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Bahamas_Owner,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);

            SetupPermissions(WellKnownRoleNames.Bahamas_Officers_SuperUser,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);
        }

        /// <summary>
        /// Setup the announcement permissions for bahamas roles.
        /// </summary>
        private void SetupAnnouncementPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Bahamas_Owner,
                WellKnownPermissionNames.AnnouncementModule_Search,
                WellKnownPermissionNames.AnnouncementModule_View,
                WellKnownPermissionNames.AnnouncementModule_Delete,
                WellKnownPermissionNames.AnnouncementModule_Create_Limited);

            SetupPermissions(WellKnownRoleNames.Bahamas_COM_SuperUser,
               WellKnownPermissionNames.AnnouncementModule_Search,
               WellKnownPermissionNames.AnnouncementModule_View,
               WellKnownPermissionNames.AnnouncementModule_Delete);
        }

        /// <summary>
        /// Setup the Economic Substance permissions for bahamas roles.
        /// </summary>
        private void SetupEconomicSubstancePermissions()
        {
            SetupPermissions(WellKnownRoleNames.Bahamas_Owner,
                WellKnownPermissionNames.ESBahamasModule_Submissions_View,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Search,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Export,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Reset,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Delete_Completed,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.ESBahamasModule_Submissions_View_Paid,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.ESBahamasModule_Payments_Import,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Export_ITA,
                WellKnownPermissionNames.ESBahamasModule_View_RFI,
                WellKnownPermissionNames.ESBahamasModule_Start_RFI_Request,
                WellKnownPermissionNames.ESBahamasModule_Cancel_RFI_Request,
                WellKnownPermissionNames.ESBahamasModule_Set_Financial_Period);

            SetupPermissions(WellKnownRoleNames.Bahamas_ES_SuperUser,
                WellKnownPermissionNames.ESBahamasModule_Submissions_View,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Search,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Export,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Reset,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Delete_Completed,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.ESBahamasModule_Submissions_View_Paid,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.ESBahamasModule_Payments_Import,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Export_ITA,
                WellKnownPermissionNames.ESBahamasModule_View_RFI,
                WellKnownPermissionNames.ESBahamasModule_Start_RFI_Request,
                WellKnownPermissionNames.ESBahamasModule_Cancel_RFI_Request,
                WellKnownPermissionNames.ESBahamasModule_Set_Financial_Period);

            SetupPermissions(WellKnownRoleNames.Bahamas_Basic_User,
                WellKnownPermissionNames.ESBahamasModule_Submissions_View,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Search,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Export,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.ESBahamasModule_Submissions_View_Paid,
                WellKnownPermissionNames.ESBahamasModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.ESBahamasModule_View_RFI,
                WellKnownPermissionNames.ESBahamasModule_Start_RFI_Request);
        }

        /// <summary>
        /// Setup the status permissions for bahamas roles.
        /// </summary>
        private void SetupStatusPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Bahamas_Owner,
                WellKnownPermissionNames.General_Status_Page_Sync);
        }
    }
}
