﻿// <copyright file="NevisPermissionsBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Roles;
namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Builder for permissions for Nevis.
    /// </summary>
    public class CommonPermissionsBuilder : PermissionsBuilderBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CommonPermissionsBuilder"/> class.
        /// </summary>
        public CommonPermissionsBuilder()
        {
            SetupMasterClientPermissions();
            SetupUsersPermissions();
            SetupAnnouncementPermissions();
        }

        private void SetupMasterClientPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Common_BasicUser,
                WellKnownPermissionNames.MasterClients_Search,
                WellKnownPermissionNames.MasterClients_View,
                WellKnownPermissionNames.MasterClients_View_Log,
                WellKnownPermissionNames.MasterClients_View_Trident_Users);

            SetupPermissions(WellKnownRoleNames.Common_CMU_SuperUser,
                WellKnownPermissionNames.MasterClients_Search,
                WellKnownPermissionNames.MasterClients_View,
                WellKnownPermissionNames.MasterClients_View_Log,
                WellKnownPermissionNames.MasterClients_View_Trident_Users);

            SetupPermissions(WellKnownRoleNames.Common_SupportUser,
                WellKnownPermissionNames.MasterClients_Search,
                WellKnownPermissionNames.MasterClients_View,
                WellKnownPermissionNames.MasterClients_View_Log,
                WellKnownPermissionNames.MasterClients_SendInvitation,
                WellKnownPermissionNames.MasterClients_View_Trident_Users,
                WellKnownPermissionNames.MasterClients_Add_Trident_Users,
                WellKnownPermissionNames.MasterClients_Remove_Trident_Users);
        }

        private void SetupUsersPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Common_CMU_SuperUser,
                WellKnownPermissionNames.Users_Search,
                WellKnownPermissionNames.Users_View,
                WellKnownPermissionNames.Users_Block,
                WellKnownPermissionNames.Users_ViewLog);

            SetupPermissions(WellKnownRoleNames.Common_SupportUser,
                WellKnownPermissionNames.Users_Search,
                WellKnownPermissionNames.Users_View,
                WellKnownPermissionNames.Users_Block,
                WellKnownPermissionNames.Users_Unblock,
                WellKnownPermissionNames.Users_ResetAuthentication,
                WellKnownPermissionNames.Users_ViewLog);
        }

        private void SetupAnnouncementPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Common_COM_Owner,
                WellKnownPermissionNames.AnnouncementModule_View,
                WellKnownPermissionNames.AnnouncementModule_Search,
                WellKnownPermissionNames.AnnouncementModule_Delete,
                WellKnownPermissionNames.AnnouncementModule_Create_Limited,
                WellKnownPermissionNames.AnnouncementModule_Create);
        }
    }
}
