﻿// <copyright file="NevisPermissionsBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Builder for permissions for Nevis.
    /// </summary>
    public class NevisPermissionsBuilder : PermissionsBuilderBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="NevisPermissionsBuilder"/> class.
        /// </summary>
        public NevisPermissionsBuilder()
        {
            SetupCompanyPermissions();
            SetupBODIRPermissions();
            SetupAnnouncementPermissions();
            SetupStrSubmissionPermissions();
            SetupStatusPermissions();
        }

        private void SetupCompanyPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Nevis_Owner,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Late_Payment_Exemption_STR,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.Companies_View_Custom_STR_Fee,
                WellKnownPermissionNames.Companies_Set_Custom_STR_Fee,
                WellKnownPermissionNames.Companies_View_Log,
                WellKnownPermissionNames.Companies_View_First_Submission_Year,
                WellKnownPermissionNames.Companies_Set_First_Submission_Year,
                WellKnownPermissionNames.Companies_Move_Delete_Submissions,
                WellKnownPermissionNames.Companies_Set_Back_To_Onboarding_No_Submissions,
                WellKnownPermissionNames.Companies_View_STR_Submission_Log);

            SetupPermissions(WellKnownRoleNames.Nevis_CMU_SuperUser,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Approve_Onboarding,
                WellKnownPermissionNames.Companies_Reject_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Available_Modules,
                WellKnownPermissionNames.Companies_Set_Late_Payment_Exemption_STR,
                WellKnownPermissionNames.Companies_View_Annual_Fee,
                WellKnownPermissionNames.Companies_Set_Annual_Fee,
                WellKnownPermissionNames.Companies_View_Custom_STR_Fee,
                WellKnownPermissionNames.Companies_Set_Custom_STR_Fee,
                WellKnownPermissionNames.Companies_View_Log,
                WellKnownPermissionNames.Companies_View_First_Submission_Year,
                WellKnownPermissionNames.Companies_Set_First_Submission_Year,
                WellKnownPermissionNames.Companies_Set_Back_To_Onboarding_No_Submissions,
                WellKnownPermissionNames.Companies_View_STR_Submission_Log);

            SetupPermissions(WellKnownRoleNames.Nevis_STR_SuperUser,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_View_Custom_STR_Fee,
                WellKnownPermissionNames.Companies_Set_Custom_STR_Fee,
                WellKnownPermissionNames.Companies_View_First_Submission_Year,
                WellKnownPermissionNames.Companies_Set_First_Submission_Year,
                WellKnownPermissionNames.Companies_View_STR_Submission_Log);

            SetupPermissions(WellKnownRoleNames.Nevis_Basic_User,
                WellKnownPermissionNames.Companies_Access_Onboarding,
                WellKnownPermissionNames.Companies_Search,
                WellKnownPermissionNames.Companies_View,
                WellKnownPermissionNames.Companies_View_Available_Modules,
                WellKnownPermissionNames.Companies_View_First_Submission_Year,
                WellKnownPermissionNames.Companies_View_Custom_STR_Fee);
        }

        private void SetupBODIRPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Nevis_Owner,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);

            SetupPermissions(WellKnownRoleNames.Nevis_Officers_SuperUser,
                WellKnownPermissionNames.BODIRModule_Search,
                WellKnownPermissionNames.BODIRModule_View,
                WellKnownPermissionNames.BODIRModule_Export);
        }

        private void SetupAnnouncementPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Nevis_Owner,
                WellKnownPermissionNames.AnnouncementModule_Search,
                WellKnownPermissionNames.AnnouncementModule_View,
                WellKnownPermissionNames.AnnouncementModule_Delete,
                WellKnownPermissionNames.AnnouncementModule_Create_Limited);

            SetupPermissions(WellKnownRoleNames.Nevis_COM_SuperUser,
               WellKnownPermissionNames.AnnouncementModule_Search,
               WellKnownPermissionNames.AnnouncementModule_View,
               WellKnownPermissionNames.AnnouncementModule_Delete);
        }

        private void SetupStrSubmissionPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Nevis_Owner,
                WellKnownPermissionNames.STRModule_Submissions_View,
                WellKnownPermissionNames.STRModule_Submissions_Search,
                WellKnownPermissionNames.STRModule_Submissions_Export,
                WellKnownPermissionNames.STRModule_Submissions_Reset,
                WellKnownPermissionNames.STRModule_Submissions_Delete_Completed,
                WellKnownPermissionNames.STRModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.STRModule_Submissions_View_Paid,
                WellKnownPermissionNames.STRModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.STRModule_Payments_Import,
                WellKnownPermissionNames.STRModule_Submissions_Export_IRD,
                WellKnownPermissionNames.STRModule_Invoices_Export,
                WellKnownPermissionNames.STRModule_Invoices_Export_Financial,
                WellKnownPermissionNames.STRModule_Management_Information,
                WellKnownPermissionNames.STRModule_View_RFI,
                WellKnownPermissionNames.STRModule_Start_RFI_Request,
                WellKnownPermissionNames.STRModule_Cancel_RFI_Request,
                WellKnownPermissionNames.STRModule_View_Fee,
                WellKnownPermissionNames.STRModule_Set_Fee,
                WellKnownPermissionNames.STRModule_Set_Late_Payments,
                WellKnownPermissionNames.STRModule_View_Late_Payments,
                WellKnownPermissionNames.STRModule_DataMigration,
                WellKnownPermissionNames.STRModule_Edit_Late_Payments,
                WellKnownPermissionNames.STRModule_View_Submission_Log);

            SetupPermissions(WellKnownRoleNames.Nevis_CMU_SuperUser,
                WellKnownPermissionNames.STRModule_View_Submission_Log);

            SetupPermissions(WellKnownRoleNames.Nevis_STR_SuperUser,
                 WellKnownPermissionNames.STRModule_Submissions_View,
                WellKnownPermissionNames.STRModule_Submissions_Search,
                WellKnownPermissionNames.STRModule_Submissions_Export,
                WellKnownPermissionNames.STRModule_Submissions_Reset,
                WellKnownPermissionNames.STRModule_Submissions_Delete_Completed,
                WellKnownPermissionNames.STRModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.STRModule_Submissions_View_Paid,
                WellKnownPermissionNames.STRModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.STRModule_Payments_Import,
                WellKnownPermissionNames.STRModule_Submissions_Export_IRD,
                WellKnownPermissionNames.STRModule_Invoices_Export,
                WellKnownPermissionNames.STRModule_Invoices_Export_Financial,
                WellKnownPermissionNames.STRModule_Management_Information,
                WellKnownPermissionNames.STRModule_View_RFI,
                WellKnownPermissionNames.STRModule_Start_RFI_Request,
                WellKnownPermissionNames.STRModule_Cancel_RFI_Request,
                WellKnownPermissionNames.STRModule_View_Fee,
                WellKnownPermissionNames.STRModule_Set_Fee,
                WellKnownPermissionNames.STRModule_Set_Late_Payments,
                WellKnownPermissionNames.STRModule_View_Late_Payments,
                WellKnownPermissionNames.STRModule_Edit_Late_Payments,
                WellKnownPermissionNames.STRModule_View_Submission_Log);

            SetupPermissions(WellKnownRoleNames.Nevis_Basic_User,
                WellKnownPermissionNames.STRModule_Submissions_View,
                WellKnownPermissionNames.STRModule_Submissions_Search,
                WellKnownPermissionNames.STRModule_Submissions_Export,
                WellKnownPermissionNames.STRModule_Invoices_Export,
                WellKnownPermissionNames.STRModule_Submissions_Delete_Saved,
                WellKnownPermissionNames.STRModule_Submissions_View_Paid,
                WellKnownPermissionNames.STRModule_Submissions_Mark_Paid,
                WellKnownPermissionNames.STRModule_Start_RFI_Request,
                WellKnownPermissionNames.STRModule_View_RFI,
                WellKnownPermissionNames.STRModule_View_Late_Payments,
                WellKnownPermissionNames.STRModule_View_Fee,
                WellKnownPermissionNames.STRModule_View_Late_Payments);
        }

        /// <summary>
        /// Setup the permissions for the status page in Nevis.
        /// </summary>
        private void SetupStatusPermissions()
        {
            SetupPermissions(WellKnownRoleNames.Nevis_Owner,
                WellKnownPermissionNames.General_Status_Page_Sync);
        }
    }
}
