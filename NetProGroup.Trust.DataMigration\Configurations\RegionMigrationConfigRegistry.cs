using NetProGroup.Trust.DataMigration.Models;
using NetProGroup.Trust.DataMigration.Models.Nevis;
using NetProGroup.Trust.DataMigration.Services.Nevis;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.DataMigration.Configurations
{
    /// <summary>
    /// Registry for region-specific migration configurations.
    /// These configurations define the migration steps for different jurisdictions.
    /// These steps include the entities to be migrated, their collection names,
    /// display names, and the services that will handle the migrations.
    /// </summary>
    public static class RegionMigrationConfigRegistry
    {
        /// <summary>
        /// Dictionary containing migration steps for different jurisdictions.
        /// </summary>
        public static Dictionary<string, IEnumerable<EntityMigrationStep>> MigrationSteps { get; } = new()
        {
            {
                JurisdictionCodes.Nevis,
                new List<EntityMigrationStep>
                {
                    new(
                        typeof(NevisInvoiceConfiguration),
                        MigrationConsts.NevisInvoiceConfigurations.CollectionName,
                        MigrationConsts.NevisInvoiceConfigurations.DisplayName,
                        typeof(InvoiceConfigurationsMigrationService)),
                    new(
                        typeof(Entry),
                        MigrationConsts.Entries.CollectionName,
                        MigrationConsts.Entries.DisplayName,
                        typeof(EntryMigrationServiceWrapper)),
                    new(
                        typeof(Company),
                        MigrationConsts.Companies.CollectionName,
                        MigrationConsts.Companies.DisplayName,
                        typeof(CompanyMigrationServiceWrapper))
                }
            },
            {
                JurisdictionCodes.Bahamas,
                new List<EntityMigrationStep>()
            }
        };
    }
}