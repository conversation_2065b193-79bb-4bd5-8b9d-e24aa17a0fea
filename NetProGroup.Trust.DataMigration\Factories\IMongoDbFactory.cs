// <copyright file="IMongoDbFactory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using MongoDB.Driver;

namespace NetProGroup.Trust.DataMigration.Factories
{
    /// <summary>
    /// Factory interface for creating MongoDB clients and databases based on region.
    /// </summary>
    public interface IMongoDbFactory
    {
        /// <summary>
        /// Gets a MongoDB client for the specified region.
        /// </summary>
        /// <param name="region">The region name (jurisdiction name).</param>
        /// <returns>The MongoDB client for the specified region.</returns>
        IMongoClient GetMongoClient(string region);

        /// <summary>
        /// Gets a MongoDB database for the specified region.
        /// </summary>
        /// <param name="region">The region name (jurisdiction name).</param>
        /// <returns>The MongoDB database for the specified region.</returns>
        IMongoDatabase GetMongoDatabase(string region);
    }
}
