namespace NetProGroup.Trust.DataMigration.Models
{
    /// <summary>
    /// Represents a migration step for an entity for a specific jurisdiction. 
    /// </summary>
    /// <param name="entityType">The type of the entity to be migrated.</param>
    /// <param name="collectionName">The name of the collection where the entity is stored.</param>
    /// <param name="displayName">A user-friendly name for the entity type.</param>
    /// <param name="migrationServiceType">The type of the service that will handle the migration for this entity.</param>
    public class EntityMigrationStep(
        Type entityType,
        string collectionName,
        string displayName,
        Type migrationServiceType)
    {
        public Type EntityType { get; } = entityType;
        public string CollectionName { get; } = collectionName;
        public string DisplayName { get; } = displayName;
        public Type MigrationServiceType { get; } = migrationServiceType;
    }
}