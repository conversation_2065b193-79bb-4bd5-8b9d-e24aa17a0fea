﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="MongoDB.Driver" Version="2.19.2" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.DataManager\NetProGroup.Trust.DataManager.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Domain\NetProGroup.Trust.Domain.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Repository\NetProGroup.Trust.Domain.Repository.csproj" />
  </ItemGroup>

</Project>
