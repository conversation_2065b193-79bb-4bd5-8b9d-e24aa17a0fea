using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Framework.Services.ActivityLogs.Services;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for managing activity logs during data migration.
    /// </summary>
    public class ActivityLogMigrationService
    {
        private readonly IActivityLogManager _activityLogManager;
        private readonly IActivityLogRepository _activityLogRepository;
        private readonly IWorkContext _workContext;
        private readonly ILogger<ActivityLogMigrationService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActivityLogMigrationService"/> class.
        /// </summary>
        /// <param name="activityLogManager">The activity log manager.</param>
        /// <param name="activityLogRepository">The activity log repository.</param>
        /// <param name="workContext">The work context.</param>
        /// <param name="logger">The logger instance.</param>
        public ActivityLogMigrationService(
            IActivityLogManager activityLogManager,
            IActivityLogRepository activityLogRepository,
            IWorkContext workContext,
            ILogger<ActivityLogMigrationService> logger)
        {
            _activityLogManager = activityLogManager ?? throw new ArgumentNullException(nameof(activityLogManager));
            _activityLogRepository = activityLogRepository ?? throw new ArgumentNullException(nameof(activityLogRepository));
            _workContext = workContext ?? throw new ArgumentNullException(nameof(workContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates migration activity logs for submission, invoice, and payment.
        /// </summary>
        /// <param name="entry">The entry containing migration data.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="submission">The submission being migrated.</param>
        /// <param name="invoice">The invoice being migrated (if any).</param>
        /// <param name="payment">The payment being migrated (if any).</param>
        /// <param name="financialYear">The financial year of the submission</param>
        public async Task CreateMigrationActivityLogs(Entry entry, ApplicationUser migrationStartedByUser, string region,
            Submission submission, Invoice invoice, Domain.Payments.Payment payment, int financialYear)
        {
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            _logger.LogTrace("Creating migration activity logs");

            await _activityLogManager.AddActivityLogAsync(
                migrationStartedByUser.Id,
                submission,
                shortDescription: "Submission migrated",
                text: $"Submission '{submission.Name}' migrated from {region} by user '{migrationStartedByUser.DisplayName}' at {DateTime.UtcNow:yyyy-MM-dd HH:mm}{(financialYear >= 2024 ? " (no invoice created because financial year is 2024 or later)" : "")}",
                activityType: ActivityLogActivityTypes.SubmissionMigrated);

            if (invoice != null)
            {
                await _activityLogManager.AddActivityLogAsync(
                    migrationStartedByUser.Id,
                    invoice,
                    shortDescription: "Invoice migrated",
                    text: $"Invoice '{invoice.InvoiceNr}' migrated from {region} by user '{migrationStartedByUser.DisplayName}' at {DateTime.UtcNow:yyyy-MM-dd HH:mm}",
                    activityType: ActivityLogActivityTypes.InvoiceMigrated);
            }

            if (payment != null)
            {
                await _activityLogManager.AddActivityLogAsync(
                    migrationStartedByUser.Id,
                    payment,
                    shortDescription: "Payment migrated",
                    text: $"Payment '{payment.Reference}' migrated from {region} by user '{migrationStartedByUser.DisplayName}' at {DateTime.UtcNow:yyyy-MM-dd HH:mm}",
                    activityType: ActivityLogActivityTypes.PaymentMigrated);
            }

            await AddReopenedActivityLogIfNeeded(entry, migrationStartedByUser, submission);
        }

        /// <summary>
        /// Adds reopened activity log if needed.
        /// </summary>
        /// <param name="entry">The entry containing reopened data.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="submission">The submission being migrated.</param>
        private async Task AddReopenedActivityLogIfNeeded(Entry entry, ApplicationUser migrationStartedByUser, Submission submission)
        {
            if (entry.Reopened != null)
            {
                _logger.LogTrace("Adding reopened activity logs");

                foreach (var reopenedDetail in entry.Reopened.Details)
                {
                    var dateReopened = reopenedDetail.DateReopened;
                    var reopenedBy = reopenedDetail.ReopenedBy;

                    var reopenedActivityLog = await _activityLogRepository.FindFirstOrDefaultByConditionAsync(a =>
                        a.EntityId == submission.Id && a.ActivityType == ActivityLogActivityTypes.SubmissionReopened &&
                        a.CreatedAt == dateReopened);

                    var text = $"Submission '{submission.Name}' reopened by user with name '{reopenedBy}' at {dateReopened:yyyy-MM-dd HH:mm}";

                    if (reopenedActivityLog == null)
                    {
                        _logger.LogDebug("Creating new reopened activity log");
                        reopenedActivityLog = new ActivityLog();
                        await _activityLogRepository.InsertAsync(reopenedActivityLog);
                    }
                    else
                    {
                        _logger.LogDebug("Updating existing reopened activity log");
                    }

                    reopenedActivityLog.ActivityType = ActivityLogActivityTypes.SubmissionReopened;
                    reopenedActivityLog.CreatedAt = dateReopened;
                    reopenedActivityLog.ContextId = _workContext.ContextId;
                    reopenedActivityLog.CreatedByIdentityUserId = migrationStartedByUser.Id;
                    reopenedActivityLog.EntityId = submission.Id;
                    reopenedActivityLog.EntityName = submission.GetType().Name;
                    reopenedActivityLog.ShortDescription = "Submission reopened";
                    reopenedActivityLog.Text = text;
                }
            }
        }
    }
}
