// <copyright file="EntryMigrationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms.Forms;
using System.Linq.Expressions;
using MongoDB.Driver;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Shared.Modules;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.DataMigration.Models.Nevis;
using NetProGroup.Trust.DataMigration.Factories;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for migrating entry data.
    /// </summary>
    public class EntryMigrationService
    {
        private readonly ILogger<EntryMigrationService> _logger;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IModulesRepository _modulesRepository;
        private readonly SubmissionMigrationService _submissionMigrationService;
        private readonly InvoiceMigrationService _invoiceMigrationService;
        private readonly PaymentMigrationService _paymentMigrationService;
        private readonly ActivityLogMigrationService _activityLogMigrationService;
        private Module _module;
        private readonly IMongoDbFactory _mongoDbFactory;
        private readonly IFormDocumentAttributesRepository _formDocumentAttributesRepository;
        private readonly TrustDbContext _dbContext;
        private readonly IOptions<DataMigrationAppSettings> _appSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="EntryMigrationService"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="legalEntitiesRepository">The legal entities repository.</param>
        /// <param name="modulesRepository">The modules repository.</param>
        /// <param name="submissionMigrationService">The submission service.</param>
        /// <param name="invoiceMigrationService">The invoice service.</param>
        /// <param name="paymentMigrationService">The payment service.</param>
        /// <param name="activityLogMigrationService">The activity log service.</param>
        /// <param name="mongoDbFactory">The MongoDB factory.</param>
        /// <param name="formDocumentAttributesRepository">The form document attributes repository.</param>
        /// <param name="dbContext">The database context.</param>
        /// <param name="appSettings">The application settings.</param>
        public EntryMigrationService(
            ILogger<EntryMigrationService> logger,
            ILegalEntitiesRepository legalEntitiesRepository,
            IModulesRepository modulesRepository,
            SubmissionMigrationService submissionMigrationService,
            InvoiceMigrationService invoiceMigrationService,
            PaymentMigrationService paymentMigrationService,
            ActivityLogMigrationService activityLogMigrationService,
            IMongoDbFactory mongoDbFactory,
            IFormDocumentAttributesRepository formDocumentAttributesRepository,
            TrustDbContext dbContext,
            IOptions<DataMigrationAppSettings> appSettings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _legalEntitiesRepository = legalEntitiesRepository ?? throw new ArgumentNullException(nameof(legalEntitiesRepository));
            _modulesRepository = modulesRepository ?? throw new ArgumentNullException(nameof(modulesRepository));
            _submissionMigrationService = submissionMigrationService ?? throw new ArgumentNullException(nameof(submissionMigrationService));
            _invoiceMigrationService = invoiceMigrationService ?? throw new ArgumentNullException(nameof(invoiceMigrationService));
            _paymentMigrationService = paymentMigrationService ?? throw new ArgumentNullException(nameof(paymentMigrationService));
            _activityLogMigrationService = activityLogMigrationService ?? throw new ArgumentNullException(nameof(activityLogMigrationService));
            _mongoDbFactory = mongoDbFactory ?? throw new ArgumentNullException(nameof(mongoDbFactory));
            _formDocumentAttributesRepository = formDocumentAttributesRepository;
            _dbContext = dbContext;
            _appSettings = appSettings;
        }

        /// <summary>
        /// Handles the processing of a single entry.
        /// </summary>
        /// <param name="entry">The entry to process.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="moduleId"></param>
        /// <param name="moduleName"></param>
        /// <param name="jurisdictionId"></param>
        /// <param name="jurisdictionName"></param>
        /// <returns>A tuple indicating success and any errors encountered.</returns>
        public async Task<(bool Success, List<string> Errors)> HandleEntry(Entry entry,
            ApplicationUser migrationStartedByUser, string region,
            Guid moduleId,
            string moduleName,
            Guid jurisdictionId,
            string jurisdictionName)
        {
            var errors = new List<string>();
            Check.NotNullOrWhiteSpace(entry.PeriodYear, nameof(entry.PeriodYear));

            if (!int.TryParse(entry.PeriodYear, out var financialYear))
            {
                errors.Add($"Period_year {entry.PeriodYear} in incorrect format.");
                return (false, errors);
            }
            else if (financialYear < 2019)
            {
                errors.Add($"Period_year {financialYear} is less than 2019.");
                return (false, errors);
            }

            if (entry.Version == null)
            {
                errors.Add("Submission version may not be null.");
                return (false, errors);
            }

            var legalEntity = await GetMatchingLegalEntity(entry);
            var formTemplateVersion = await _submissionMigrationService.GetOrCreateTemplateVersionAsync(financialYear, entry.Version, jurisdictionId, jurisdictionName, moduleName, moduleId);
            var submission = await _submissionMigrationService.GetOrCreateSubmissionAsync(legalEntity, financialYear, moduleId);

            var (submissionStatus, submissionIsPaid, formDocumentStatus, formDocumentRevisionStatus) = DetermineStatuses(entry);

            _submissionMigrationService.SetSubmissionProperties(submission, entry, formTemplateVersion, financialYear, legalEntity, submissionStatus, submissionIsPaid, moduleId);
            _submissionMigrationService.SetSubmissionAttributes(submission, entry);
            _submissionMigrationService.SetFormDocumentProperties(submission, entry, formTemplateVersion, legalEntity, financialYear, formDocumentStatus, moduleId);

            var formDocumentRevision = _submissionMigrationService.GetOrCreateFormDocumentRevision(entry, submission, formTemplateVersion, formDocumentRevisionStatus);
            var (mappingSuccess, mappingErrors, form) = MapEntryToForm(entry, formDocumentRevision);

            if (!mappingSuccess)
            {
                errors.AddRange(mappingErrors);
                return (false, errors);
            }

            await UpdateFormAttributes(form, submission.FormDocument);

            var invoice = await _invoiceMigrationService.CreateInvoiceIfNeededAsync(entry, submission, legalEntity, financialYear);

            var (payment, paymentErrors) = await _paymentMigrationService.CreatePaymentIfNeededAsync(entry, submission, invoice, financialYear);
            if (paymentErrors.Any())
            {
                errors.AddRange(paymentErrors);
                return (false, errors);
            }

            // We have to save here because otherwise the activity log creation will fail because the entity ID will be empty.
            await _dbContext.SaveChangesAsync();

            await _activityLogMigrationService.CreateMigrationActivityLogs(entry, migrationStartedByUser, region, submission, invoice, payment, financialYear);

            await _dbContext.SaveChangesAsync();

            return (true, errors);
        }

        /// <summary>
        /// Determines the statuses for a given entry.
        /// </summary>
        /// <param name="entry">The entry to determine statuses for.</param>
        /// <returns>A tuple containing the determined statuses.</returns>
        private static (SubmissionStatus submissionStatus, bool submissionIsPaid, FormDocumentStatus formDocumentStatus,
            FormDocumentRevisionStatus formDocumentRevisionStatus) DetermineStatuses(Entry entry)
        {
            SubmissionStatus submissionStatus;
            FormDocumentStatus formDocumentStatus;
            FormDocumentRevisionStatus formDocumentRevisionStatus;

            if (entry.Status is "SUBMITTED" or "PAID")
            {
                formDocumentStatus = FormDocumentStatus.Finalized;
                submissionStatus = SubmissionStatus.Submitted;
                formDocumentRevisionStatus = FormDocumentRevisionStatus.Finalized;
            }
            else if (entry.Status == "SAVED")
            {
                if (entry.Reopened == null)
                {
                    formDocumentStatus = FormDocumentStatus.Draft;
                    submissionStatus = SubmissionStatus.Draft;
                }
                else
                {
                    formDocumentStatus = FormDocumentStatus.Revision;
                    submissionStatus = SubmissionStatus.Revision;
                }

                formDocumentRevisionStatus = FormDocumentRevisionStatus.Draft;
            }
            else
            {
                throw new ConstraintException($"Unknown entry status: {entry.Status}");
            }

            var submissionIsPaid = entry.Payment != null;

            return (submissionStatus, submissionIsPaid, formDocumentStatus, formDocumentRevisionStatus);
        }

        /// <summary>
        /// Maps an entry to a form.
        /// </summary>
        /// <param name="entry">The entry to map.</param>
        /// <param name="formDocumentRevision">The form document revision to map to.</param>
        /// <returns>A tuple indicating success and any errors encountered.</returns>
        private (bool Success, List<string> Errors, KeyValueForm Form) MapEntryToForm(Entry entry, FormDocumentRevision formDocumentRevision)
        {
            _logger.LogTrace("Mapping entry {EntryId} to form", entry.Id);
            var formBuilder = formDocumentRevision.GetFormBuilder();
            var form = new KeyValueForm();
            formBuilder.Form = form;

            var (mappingSuccess, mappingErrors) = EntryToFormMapper.MapEntryToForm(entry, form, _appSettings.Value.CountryOverrides);

            if (mappingSuccess)
            {
                formDocumentRevision.DataAsJson = formBuilder.ToJson();
            }

            _logger.LogTrace("Mapping entry {EntryId} to form completed with success: {Success}", entry.Id, mappingSuccess);

            return (mappingSuccess, mappingErrors, form);
        }

        /// <summary>
        /// Gets the matching legal entity for an entry.
        /// </summary>
        /// <param name="entry">The entry to find a matching legal entity for.</param>
        /// <returns>The matching legal entity.</returns>
        private async Task<LegalEntity> GetMatchingLegalEntity(Entry entry)
        {
            var companyCode = entry.Company;

            var (company, legalEntity) = await FindLegalEntity(companyCode);

            if (legalEntity == null)
            {
                if (company == null)
                {
                    _logger.LogError("Company with code '{CompanyCode}' not found in TNEV database", companyCode);
                    throw new ConstraintException($"Company with code '{companyCode}' not found in TNEV database.");
                }

                _logger.LogError("Company with code '{CompanyCode}' found in TNEV database, but LegalEntity with VP code '{CompanyVpCode}' not found in PCP database", company.Code, company.VpCode);
                throw new ConstraintException($"Company with code '{companyCode}' found in TNEV database, but LegalEntity with VP code '{company.VpCode}' not found in PCP database.");
            }

            _logger.LogInformation("Found legal entity with code '{LegalEntityCode}' for company code '{CompanyCode}'", legalEntity.Code, companyCode);

            return legalEntity;
        }

        private async Task<(Company tnevCompany, LegalEntity pcpLegalEntity)> FindLegalEntity(string companyCode)
        {
            // get company from mongo database - always use Nevis for services in the Nevis folder
            var mongoDatabase = _mongoDbFactory.GetMongoDatabase(JurisdictionCodes.Nevis);
            var company = await mongoDatabase.GetCollection<Company>("companies")
                .Find(c => c.Code == companyCode)
                .SingleOrDefaultAsync();


            Expression<Func<LegalEntity, bool>> GetLegalEntityPredicate()
            {
                Expression<Func<LegalEntity, bool>> predicate;
                if (company != null)
                {
                    var companyVpCode = company.VpCode;
                    if (company.VpCode != null)
                    {
                        _logger.LogTrace(
                            "Fetching legal entity for company with legacy code: {CompanyCode}, using VP code: {VpCode}.",
                            companyCode, companyVpCode);

                        predicate = entity => entity.Code == companyVpCode;
                        return predicate;
                    }

                    _logger.LogWarning("Company {CompanyCode} has no VP code in MongoDB", companyCode);
                }
                else
                {
                    _logger.LogWarning("Company {CompanyCode} not found in MongoDB", companyCode);
                }

                _logger.LogWarning("VP Code for company code {CompanyCode} could not be found, using company code as search key", companyCode);

                return entity => entity.Code == companyCode;
            }

            var predicate = GetLegalEntityPredicate();

            var legalEntity = await _legalEntitiesRepository.FindFirstOrDefaultByConditionAsync(
                predicate,
                entities => entities);

            return (company, legalEntity);
        }

        /// <summary>
        /// Gets the module for the migration.
        /// </summary>
        /// <returns>The module.</returns>
        private async Task<Module> GetModule()
        {
            if (_module != null)
            {
                return _module;
            }

            _logger.LogDebug("Fetching module with key {ModuleKey}", ModuleKeyConsts.SimplifiedTaxReturn);
            _module = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.SimplifiedTaxReturn);

            if (_module == null)
            {
                _logger.LogError("Module with key '{ModuleKey}' not found", ModuleKeyConsts.SimplifiedTaxReturn);
                throw new ConstraintException($"Module with key '{ModuleKeyConsts.SimplifiedTaxReturn}' not found");
            }

            return _module;
        }

        private async Task UpdateFormAttributes(KeyValueForm form, FormDocument formDocument)
        {
            // Update attributes
            var existingAttributes = formDocument.Attributes.ToList();

            var formValues = form.GetFormValues();
            foreach (var fieldValue in formValues)
            {
                var attr = existingAttributes.FirstOrDefault(x => x.Key == fieldValue.Key);
                if (attr != null)
                {
                    attr.Value = fieldValue.Value;
                    existingAttributes.Remove(attr);
                }
                else
                {
                    formDocument.Attributes.Add(new FormDocumentAttribute(fieldValue.Key, fieldValue.Key) { Value = fieldValue.Value });
                }
            }

            if (existingAttributes.Count > 0)
            {
                await _formDocumentAttributesRepository.DeleteAsync(existingAttributes, false);
            }
        }
    }
}