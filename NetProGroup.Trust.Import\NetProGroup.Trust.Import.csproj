﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FastExcel" Version="3.0.13" />
    <PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.22" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.DataManager\NetProGroup.Trust.DataManager.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Repository\NetProGroup.Trust.Domain.Repository.csproj" />
  </ItemGroup>

</Project>
