using System.Xml.Serialization;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.CXPayStatus
{
    /// <summary>
    /// Represents billing information for CX payment processing.
    /// </summary>
    public class CxPaymentBilling
    {
        /// <summary>
        /// Gets or sets the company name.
        /// </summary>
        public string company { get; set; }

        /// <summary>
        /// Gets or sets the email address.
        /// </summary>
        public string email { get; set; }

        /// <summary>
        /// Gets or sets the first name.
        /// </summary>
        [XmlElement("first-name")]
        public string firstName { get; set; }

        /// <summary>
        /// Gets or sets the last name.
        /// </summary>
        [XmlElement("last-name")]
        public string lastName { get; set; }

        /// <summary>
        /// Gets or sets the address.
        /// </summary>
        [XmlElement("address1")]
        public string address { get; set; }

        /// <summary>
        /// Gets or sets the city.
        /// </summary>
        [XmlElement("city")]
        public string city { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        [XmlElement("state")]
        public string state { get; set; }

        /// <summary>
        /// Gets or sets the postal code.
        /// </summary>
        [XmlElement("postal")]
        public string postal { get; set; }

        /// <summary>
        /// Gets or sets the country.
        /// </summary>
        [XmlElement("country")]
        public string country { get; set; }

        /// <summary>
        /// Gets or sets the phone number.
        /// </summary>
        [XmlElement("phone")]
        public string phone { get; set; }

        /// <summary>
        /// Gets or sets the credit card digits.
        /// </summary>
        [XmlElement("cc-number")]
        public string CardDigits { get; set; }

    }
}