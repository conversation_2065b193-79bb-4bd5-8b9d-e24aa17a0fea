using System.Xml.Serialization;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.CXPayStatus;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models
{
    /// <summary>
    /// Represents a payment sale request for CX payment processing.
    /// </summary>
    [XmlRoot("sale")]
    public class CxPaymentSale
    {
        /// <summary>
        /// Gets or sets the API key for authentication.
        /// </summary>
        [XmlElement("api-key")]
        public string apiKey { get; set; }

        /// <summary>
        /// Gets or sets the redirect URL after payment completion.
        /// </summary>
        [XmlElement("redirect-url")]
        public string redirectUrl { get; set; }

        /// <summary>
        /// Gets or sets the payment amount.
        /// </summary>
        public decimal amount { get; set; }

        /// <summary>
        /// Gets or sets the order identifier.
        /// </summary>
        [XmlElement("order-id")]
        public string orderId { get; set; }

        /// <summary>
        /// Gets or sets the order description.
        /// </summary>
        [XmlElement("order-description")]
        public string orderDescription { get; set; }

        /// <summary>
        /// Gets or sets the currency code.
        /// </summary>
        [XmlElement("currency")]
        public string currency { get; set; }

        /// <summary>
        /// Gets or sets the merchant receipt email address.
        /// </summary>
        [XmlElement("merchant-receipt-email")]
        public string merchantReceiptEmail { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to send customer receipt.
        /// </summary>
        [XmlElement("customer-receipt")]
        public bool customerReceipt { get; set; }

        /// <summary>
        /// Gets or sets the billing information.
        /// </summary>
        public CxPaymentBilling billing { get; set; }
    }
}