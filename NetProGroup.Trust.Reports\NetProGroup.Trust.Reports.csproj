﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.1" />
	<PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.22" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.DataManager\NetProGroup.Trust.DataManager.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Domain\NetProGroup.Trust.Domain.csproj" />
  </ItemGroup>

</Project>
