﻿// <copyright file="CurrenciesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Currencies;

namespace NetProGroup.Trust.Domain.Repository.Currencies
{
    /// <summary>
    /// Repository for Currencies.
    /// </summary>
    public class CurrenciesRepository : RepositoryBase<TrustDbContext, Currency, Guid>, ICurrenciesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CurrenciesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public CurrenciesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ICurrenciesRepository.DbContext => base.DbContext;
    }
}
