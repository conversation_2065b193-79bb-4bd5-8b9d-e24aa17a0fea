﻿// <copyright file="CurrencyModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Currencies;

namespace NetProGroup.Trust.Domain.Repository.Currencies
{
    /// <summary>
    /// Model configuration for a Currency.
    /// </summary>
    public class CurrencyModelConfiguration : IEntityTypeConfiguration<Currency>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<Currency> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "Currencies", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<Currency>(builder);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(100);
            builder.Property(e => e.Code).IsRequired().HasMaxLength(10);
            builder.Property(e => e.Symbol).IsRequired().HasMaxLength(10);
        }
    }
}
