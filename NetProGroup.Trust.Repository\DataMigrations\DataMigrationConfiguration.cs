using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.DataMigrations;
using NetProGroup.Trust.Domain.Repository;

namespace NetProGroup.Trust.Repository.DataMigrations
{
    /// <summary>
    /// Configuration class for the DataMigration entity.
    /// </summary>
    public class DataMigrationConfiguration : IEntityTypeConfiguration<DataMigration>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the entity mapping for DataMigration.
        /// </summary>
        /// <param name="builder">The entity type builder.</param>
        public void Configure(EntityTypeBuilder<DataMigration> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "DataMigrations", TrustDbContext.DbSchema);
            builder.HasKey(dm => dm.Id);
            builder.Property(dm => dm.Id).ValueGeneratedOnAdd();

            builder.Property(dm => dm.JurisdictionId).IsRequired().HasMaxLength(50);
            builder.Property(dm => dm.LastUpdated).IsRequired();
            builder.Property(dm => dm.InitialSyncCompleted).IsRequired();
            builder.Property(dm => dm.MigrationCompleted).IsRequired();
            builder.Property(dm => dm.Status).IsRequired().HasMaxLength(20);
            builder.Property(dm => dm.StopRequested).IsRequired();

            builder.HasMany(dm => dm.EntityMigrationProgresses)
                .WithOne(ep => ep.DataMigration)
                .HasForeignKey(ep => ep.DataMigrationId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(dm => dm.Jurisdiction)
                .WithMany()
                .HasForeignKey(dm => dm.JurisdictionId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }

    /// <summary>
    /// Configuration class for the EntityMigrationProgress entity.
    /// </summary>
    public class EntityMigrationProgressConfiguration : IEntityTypeConfiguration<EntityMigrationProgress>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the entity mapping for EntityMigrationProgress.
        /// </summary>
        /// <param name="builder">The entity type builder.</param>
        public void Configure(EntityTypeBuilder<EntityMigrationProgress> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "EntityMigrationProgresses", TrustDbContext.DbSchema);
            builder.HasKey(dm => dm.Id);
            builder.Property(dm => dm.Id).ValueGeneratedOnAdd();

            builder.HasKey(e => e.Id);
            builder.Property(e => e.EntityName).IsRequired().HasMaxLength(100);
            builder.Property(e => e.SourceCount).IsRequired();
            builder.Property(e => e.ProcessedCount).IsRequired();
            builder.Property(e => e.SuccessCount).IsRequired();
            builder.Property(e => e.LastUpdated).IsRequired();

            builder.HasOne(e => e.DataMigration)
                .WithMany(dm => dm.EntityMigrationProgresses)
                .HasForeignKey(e => e.DataMigrationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_DataMigration_EntityMigrationProgress");
        }
    }
}
