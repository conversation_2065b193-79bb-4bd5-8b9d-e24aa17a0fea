﻿// <copyright file="ExpressionExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Linq.Expressions;

namespace NetProGroup.Trust.Domain.Repository.Extensions
{
    /// <summary>
    /// Provides extension methods for combining expressions.
    /// </summary>
    public static class ExpressionExtensions
    {
        /// <summary>
        /// Combines two expressions with a logical AND operation.
        /// </summary>
        /// <typeparam name="T">The type of the parameter in the expressions.</typeparam>
        /// <param name="expr1">The first expression.</param>
        /// <param name="expr2">The second expression.</param>
        /// <returns>A new expression that represents the logical AND of the two input expressions.</returns>
        public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));

            var body = Expression.AndAlso(
                Expression.Invoke(expr1, parameter),
                Expression.Invoke(expr2, parameter));

            return Expression.Lambda<Func<T, bool>>(body, parameter);
        }

        /// <summary>
        /// Combines two expressions with a logical OR operation.
        /// </summary>
        /// <typeparam name="T">The type of the parameter in the expressions.</typeparam>
        /// <param name="expr1">The first expression.</param>
        /// <param name="expr2">The second expression.</param>
        /// <returns>A new expression that represents the logical OR of the two input expressions.</returns>
        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));
            var body = Expression.OrElse(
                Expression.Invoke(expr1, parameter),
                Expression.Invoke(expr2, parameter));

            return Expression.Lambda<Func<T, bool>>(body, parameter);
        }

        /// <summary>
        /// Create an expression where a property of parameter of the first expression is used as the parameter of the second expression.
        /// </summary>
        /// <typeparam name="TSource">The type of the input of the first expression.</typeparam>
        /// <typeparam name="TProp">The type of the property from the first expression.</typeparam>
        /// <typeparam name="TResult">The type of the result of the second expression.</typeparam>
        /// <param name="selectExpression">The expression that selects the property to be used as the parameter of the second expression.</param>
        /// <param name="transformExpression">The expression that transforms the property selected by the first expression.</param>
        /// <returns>A new expression that represents the transformation of the property selected by the first expression.</returns>
        public static Expression<Func<TSource, TResult>> Combine<TSource, TProp, TResult>(
            Expression<Func<TSource, TProp>> selectExpression,
            Expression<Func<TProp, TResult>> transformExpression)
        {
            ArgumentNullException.ThrowIfNull(selectExpression, nameof(selectExpression));

            var sourceParameter = selectExpression.Parameters[0];
            var propertyAccess = selectExpression.Body;
            var transformedAccess = Expression.Invoke(transformExpression, propertyAccess);
            return Expression.Lambda<Func<TSource, TResult>>(transformedAccess, sourceParameter);
        }
    }
}
