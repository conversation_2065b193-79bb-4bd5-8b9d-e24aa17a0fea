// <copyright file="FindExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.EF.Repository.Interfaces;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.Domain.Repository.Extensions
{
    /// <summary>
    /// Extension methods for the repository.
    /// </summary>
    public static class FindExtensions
    {
        /// <summary>
        /// Finds entities using a predicate and applies paging.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TProjected">The type of the projected entity.</typeparam>
        /// <param name="entities">The entities to filter.</param>
        /// <param name="expression">The predicate expression to filter entities.</param>
        /// <param name="configuration">The AutoMapper configuration provider.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is 1.</param>
        /// <param name="pageSize">The size of the page. Default is **********.</param>
        /// <param name="options">Callback to modify the IQueryable, such as includes and ordering.</param>
        /// <param name="optionsMapped">Callback to modify the IQueryable after projection.</param>
        /// <param name="projectedExpression">The predicate expression to filter the projected entities.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the paged list of projected entities.</returns>
        public static async Task<IPagedList<TProjected>> FindByConditionAsPagedListMappedAsync<TEntity, TProjected>(this IQueryable<TEntity> entities,
            Expression<Func<TEntity, bool>> expression,
            IConfigurationProvider configuration,
            int pageNumber = 1,
            int pageSize = **********,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null,
            Func<IQueryable<TProjected>, IQueryable<TProjected>> optionsMapped = null,
            Expression<Func<TProjected, bool>> projectedExpression = null) where TEntity : class
        {
            ArgumentNullException.ThrowIfNull(entities);

            IQueryable<TEntity> superset = entities.Where(expression);
            if (options != null)
            {
                superset = options(superset);
            }

            var mapped = superset.ProjectTo<TProjected>(configuration);

            if (projectedExpression != null)
            {
                mapped = mapped.Where<TProjected>(projectedExpression);
            }

            if (optionsMapped != null)
            {
                mapped = optionsMapped(mapped);
            }

            return await mapped.ToPagedListAsync(pageNumber, pageSize);
        }

        /// <summary>
        /// Finds entities using a predicate and applies paging.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TProjected">The type of the projected entity.</typeparam>
        /// <param name="repository">The repository instance.</param>
        /// <param name="expression">The predicate expression to filter entities.</param>
        /// <param name="configuration">The AutoMapper configuration provider.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is 1.</param>
        /// <param name="pageSize">The size of the page. Default is **********.</param>
        /// <param name="options">Callback to modify the IQueryable, such as includes and ordering.</param>
        /// <param name="optionsMapped">Callback to modify the IQueryable after projection.</param>
        /// <param name="projectedExpression">The predicate expression to filter the projected entities.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the paged list of projected entities.</returns>
        public static async Task<IPagedList<TProjected>> FindByConditionAsPagedListMappedAsync<TEntity, TProjected>(this IRepository<TEntity, Guid> repository,
            Expression<Func<TEntity, bool>> expression,
            IConfigurationProvider configuration,
            int pageNumber = 1,
            int pageSize = **********,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null,
            Func<IQueryable<TProjected>, IQueryable<TProjected>> optionsMapped = null,
            Expression<Func<TProjected, bool>> projectedExpression = null) where TEntity : class
        {
            ArgumentNullException.ThrowIfNull(repository, nameof(repository));

            return await repository.GetQueryable().FindByConditionAsPagedListMappedAsync(
                expression, configuration, pageNumber, pageSize, options, optionsMapped, projectedExpression);
        }

        /// <summary>
        /// Finds entities using a predicate and applies paging.
        /// </summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TProjected">The type of the projected entity.</typeparam>
        /// <param name="repository">The repository instance.</param>
        /// <param name="expression">The predicate expression to filter entities.</param>
        /// <param name="configuration">The AutoMapper configuration provider.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is 1.</param>
        /// <param name="pageSize">The size of the page. Default is **********.</param>
        /// <param name="options">Callback to modify the IQueryable, such as includes and ordering.</param>
        /// <param name="optionsMapped">Callback to modify the IQueryable after projection.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the paged list of projected entities.</returns>
        public static async Task<List<TProjected>> FindByConditionMappedAsync<TEntity, TProjected>(this IRepository<TEntity, Guid> repository,
            Expression<Func<TEntity, bool>> expression,
            IConfigurationProvider configuration,
            int pageNumber = 1,
            int pageSize = **********,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null,
            Func<IQueryable<TProjected>, IQueryable<TProjected>> optionsMapped = null) where TEntity : class
        {
            ArgumentNullException.ThrowIfNull(repository, nameof(repository));

            IQueryable<TEntity> source = repository.GetQueryable().Where(expression);

            if (options != null)
            {
                source = options(source);
            }

            var mapped = source.ProjectTo<TProjected>(configuration);

            if (optionsMapped != null)
            {
                mapped = optionsMapped(mapped);
            }

            return await mapped.ToListAsync();
        }

        /// <summary>Find entities using predicate.</summary>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TKey">The type of the key.</typeparam>
        /// <param name="repository">The repository instance.</param>
        /// <param name="expression">The predicate expression to filter entities.</param>
        /// <param name="options">Callback to modify the IQueryable like includes and ordering.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of entities.</returns>
        public static async Task<TEntity> FindSingleOrDefaultByConditionAsync<TEntity, TKey>(
            this IRepository<TEntity, TKey> repository,
            Expression<Func<TEntity, bool>> expression,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null)
        {
            ArgumentNullException.ThrowIfNull(repository, nameof(repository));

            IQueryable<TEntity> source = repository.GetQueryable().Where<TEntity>(expression);
            if (options != null)
            {
                source = options(source);
            }

            return await source.SingleOrDefaultAsync<TEntity>();
        }
    }
}