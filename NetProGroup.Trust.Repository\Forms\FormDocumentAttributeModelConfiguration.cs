﻿// <copyright file="FormDocumentAttributeModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Forms
{
    /// <summary>
    /// Model configuration for a FormDocumentAttribute.
    /// </summary>
    public class FormDocumentAttributeModelConfiguration : IEntityTypeConfiguration<FormDocumentAttribute>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<FormDocumentAttribute> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "FormDocumentAttributes", TrustDbContext.DbSchema);
            builder.HasKey(fda => new { fda.Id });
            builder.Property(fda => fda.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<FormDocumentAttribute>(builder);

            builder.Property(fda => fda.Key).IsRequired().HasMaxLength(SettingConsts.KeyMaxLength);
            builder.Property(fda => fda.Name).IsRequired().HasMaxLength(SettingConsts.NameMaxLength);

            builder.HasOne(fda => fda.FormDocument).WithMany(fd=>fd.Attributes)
                .HasForeignKey(fda => fda.FormDocumentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormDocumentAttribute_FormDocument");
        }
    }
}
