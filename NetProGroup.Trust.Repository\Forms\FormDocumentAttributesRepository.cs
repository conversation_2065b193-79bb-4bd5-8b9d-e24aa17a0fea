﻿// <copyright file="FormDocumentAttributesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for FormDocument.
    /// </summary>
    public class FormDocumentAttributesRepository : RepositoryBase<TrustDbContext, FormDocumentAttribute, Guid>, IFormDocumentAttributesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentAttributesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public FormDocumentAttributesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IFormDocumentAttributesRepository.DbContext => base.DbContext;
    }
}
