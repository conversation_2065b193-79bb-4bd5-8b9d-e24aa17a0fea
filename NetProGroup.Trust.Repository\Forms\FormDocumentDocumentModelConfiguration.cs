﻿// <copyright file="FormDocumentDocumentModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Forms
{
    /// <summary>
    /// Model configuration for a FormDocumentDocument.
    /// </summary>
    public class FormDocumentDocumentModelConfiguration : IEntityTypeConfiguration<FormDocumentDocument>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<FormDocumentDocument> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "FormDocumentDocuments", TrustDbContext.DbSchema);
            builder.HasKey(fd => new { fd.Id });
            builder.Property(fd => fd.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<FormDocumentDocument>(builder);

            // Document relationship (required)
            builder.HasOne(p => p.Document)
                .WithMany()
                .HasForeignKey(p => p.DocumentId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_FormDocumentDocument_Document");

            // FormDocument relationship (required)
            builder.HasOne(p => p.FormDocument)
                .WithMany()
                .HasForeignKey(p => p.FormDocumentId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_FormDocumentdocument_FormDocument");
        }
    }
}
