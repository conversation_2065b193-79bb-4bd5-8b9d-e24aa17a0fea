﻿// <copyright file="FormDocumentDocumentsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for FormDocument.
    /// </summary>
    public class FormDocumentDocumentsRepository : RepositoryBase<TrustDbContext, FormDocumentDocument, Guid>, IFormDocumentDocumentsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentDocumentsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public FormDocumentDocumentsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IFormDocumentDocumentsRepository.DbContext => base.DbContext;
    }
}
