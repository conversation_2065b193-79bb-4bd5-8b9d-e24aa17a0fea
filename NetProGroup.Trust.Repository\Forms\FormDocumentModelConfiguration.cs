﻿// <copyright file="FormDocumentModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Forms
{
    /// <summary>
    /// Model configuration for a FormTemplate.
    /// </summary>
    public class FormDocumentModelConfiguration : IEntityTypeConfiguration<FormDocument>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<FormDocument> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "FormDocuments", TrustDbContext.DbSchema);
            builder.HasKey(fd => new { fd.Id });
            builder.Property(fd => fd.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<FormDocument>(builder);

            builder.Property(fd => fd.Name).IsRequired().HasMaxLength(FormConsts.NameMaxLength);

            builder.HasOne(fd => fd.FormTemplateVersion).WithMany()
                .HasForeignKey(fd => fd.FormTemplateVersionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormDocument_FormTemplateVersion");

            builder.HasOne(fd => fd.LegalEntity).WithMany()
                .HasForeignKey(fd => fd.LegalEntityId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormDocument_LegalEntity");

            builder.HasOne(fd => fd.Module).WithMany()
                .HasForeignKey(fd => fd.ModuleId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormDocument_Module");
        }
    }
}
