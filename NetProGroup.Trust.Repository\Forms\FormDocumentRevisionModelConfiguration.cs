﻿// <copyright file="FormDocumentRevisionModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.Forms
{
    /// <summary>
    /// Model configuration for a FormDocumentRevision.
    /// </summary>
    public class FormDocumentRevisionModelConfiguration : IEntityTypeConfiguration<FormDocumentRevision>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<FormDocumentRevision> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "FormDocumentRevisions", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<FormDocumentRevision>(builder);

            builder.HasOne(revision => revision.FormDocument).WithMany(tv => tv.FormDocumentRevisions)
                .HasForeignKey(revision => revision.FormDocumentId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormDocumentRevision_FormDocument");
        }
    }
}
