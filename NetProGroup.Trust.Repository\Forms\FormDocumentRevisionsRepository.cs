﻿// <copyright file="FormDocumentRevisionsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for FormDocumentRevision.
    /// </summary>
    public class FormDocumentRevisionsRepository : RepositoryBase<TrustDbContext, FormDocumentRevision, Guid>, IFormDocumentRevisionsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentRevisionsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public FormDocumentRevisionsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IFormDocumentRevisionsRepository.DbContext => base.DbContext;
    }
}
