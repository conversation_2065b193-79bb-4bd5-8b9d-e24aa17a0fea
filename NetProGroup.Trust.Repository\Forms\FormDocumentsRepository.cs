﻿// <copyright file="FormDocumentsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for FormDocument.
    /// </summary>
    public class FormDocumentsRepository : RepositoryBase<TrustDbContext, FormDocument, Guid>, IFormDocumentsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public FormDocumentsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IFormDocumentsRepository.DbContext => base.DbContext;
    }
}
