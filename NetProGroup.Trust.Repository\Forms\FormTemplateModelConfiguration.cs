﻿// <copyright file="FormTemplateModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Forms
{
    /// <summary>
    /// Model configuration for a FormTemplate.
    /// </summary>
    public class FormTemplateModelConfiguration : IEntityTypeConfiguration<FormTemplate>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<FormTemplate> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "FormTemplates", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<FormTemplate>(builder);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(FormConsts.NameMaxLength);
            builder.Property(e => e.Key).IsRequired().HasMaxLength(FormConsts.KeyMaxLength);

            builder.HasOne(template => template.Jurisdiction).WithMany()
                .HasForeignKey(template => template.JurisdictionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormTemplate_Jurisdiction");

            builder.HasOne(template => template.Module).WithMany()
                .HasForeignKey(template => template.ModuleId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormTemplate_Module");
        }
    }
}
