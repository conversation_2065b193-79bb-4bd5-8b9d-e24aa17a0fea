﻿// <copyright file="FormTemplateVersionModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Forms
{
    /// <summary>
    /// Model configuration for a FormTemplateVersion.
    /// </summary>
    public class FormTemplateVersionModelConfiguration : IEntityTypeConfiguration<FormTemplateVersion>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<FormTemplateVersion> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "FormTemplateVersions", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<FormTemplateVersion>(builder);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(FormConsts.NameMaxLength);
            builder.Property(e => e.Version).IsRequired().HasMaxLength(FormConsts.VersionMaxLength);

            builder.HasOne(version => version.FormTemplate).WithMany(template => template.FormTemplateVersions)
                .HasForeignKey(version => version.FormTemplateId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_FormTemplateVersion_FormTemplate");
        }
    }
}
