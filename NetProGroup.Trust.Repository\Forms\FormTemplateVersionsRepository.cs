﻿// <copyright file="FormTemplateVersionsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for FormTemplateVersion.
    /// </summary>
    public class FormTemplateVersionsRepository : RepositoryBase<TrustDbContext, FormTemplateVersion, Guid>, IFormTemplateVersionsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormTemplateVersionsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public FormTemplateVersionsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IFormTemplateVersionsRepository.DbContext => base.DbContext;
    }
}
