﻿// <copyright file="FormTemplatesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for FormTemplate.
    /// </summary>
    public class FormTemplatesRepository : RepositoryBase<TrustDbContext, FormTemplate, Guid>, IFormTemplatesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormTemplatesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public FormTemplatesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IFormTemplatesRepository.DbContext => base.DbContext;
    }
}
