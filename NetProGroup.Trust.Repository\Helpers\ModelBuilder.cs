﻿// <copyright file="ModelBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Domain.Repository.Helpers
{
    /// <summary>
    /// Helper for the ModelBuilder.
    /// Should be moved to the NetPro Framework.
    /// </summary>
    internal static class ModelBuilder
    {
        /// <summary>
        /// Sets the SQL defaults for StampedEntity.
        /// </summary>
        /// <typeparam name="T">Generic type for the EntityTypeBuilder.</typeparam>
        /// <param name="builder">The EntityTypeBuilder.</param>
        public static void SetStampedEntityDefaults<T>(EntityTypeBuilder<T> builder) where T : StampedEntity<Guid>
        {
            builder.Property(x => x.Id).ValueGeneratedOnAdd().HasDefaultValueSql("newid()");

            builder.Property(x => x.CreatedAt).IsRequired().HasDefaultValueSql("getutcdate()");
            builder.Property(x => x.UpdatedAt).IsRequired().HasDefaultValueSql("getutcdate()");
        }
    }
}
