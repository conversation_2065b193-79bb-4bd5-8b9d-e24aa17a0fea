﻿// <copyright file="InboxOwnerModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Inboxes;

namespace NetProGroup.Trust.Domain.Repository.Inboxes
{
    /// <summary>
    /// Model configuration for an InboxOwner.
    /// </summary>
    public class InboxOwnerModelConfiguration : IEntityTypeConfiguration<InboxOwner>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<InboxOwner> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "InboxOwners", TrustDbContext.DbSchema);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<InboxOwner>(builder);

            builder.HasOne(e => e.Inbox)
                .WithMany()
                .HasForeignKey(e => e.InboxId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_InboxOwner_Inbox");
        }
    }
}
