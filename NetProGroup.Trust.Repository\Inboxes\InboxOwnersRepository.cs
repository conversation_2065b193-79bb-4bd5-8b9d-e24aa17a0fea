﻿// <copyright file="InboxOwnersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Inboxes;

namespace NetProGroup.Trust.Domain.Repository.Inboxes
{
    /// <summary>
    /// Repository for InboxOwners.
    /// </summary>
    public class InboxOwnersRepository : RepositoryBase<TrustDbContext, InboxOwner, Guid>, IInboxOwnersRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="InboxOwnersRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public InboxOwnersRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IInboxOwnersRepository.DbContext => base.DbContext;
    }
}
