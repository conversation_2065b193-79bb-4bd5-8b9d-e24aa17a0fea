﻿// <copyright file="InboxReadStatusModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Inboxes;

namespace NetProGroup.Trust.Domain.Repository.Inboxes
{
    /// <summary>
    /// Model configuration for an InboxReadStatus.
    /// </summary>
    public class InboxReadStatusModelConfiguration : IEntityTypeConfiguration<InboxReadStatus>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<InboxReadStatus> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "InboxReadStatuses", TrustDbContext.DbSchema);
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<InboxReadStatus>(builder);

            builder.Property(e => e.ReadAt).IsRequired();

            builder.HasOne(e => e.Inbox)
                .WithMany()
                .HasForeignKey(e => e.InboxId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_InboxReadStatus_Inbox");

            builder.HasOne(e => e.User)
                .WithMany()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_InboxReadStatus_User");
        }
    }
}
