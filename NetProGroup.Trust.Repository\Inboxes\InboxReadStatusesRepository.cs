﻿// <copyright file="InboxReadStatusesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Inboxes;

namespace NetProGroup.Trust.Domain.Repository.Inboxes
{
    /// <summary>
    /// Repository for InboxReadStatuses.
    /// </summary>
    public class InboxReadStatusesRepository : RepositoryBase<TrustDbContext, InboxReadStatus, Guid>, IInboxReadStatusesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="InboxReadStatusesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public InboxReadStatusesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IInboxReadStatusesRepository.DbContext => base.DbContext;
    }
}
