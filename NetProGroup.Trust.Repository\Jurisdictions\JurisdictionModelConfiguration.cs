﻿// <copyright file="JurisdictionModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Jurisdictions
{
    /// <summary>
    /// Model configuration for a Jurisdiction.
    /// </summary>
    public class JurisdictionModelConfiguration : IEntityTypeConfiguration<Jurisdiction>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<Jurisdiction> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "Jurisdictions", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<Jurisdiction>(builder);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(JurisdictionConsts.NameMaxLength);
            builder.Property(e => e.Code).IsRequired().HasMaxLength(JurisdictionConsts.CodeMaxLength);            
        }
    }
}
