﻿// <copyright file="JurisdictionTaxRateModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Jurisdictions;

namespace NetProGroup.Trust.Domain.Repository.Jurisdictions
{
    /// <summary>
    /// Model configuration for a JurisdictionTaxRate.
    /// </summary>
    public class JurisdictionTaxRateModelConfiguration : IEntityTypeConfiguration<JurisdictionTaxRate>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<JurisdictionTaxRate> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "JurisdictionTaxRates", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<JurisdictionTaxRate>(builder);

            builder.Property(e => e.TaxRate).IsRequired().HasColumnType("decimal(18,2)");
            builder.Property(e => e.StartDate).IsRequired();

            builder.HasOne(e => e.Jurisdiction)
                   .WithMany(j => j.TaxRates)
                   .HasForeignKey(e => e.JurisdictionId)
                   .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
