﻿// <copyright file="JurisdictionTaxRatesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Jurisdictions;

namespace NetProGroup.Trust.Domain.Repository.Jurisdictions
{
    /// <summary>
    /// Repository for JurisdictionTaxRates.
    /// </summary>
    public class JurisdictionTaxRatesRepository : RepositoryBase<TrustDbContext, JurisdictionTaxRate, Guid>, IJurisdictionTaxRatesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionTaxRatesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public JurisdictionTaxRatesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IJurisdictionTaxRatesRepository.DbContext => base.DbContext;
    }
}
