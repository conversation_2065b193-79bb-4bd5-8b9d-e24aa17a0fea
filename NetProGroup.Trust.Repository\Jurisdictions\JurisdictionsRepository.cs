﻿// <copyright file="JurisdictionsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Jurisdictions;

namespace NetProGroup.Trust.Domain.Repository.Projects
{
    /// <summary>
    /// Repository for Jurisdictions.
    /// </summary>
    public class JurisdictionsRepository : RepositoryBase<TrustDbContext, Jurisdiction, Guid>, IJurisdictionsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public JurisdictionsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IJurisdictionsRepository.DbContext => base.DbContext;
    }
}
