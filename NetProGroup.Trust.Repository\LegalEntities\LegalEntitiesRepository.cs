﻿// <copyright file="LegalEntitiesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for LegalEntity.
    /// </summary>
    public class LegalEntitiesRepository : RepositoryBase<TrustDbContext, LegalEntity, Guid>, ILegalEntitiesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntitiesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public LegalEntitiesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ILegalEntitiesRepository.DbContext => base.DbContext;

        /// <summary>
        /// Gets the payment provider settings for a company.
        /// </summary>
        /// <param name="companyId">The ID of the company.</param>
        /// <returns>The payment provider settings for the company, or null if not found.</returns>
        /// <exception cref="ArgumentNullException">Thrown if <paramref name="companyId"/> is null.</exception>
        public Domain.Payments.Provider.PaymentProvider GetPaymentProviderSettingsForCompany(Guid companyId)
        {
            ArgumentNullException.ThrowIfNull(companyId);

            var legalEntity = GetById(companyId, q =>
                q.Include(le => le.Jurisdiction)
                    .ThenInclude(j => j.PaymentProviders));

            // TODO: THIS ONE NEED TO ADJUST, right now there's a single payment provider per jurisdiction
            // which CXPAY
            return legalEntity.Jurisdiction?.PaymentProviders?.FirstOrDefault();
        }
    }
}
