﻿// <copyright file="LegalEntityAnnualFeeModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a LegalEntityAnnualFee.
    /// </summary>
    public class LegalEntityAnnualFeeModelConfiguration : IEntityTypeConfiguration<LegalEntityAnnualFee>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<LegalEntityAnnualFee> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "LegalEntityAnnualFees", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<LegalEntityAnnualFee>(builder);

            builder.Property(e => e.LegalEntityId).IsRequired();
            builder.Property(e => e.IsPaid).IsRequired();
            builder.Property(e => e.FinancialYear).IsRequired();

            builder.HasIndex(x => new { x.LegalEntityId, x.FinancialYear }).IsUnique();

            builder.HasOne(x => x.LegalEntity).WithMany(x => x.AnnualFees)
                .HasForeignKey(x => x.LegalEntityId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_LegalEntityAnnualFee_LegalEntity");
        }
    }
}
