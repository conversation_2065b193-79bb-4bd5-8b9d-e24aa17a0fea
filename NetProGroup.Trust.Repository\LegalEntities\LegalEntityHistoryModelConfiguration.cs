﻿// <copyright file="LegalEntityHistoryModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a LegalEntityHistory.
    /// </summary>
    public class LegalEntityHistoryModelConfiguration : IEntityTypeConfiguration<LegalEntityHistory>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<LegalEntityHistory> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "LegalEntityHistory", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<LegalEntityHistory>(builder);

            builder.Property(e => e.ExternalUniqueId).IsRequired(false).HasMaxLength(LegalEntityConsts.CodeMaxLength);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(LegalEntityConsts.NameMaxLength);
            builder.Property(e => e.Code).IsRequired().HasMaxLength(LegalEntityConsts.CodeMaxLength);
            builder.Property(e => e.LegacyCode).IsRequired(false).HasMaxLength(LegalEntityConsts.LegacyCodeMaxLength);

            builder.Property(e => e.EntityTypeName).IsRequired(false).HasMaxLength(LegalEntityConsts.NameMaxLength);
            builder.Property(e => e.EntityTypeCode).IsRequired(false).HasMaxLength(LegalEntityConsts.CodeMaxLength);

            builder.Property(e => e.EntityStatus).IsRequired(false).HasMaxLength(LegalEntityConsts.EntityStatusMaxLength);
            builder.Property(e => e.EntitySubStatus).IsRequired(false).HasMaxLength(LegalEntityConsts.EntitySubStatusMaxLength);

            builder.Property(e => e.IncorporationNr).IsRequired(false).HasMaxLength(LegalEntityConsts.IncorporationNrMaxLength);
            builder.Property(e => e.JurisdictionOfRegistration).IsRequired(false).HasMaxLength(LegalEntityConsts.JurisdictionOfRegistrationMaxLength);

            builder.Property(e => e.ReferralOffice).IsRequired(false).HasMaxLength(LegalEntityConsts.ReferralOfficeMaxLength);
            builder.Property(e => e.ProductionOffice).IsRequired(false).HasMaxLength(LegalEntityConsts.ProductionOfficeMaxLength);

            builder.Property(e => e.RiskGroup).IsRequired(false).HasMaxLength(LegalEntityConsts.RiskGroupMaxLength);
            builder.Property(e => e.Administrator).IsRequired(false).HasMaxLength(LegalEntityConsts.AdministratorMaxLength);
            builder.Property(e => e.Manager).IsRequired(false).HasMaxLength(LegalEntityConsts.ManagerMaxLength);

            builder.Property(e => e.MasterClientCode).IsRequired(false).HasMaxLength(MasterClientConsts.CodeMaxLength);
        }
    }
}
