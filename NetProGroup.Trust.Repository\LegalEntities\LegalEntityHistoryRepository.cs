﻿// <copyright file="LegalEntityHistoryRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for LegalEntityStaging.
    /// </summary>
    public class LegalEntityHistoryRepository : RepositoryBase<TrustDbContext, LegalEntityHistory, Guid>, ILegalEntityHistoryRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntityHistoryRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public LegalEntityHistoryRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <inheritdoc/>
        public async Task<IList<LegalEntityHistory>> ListCurrentCompaniesByJurisdictionIdAsync(Guid jurisdictionId)
        {
            var paramJurisdictionId = new Microsoft.Data.SqlClient.SqlParameter("jurisdictionId", jurisdictionId);

            var query = this.Entities.FromSqlRaw("SELECT les.* FROM [LegalEntityHistory] as leh " +
                                                "INNER JOIN " +
                                                "(SELECT Code, JurisdictionId, EntityType, MAX(CreatedAt) MaxCreatedAt FROM [LegalEntityStaging] " +
                                                "GROUP BY Code, JurisdictionId, EntityType) lehmax " +
                                                "ON leh.Code = lehmax.Code AND leh.JurisdictionId = lehmax.JurisdictionId AND leh.CreatedAt = lehmax.MaxCreatedAt AND leh.EntityType = lehmax.EntityType AND leh.jurisdictionId = @jurisdictionId",
                                                paramJurisdictionId);

            query = query.Where(x => x.EntityType == DomainShared.Enums.LegalEntityType.Company);

            return await query.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<LegalEntityHistory> GetCurrentCompanyByEntityCodeAsync(Guid jurisdictionId, string code)
        {
            var query = this.Entities
                            .Where(leh => leh.EntityType == DomainShared.Enums.LegalEntityType.Company
                                          && leh.Code == code
                                          && leh.JurisdictionId == jurisdictionId)
                            .OrderByDescending(leh => leh.CreatedAt);

            return await query.FirstOrDefaultAsync();
        }
    }
}
