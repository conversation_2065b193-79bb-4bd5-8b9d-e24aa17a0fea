﻿// <copyright file="LegalEntityModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a LegalEntity.
    /// </summary>
    public class LegalEntityModelConfiguration : IEntityTypeConfiguration<LegalEntity>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<LegalEntity> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "LegalEntities", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<LegalEntity>(builder);

            builder.Property(e => e.ExternalUniqueId).IsRequired(false).HasMaxLength(LegalEntityConsts.CodeMaxLength);

            builder.Property(e => e.Name).IsRequired().HasMaxLength(LegalEntityConsts.NameMaxLength);
            builder.Property(e => e.Code).IsRequired().HasMaxLength(LegalEntityConsts.CodeMaxLength);
            builder.Property(e => e.LegacyCode).IsRequired(false).HasMaxLength(LegalEntityConsts.LegacyCodeMaxLength);

            builder.Property(e => e.EntityTypeName).IsRequired(false).HasMaxLength(LegalEntityConsts.NameMaxLength);
            builder.Property(e => e.EntityTypeCode).IsRequired(false).HasMaxLength(LegalEntityConsts.CodeMaxLength);

            builder.Property(e => e.EntityStatus).IsRequired(false).HasMaxLength(LegalEntityConsts.EntityStatusMaxLength);
            builder.Property(e => e.EntitySubStatus).IsRequired(false).HasMaxLength(LegalEntityConsts.EntitySubStatusMaxLength);

            builder.Property(e => e.IncorporationNr).IsRequired(false).HasMaxLength(LegalEntityConsts.IncorporationNrMaxLength);
            builder.Property(e => e.JurisdictionOfRegistration).IsRequired(false).HasMaxLength(LegalEntityConsts.JurisdictionOfRegistrationMaxLength);

            builder.Property(e => e.ReferralOffice).IsRequired(false).HasMaxLength(LegalEntityConsts.ReferralOfficeMaxLength);
            builder.Property(e => e.ProductionOffice).IsRequired(false).HasMaxLength(LegalEntityConsts.ProductionOfficeMaxLength);

            builder.Property(e => e.RiskGroup).IsRequired(false).HasMaxLength(LegalEntityConsts.RiskGroupMaxLength);
            builder.Property(e => e.Administrator).IsRequired(false).HasMaxLength(LegalEntityConsts.AdministratorMaxLength);
            builder.Property(e => e.Manager).IsRequired(false).HasMaxLength(LegalEntityConsts.ManagerMaxLength);

            builder.Property(e => e.MasterClientCode).IsRequired(false).HasMaxLength(MasterClientConsts.CodeMaxLength);

            builder.HasOne(x => x.MasterClient).WithMany(x => x.LegalEntities)
                .HasForeignKey(x => x.MasterClientId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_LegalEntity_MasterClient");

            builder.HasOne(x => x.Jurisdiction).WithMany()
                .HasForeignKey(x => x.JurisdictionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_LegalEntity_Jurisdiction");

            builder.HasIndex(mc => new { mc.EntityType, mc.OnboardingStatus, mc.JurisdictionId })
                .IncludeProperties(mc => new { mc.Code, mc.IncorporationNr, mc.LegacyCode, mc.MasterClientCode, mc.MasterClientId, mc.Name })
                .IsUnique(false)
                .HasDatabaseName("IX_LegalEntity_EntityType_OnboardingStatus_JurisdictionId");

            builder.HasIndex(mc => mc.Code).IsUnique();
        }
    }
}
