﻿// <copyright file="BeneficialOwnerHistoryModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a BeneficialOwnerHistory.
    /// </summary>
    public class BeneficialOwnerHistoryModelConfiguration : IEntityTypeConfiguration<BeneficialOwnerHistory>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<BeneficialOwnerHistory> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "BeneficialOwnerHistory", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<BeneficialOwnerHistory>(builder);

            builder.Property(e => e.ExternalUniqueId).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.ExternalUniqueIdMaxLength);
            builder.Property(e => e.Name).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.NameMaxLength);
            builder.Property(e => e.FormerName).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.FormerNameMaxLength);
            builder.Property(e => e.Code).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CodeMaxLength);
            builder.Property(e => e.CompanyNumber).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CompanyNumberMaxLength);

            builder.Property(e => e.TIN).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.TINMaxLength);
            builder.Property(e => e.IncorporationNr).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CorporateRegistrationNumberMaxLength);

            builder.Property(e => e.FileType).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.FileTypeMaxLength);
            builder.Property(e => e.OfficerTypeCode).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.OfficerTypeCodeMaxLength);
            builder.Property(e => e.OfficerTypeName).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.OfficerTypeNameMaxLength);

            builder.Property(e => e.NameOfRegulator).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.NameOfRegulatorMaxLength);
            builder.Property(e => e.JurisdictionOfRegulator).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.JurisdictionOfRegulatorMaxLength);

            builder.Property(e => e.Address).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.AddressMaxLength);
            builder.Property(e => e.ResidentialAddress).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.AddressMaxLength);
            builder.Property(e => e.ServiceAddress).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.AddressMaxLength);

            builder.Property(e => e.PlaceOfBirth).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.CountryOfBirth).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.CountryOfBirthCode).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CountryCodeMaxLength);

            builder.Property(e => e.Nationality).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.Country).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.SovereignState).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);

            builder.Property(e => e.StockExchangeCode).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.StockCodeMaxLength);
            builder.Property(e => e.StockExchangeName).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.StockExchangeMaxLength);

            builder.Property(e => e.UpdateRequestComments).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.UpdateRequestCommentsMaxLength);

            builder.HasOne(x => x.UpdateRequestedByUser).WithMany()
                .HasForeignKey(x => x.UpdateRequestedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_UpdateRequestedByUser_BeneficialOwnerHistory");

            builder.HasOne(x => x.BeneficialOwner).WithMany(x => x.BeneficialOwnerHistories)
                .HasForeignKey(x => x.BeneficialOwnerId)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("FK_BeneficialOwner_BeneficialOwnerHistory");

            builder.HasOne(x => x.ConfirmedByUser).WithMany()
                .HasForeignKey(x => x.ConfirmedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_ConfirmedByUser_BeneficialOwnerHistory");

            builder.HasIndex(x => x.ExternalUniqueId).HasDatabaseName("IX_ExternalUniqueId");
            builder.HasIndex(x => x.CreatedAt).HasDatabaseName("IX_CreatedAt");
        }
    }
}
