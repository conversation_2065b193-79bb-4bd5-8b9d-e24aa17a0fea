﻿// <copyright file="BeneficialOwnerHistoryRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for BeneficialOwner.
    /// </summary>
    public class BeneficialOwnerHistoryRepository : RepositoryBase<TrustDbContext, BeneficialOwnerHistory, Guid>, IBeneficialOwnerHistoryRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnerHistoryRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public BeneficialOwnerHistoryRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IBeneficialOwnerHistoryRepository.DbContext => base.DbContext;

        /// <inheritdoc/>
        public async Task<List<BeneficialOwnerHistory>> ListCurrentBeneficialOwnersByLegalEntityIdAsync(Guid legalEntityId)
        {
            var paramLegalEntityId = new Microsoft.Data.SqlClient.SqlParameter("legalEntityId", legalEntityId);

            var data = this.Entities.FromSqlRaw("SELECT bo.* FROM [BeneficialOwnerHistory] as bo " +
                                                "INNER JOIN " +
                                                "(SELECT ExternalUniqueId, MAX(CreatedAt) MaxCreatedAt, LegalEntityId FROM [BeneficialOwnerHistory] " +
                                                "GROUP BY ExternalUniqueId, LegalEntityId) bomax " +
                                                "ON bo.ExternalUniqueId = bomax.ExternalUniqueId AND bo.LegalEntityId = bomax.LegalEntityId AND bo.CreatedAt = bomax.MaxCreatedAt AND bo.legalEntityId = @legalEntityId " +
                                                //"ORDER BY bo.externaluniqueid ASC", paramLegalEntityId);
                                                "", paramLegalEntityId);
            data = data.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                       .Include(x => x.UpdateRequestedByUser)
                       .Include(x => x.ConfirmedByUser);


            return await data.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerHistory> GetCurrentBeneficialOwnerByUniqueRelationIdAsync(string uniqueRelationId)
        {
            var paramUniqueRelationId = new Microsoft.Data.SqlClient.SqlParameter("uniqueRelationId", uniqueRelationId);

            var data = this.Entities.FromSqlRaw("SELECT TOP 1 bo.* FROM [BeneficialOwnerHistory] as bo " +
                                                "WHERE bo.ExternalUniqueId = @uniqueRelationId " +
                                                "ORDER BY bo.CreatedAt DESC", paramUniqueRelationId);

            data = data.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                       .Include(x => x.UpdateRequestedByUser)
                       .Include(x => x.ConfirmedByUser);

            return await data.OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerHistory> GetLastBeneficialOwnerByUniqueRelationIdAndStatusAsync(string uniqueRelationId, LegalEntityRelationStatus[] inStatus)
        {
            var paramUniqueRelationId = new Microsoft.Data.SqlClient.SqlParameter("uniqueRelationId", uniqueRelationId);
            var paramStatus = string.Join(", ", inStatus.Cast<int>());

            var query = this.Entities.FromSqlRaw("SELECT TOP 1 bo.* FROM [BeneficialOwnerHistory] as bo " +
                                                 $"WHERE bo.ExternalUniqueId = @uniqueRelationId AND bo.Status in ({paramStatus}) " +
                                                 "ORDER BY bo.CreatedAt DESC", paramUniqueRelationId, paramStatus);

            query = query.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                         .Include(x => x.UpdateRequestedByUser)
                         .Include(x => x.ConfirmedByUser);

            return await query.OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();
        }
    }
}
