﻿// <copyright file="BeneficialOwnersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for BeneficialOwner.
    /// </summary>
    public class BeneficialOwnersRepository : RepositoryBase<TrustDbContext, BeneficialOwner, Guid>, IBeneficialOwnersRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnersRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public BeneficialOwnersRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IBeneficialOwnersRepository.DbContext => base.DbContext;
    }
}
