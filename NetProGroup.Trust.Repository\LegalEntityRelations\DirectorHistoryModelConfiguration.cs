﻿// <copyright file="DirectorModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a Director.
    /// </summary>
    public class DirectorHistoryModelConfiguration : IEntityTypeConfiguration<DirectorHistory>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<DirectorHistory> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "DirectorHistory", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<DirectorHistory>(builder);

            builder.Property(e => e.ExternalUniqueId).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.ExternalUniqueIdMaxLength);
            builder.Property(e => e.Name).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.NameMaxLength);
            builder.Property(e => e.FormerName).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.FormerNameMaxLength);
            builder.Property(e => e.Code).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CodeMaxLength);
            builder.Property(e => e.CompanyNumber).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CompanyNumberMaxLength);

            builder.Property(e => e.FileType).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.FileTypeMaxLength);
            builder.Property(e => e.RelationType).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.RelationTypeMaxLength);
            builder.Property(e => e.OfficerTypeName).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.OfficerTypeCodeMaxLength);
            builder.Property(e => e.TIN).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.TINMaxLength);

            builder.Property(e => e.IncorporationNr).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CorporateRegistrationNumberMaxLength);
            builder.Property(e => e.IncorporationPlace).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.IncorporationCountry).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.IncorporationCountryCode).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CountryCodeMaxLength);

            builder.Property(e => e.Address).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.AddressMaxLength);
            builder.Property(e => e.ResidentialAddress).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.AddressMaxLength);
            builder.Property(e => e.ServiceAddress).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.AddressMaxLength);

            builder.Property(e => e.PlaceOfBirth).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.CountryOfBirth).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.CountryOfBirthCode).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CountryCodeMaxLength);

            builder.Property(e => e.Nationality).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);
            builder.Property(e => e.Country).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.PlaceOrCountryMaxLength);

            builder.Property(e => e.DirectorIsAlternateToCode).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CodeMaxLength);
            builder.Property(e => e.DirectorIsAlternateToName).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.NameMaxLength);

            builder.Property(e => e.UpdateRequestComments).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.UpdateRequestCommentsMaxLength);

            builder.Property(e => e.DirectorCapacity).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.DirectorCapacityMaxLength);
            builder.Property(e => e.VPDirectorID).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.DirectorIDMaxLength);
            builder.Property(e => e.LicenseeEntityCode).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.CodeMaxLength);
            builder.Property(e => e.LicenseeEntityName).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.NameMaxLength);

            builder.HasOne(x => x.UpdateRequestedByUser).WithMany()
                .HasForeignKey(x => x.UpdateRequestedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_UpdateRequestedByUser_DirectorHistory");

            builder.HasOne(x => x.Director).WithMany(x => x.DirectorHistories)
                .HasForeignKey(x => x.DirectorId)
                .OnDelete(DeleteBehavior.SetNull)
                .HasConstraintName("FK_Director_DirectorHistory");

            builder.HasOne(x => x.ConfirmedByUser).WithMany()
                .HasForeignKey(x => x.ConfirmedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_ConfirmedByUser_DirectorHistory");

            builder.HasIndex(x => x.ExternalUniqueId).HasDatabaseName("IX_ExternalUniqueId");
            builder.HasIndex(x => x.CreatedAt).HasDatabaseName("IX_CreatedAt");
        }
    }
}
