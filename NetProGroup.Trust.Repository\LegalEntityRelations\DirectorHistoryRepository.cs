﻿// <copyright file="DirectorHistoryRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for DirectorHistory.
    /// </summary>
    public class DirectorHistoryRepository : RepositoryBase<TrustDbContext, DirectorHistory, Guid>, IDirectorHistoryRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorHistoryRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public DirectorHistoryRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IDirectorHistoryRepository.DbContext => base.DbContext;

        /// <inheritdoc/>
        public async Task<List<DirectorHistory>> ListCurrentDirectorsByLegalEntityIdAsync(Guid legalEntityId)
        {
            var paramLegalEntityId = new Microsoft.Data.SqlClient.SqlParameter("legalEntityId", legalEntityId);

            var data = this.Entities.FromSqlRaw("SELECT bo.* FROM [DirectorHistory] as bo " +
                                                "INNER JOIN " +
                                                "(SELECT ExternalUniqueId, MAX(CreatedAt) MaxCreatedAt, LegalEntityId FROM [DirectorHistory] " +
                                                "GROUP BY ExternalUniqueId, LegalEntityId) bomax " +
                                                "ON bo.ExternalUniqueId = bomax.ExternalUniqueId AND bo.LegalEntityId = bomax.LegalEntityId AND bo.CreatedAt = bomax.MaxCreatedAt AND bo.legalEntityId = @legalEntityId " +
                                                //"ORDER BY bo.externaluniqueid ASC", paramLegalEntityId);
                                                "", paramLegalEntityId);

            data = data.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                       .Include(x => x.UpdateRequestedByUser)
                       .Include(x => x.ConfirmedByUser);

            return await data.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<DirectorHistory> GetCurrentDirectorByUniqueRelationIdAsync(string uniqueRelationId)
        {
            var paramUniqueRelationId = new Microsoft.Data.SqlClient.SqlParameter("uniqueRelationId", uniqueRelationId);

            var query = this.Entities.FromSqlRaw("SELECT TOP 1 bo.* FROM [DirectorHistory] as bo " +
                                                 "WHERE bo.ExternalUniqueId = @uniqueRelationId " +
                                                 "ORDER BY bo.CreatedAt DESC", paramUniqueRelationId);

            query = query.Include(x => x.LegalEntity).ThenInclude(x => x.MasterClient)
                         .Include(x => x.UpdateRequestedByUser)
                         .Include(x => x.ConfirmedByUser);

            return await query.FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<DirectorHistory> GetLastDirectorByUniqueRelationIdAndStatusAsync(string uniqueRelationId, LegalEntityRelationStatus[] inStatus)
        {
            var paramUniqueRelationId = new Microsoft.Data.SqlClient.SqlParameter("uniqueRelationId", uniqueRelationId);
            var paramStatus = string.Join(", ", inStatus.Cast<int>());

            var query = this.Entities.FromSqlRaw("SELECT TOP 1 dir.* FROM [DirectorHistory] as dir " +
                                                 $"WHERE dir.ExternalUniqueId = @uniqueRelationId AND dir.Status in ({paramStatus}) " +
                                                 "ORDER BY dir.CreatedAt DESC", paramUniqueRelationId);

            query = query.Include(x => x.LegalEntity).ThenInclude(x => x.MasterClient)
                         .Include(x => x.UpdateRequestedByUser)
                         .Include(x => x.ConfirmedByUser);

            return await query.OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();
        }
    }
}
