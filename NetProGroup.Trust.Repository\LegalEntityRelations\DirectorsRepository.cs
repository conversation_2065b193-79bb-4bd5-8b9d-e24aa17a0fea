﻿// <copyright file="DirectorsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for Director.
    /// </summary>
    public class DirectorsRepository : RepositoryBase<TrustDbContext, Director, Guid>, IDirectorsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public DirectorsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IDirectorsRepository.DbContext => base.DbContext;
    }
}
