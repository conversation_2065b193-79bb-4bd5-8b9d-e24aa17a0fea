﻿// <copyright file="ShareholderHistoryModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a Shareholder.
    /// </summary>
    public class ShareholderHistoryModelConfiguration : IEntityTypeConfiguration<ShareholderHistory>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<ShareholderHistory> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "ShareholderHistory", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<ShareholderHistory>(builder);

            builder.Property(e => e.ExternalUniqueId).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.ExternalUniqueIdMaxLength);
            builder.Property(e => e.Name).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.NameMaxLength);
           
            builder.Property(e => e.UpdateRequestComments).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.UpdateRequestCommentsMaxLength);
                        
            builder.HasOne(x => x.UpdateRequestedByUser).WithMany()
                .HasForeignKey(x => x.UpdateRequestedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_UpdateRequestedByUser_ShareholderHistory");

            builder.HasOne(x => x.ConfirmedByUser).WithMany()
                .HasForeignKey(x => x.ConfirmedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_ConfirmedByUser_ShareholderHistory");

            builder.HasIndex(x => x.ExternalUniqueId).HasDatabaseName("IX_ExternalUniqueId");
            builder.HasIndex(x => x.CreatedAt).HasDatabaseName("IX_CreatedAt");
        }
    }
}
