﻿// <copyright file="ShareholderHistoryRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for Shareholder.
    /// </summary>
    public class ShareholderHistoryRepository : RepositoryBase<TrustDbContext, ShareholderHistory, Guid>, IShareholderHistoryRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ShareholderHistoryRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public ShareholderHistoryRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <inheritdoc/>
        public async Task<IList<ShareholderHistory>> ListCurrentShareholdersByLegalEntityIdAsync(Guid legalEntityId)
        {
            var paramLegalEntityId = new Microsoft.Data.SqlClient.SqlParameter("legalEntityId", legalEntityId);

            var data = this.Entities.FromSqlRaw("SELECT bo.* FROM [ShareholderHistory] as bo " +
                                                "INNER JOIN " +
                                                "(SELECT ExternalUniqueId, MAX(CreatedAt) MaxCreatedAt, LegalEntityId FROM [ShareholderHistory] " +
                                                "GROUP BY ExternalUniqueId, LegalEntityId) bomax " +
                                                "ON bo.ExternalUniqueId = bomax.ExternalUniqueId AND bo.LegalEntityId = bomax.LegalEntityId AND bo.CreatedAt = bomax.MaxCreatedAt AND bo.legalEntityId = @legalEntityId " +
                                                //"ORDER BY bo.externaluniqueid ASC", paramLegalEntityId);
                                                "", paramLegalEntityId);

            data = data.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                       .Include(x => x.UpdateRequestedByUser)
                       .Include(x => x.ConfirmedByUser);

            return await data.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<ShareholderHistory> GetCurrentShareholderByUniqueRelationIdAsync(string uniqueRelationId)
        {
            var paramUniqueRelationId = new Microsoft.Data.SqlClient.SqlParameter("uniqueRelationId", uniqueRelationId);

            var data = this.Entities.FromSqlRaw("SELECT TOP 1 bo.* FROM [ShareholderHistory] as bo " +
                                                "WHERE bo.ExternalUniqueId = @uniqueRelationId " +
                                                "ORDER BY bo.CreatedAt DESC", paramUniqueRelationId);

            data = data.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                       .Include(x => x.UpdateRequestedByUser)
                       .Include(x => x.ConfirmedByUser);

            return await data.FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<ShareholderHistory> GetLastShareholderByUniqueRelationIdAndStatusAsync(string uniqueRelationId, LegalEntityRelationStatus[] inStatus)
        {
            var paramUniqueRelationId = new Microsoft.Data.SqlClient.SqlParameter("uniqueRelationId", uniqueRelationId);

            var query = this.Entities.FromSqlRaw("SELECT TOP 1 bo.* FROM [ShareholderHistory] as bo " +
                                                "WHERE bo.ExternalUniqueId = @uniqueRelationId " +
                                                "ORDER BY bo.CreatedAt DESC", paramUniqueRelationId);

            // We need to get the last data that had one of these states
            query = query.Where(x => inStatus.Contains(x.Status));

            query = query.Include(x => x.LegalEntity).ThenInclude(le => le.MasterClient)
                         .Include(x => x.UpdateRequestedByUser)
                         .Include(x => x.ConfirmedByUser);

            return await query.OrderByDescending(x => x.CreatedAt).FirstOrDefaultAsync();
        }
    }
}
