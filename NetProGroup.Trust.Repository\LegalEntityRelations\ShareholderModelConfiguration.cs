﻿// <copyright file="ShareholderModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Model configuration for a Shareholder.
    /// </summary>
    public class ShareholderModelConfiguration : IEntityTypeConfiguration<Shareholder>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<Shareholder> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "Shareholders", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<Shareholder>(builder);

            builder.Property(e => e.ExternalUniqueId).IsRequired(false).HasMaxLength(LegalEntityRelationConsts.ExternalUniqueIdMaxLength);
            builder.Property(e => e.Name).IsRequired(true).HasMaxLength(LegalEntityRelationConsts.NameMaxLength);
                        
            builder.HasOne(x => x.LegalEntity).WithMany()
                .HasForeignKey(x => x.LegalEntityId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_LegalEntity_Shareholder");            
        }
    }
}
