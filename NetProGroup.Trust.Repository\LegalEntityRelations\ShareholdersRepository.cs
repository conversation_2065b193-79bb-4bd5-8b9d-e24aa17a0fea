﻿// <copyright file="ShareholdersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.LegalEntities;

namespace NetProGroup.Trust.Domain.Repository.LegalEntities
{
    /// <summary>
    /// Repository for Shareholder.
    /// </summary>
    public class ShareholdersRepository : RepositoryBase<TrustDbContext, Shareholder, Guid>, IShareholdersRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ShareholdersRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public ShareholdersRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

    }
}
