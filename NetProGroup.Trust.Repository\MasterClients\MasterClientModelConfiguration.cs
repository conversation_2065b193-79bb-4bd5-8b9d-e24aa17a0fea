﻿// <copyright file="MasterClientModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.MasterClients
{
    /// <summary>
    /// Model configuration for a MasterClient.
    /// </summary>
    public class MasterClientModelConfiguration : IEntityTypeConfiguration<MasterClient>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<MasterClient> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "MasterClients", TrustDbContext.DbSchema);
            builder.HasKey(mc => new { mc.Id });
            builder.Property(mc => mc.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<MasterClient>(builder);

            builder.Property(mc => mc.Code).IsRequired().HasMaxLength(MasterClientConsts.CodeMaxLength);
            builder.Property(mc => mc.Name).IsRequired(false).HasMaxLength(MasterClientConsts.NameMaxLength);

            builder.HasIndex(mc => mc.Code).IsUnique();
        }
    }
}
