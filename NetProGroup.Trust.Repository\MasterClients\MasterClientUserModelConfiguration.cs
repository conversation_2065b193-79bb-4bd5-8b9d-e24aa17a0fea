﻿// <copyright file="MasterClientUserModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.MasterClients;

namespace NetProGroup.Trust.Domain.Repository.MasterClients
{
    /// <summary>
    /// Model configuration for a BuildingBlock.
    /// </summary>
    public class MasterClientUserModelConfiguration : IEntityTypeConfiguration<MasterClientUser>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<MasterClientUser> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "MasterClientUsers", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<MasterClientUser>(builder);

            builder.HasOne(x => x.MasterClient).WithMany(x => x.MasterClientUsers)
                .HasForeignKey(x => x.MasterClientId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_MasterClientUser_MasterClient");

            builder.HasOne(x => x.User).WithMany()
                .HasForeignKey(x => x.UserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_MasterClientUser_ApplicationUser");
        }
    }
}
