﻿// <copyright file="MasterClientUsersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.MasterClients;

namespace NetProGroup.Trust.Domain.Repository.MasterClients
{
    /// <summary>
    /// Repository for masterclients users.
    /// </summary>
    public class MasterClientUsersRepository : RepositoryBase<TrustDbContext, MasterClientUser, Guid>, IMasterClientUsersRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientUsersRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public MasterClientUsersRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

    }
}
