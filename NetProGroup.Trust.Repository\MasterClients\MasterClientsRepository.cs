﻿// <copyright file="MasterClientsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.MasterClients;

namespace NetProGroup.Trust.Domain.Repository.MasterClients
{
    /// <summary>
    /// Repository for MasterClients.
    /// </summary>
    public class MasterClientsRepository : RepositoryBase<TrustDbContext, MasterClient, Guid>, IMasterClientsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public MasterClientsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IMasterClientsRepository.DbContext => base.DbContext;
    }
}
