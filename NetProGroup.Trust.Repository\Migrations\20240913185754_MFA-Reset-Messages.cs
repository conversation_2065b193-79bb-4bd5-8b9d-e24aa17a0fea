﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class MFAResetMessages : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "value",
                table: "Settings",
                newName: "Value");

            migrationBuilder.InsertData(
                schema: "NetPro",
                table: "MessageTemplates",
                columns: new[] { "Id", "BccEmailAddresses", "BodyType", "CcEmailAddresses", "ConcurrencyStamp", "CreatedAt", "Description", "DisplayName", "EmailAccountId", "HtmlBody", "IsActive", "PlainBody", "Subject", "SystemName", "ToEmailAddresses", "UpdatedAt" },
                values: new object[] { new Guid("cd3b60ab-98c0-403c-9360-fdd93d2ab62b"), null, 0, null, null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Message with the verification code for reset of MFA.", "MFACode Reset Message.", null, "The confirmation code for resetting MFA is: {code}", true, "The confirmation code for resetting MFA is: {code}", "Your Trident Trust Reset Verification Code", "MFAResetRequestMessage", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "NetPro",
                table: "MessageTemplates",
                keyColumn: "Id",
                keyValue: new Guid("cd3b60ab-98c0-403c-9360-fdd93d2ab62b"));

            migrationBuilder.RenameColumn(
                name: "Value",
                table: "Settings",
                newName: "value");
        }
    }
}
