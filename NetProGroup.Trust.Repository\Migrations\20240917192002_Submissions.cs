﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class Submissions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormDocuments_LegalEntities_LegalEntityId",
                table: "FormDocuments");

            migrationBuilder.CreateTable(
                name: "Submissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "newid()"),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    FinalizedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LegalEntityId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FormDocumentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ModuleId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    FinancialYear = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    ConcurrencyStamp = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Submissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Submission_FormDocument",
                        column: x => x.FormDocumentId,
                        principalTable: "FormDocuments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Submission_LegalEntity",
                        column: x => x.LegalEntityId,
                        principalTable: "LegalEntities",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Submission_Module",
                        column: x => x.ModuleId,
                        principalTable: "Modules",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_FormDocumentId",
                table: "Submissions",
                column: "FormDocumentId");

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_LegalEntityId",
                table: "Submissions",
                column: "LegalEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_ModuleId",
                table: "Submissions",
                column: "ModuleId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormDocument_LegalEntity",
                table: "FormDocuments",
                column: "LegalEntityId",
                principalTable: "LegalEntities",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormDocument_LegalEntity",
                table: "FormDocuments");

            migrationBuilder.DropTable(
                name: "Submissions");

            migrationBuilder.AddForeignKey(
                name: "FK_FormDocuments_LegalEntities_LegalEntityId",
                table: "FormDocuments",
                column: "LegalEntityId",
                principalTable: "LegalEntities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
