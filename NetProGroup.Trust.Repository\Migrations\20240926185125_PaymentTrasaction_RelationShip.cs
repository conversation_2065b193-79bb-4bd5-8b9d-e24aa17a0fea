﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class PaymentTrasaction_RelationShip : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.RenameColumn(
                name: "PaymentReference",
                table: "PaymentTransactions",
                newName: "PaymentId");

            migrationBuilder.RenameIndex(
                name: "IX_PaymentTransactions_PaymentReference",
                table: "PaymentTransactions",
                newName: "IX_PaymentTransactions_PaymentId");

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "PaymentTransactions",
                type: "nvarchar(450)",
                maxLength: 450,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CardDigits",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Company",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsFinished",
                table: "PaymentTransactions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "LastName",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PaidAt",
                table: "PaymentTransactions",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProcessCreatedAt",
                table: "PaymentTransactions",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReceiptNumber",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Result",
                table: "PaymentTransactions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResultCode",
                table: "PaymentTransactions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResultMessage",
                table: "PaymentTransactions",
                type: "nvarchar(450)",
                maxLength: 450,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "State",
                table: "PaymentTransactions",
                type: "nvarchar(450)",
                maxLength: 450,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransactionId",
                table: "PaymentTransactions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VoucherNumber",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VoucherTransactionId",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ZipCode",
                table: "PaymentTransactions",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentProviderId",
                table: "Payments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateTable(
                name: "PaymentProviders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "varchar(250)", maxLength: 250, nullable: false),
                    Key = table.Column<string>(type: "varchar(MAX)", nullable: false),
                    BaseUrl = table.Column<string>(type: "varchar(MAX)", nullable: false),
                    ApiKey = table.Column<string>(type: "varchar(MAX)", nullable: false),
                    ApiSecret = table.Column<string>(type: "varchar(MAX)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ConcurrencyStamp = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentProviders", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Payments_PaymentProviderId",
                table: "Payments",
                column: "PaymentProviderId");

            migrationBuilder.AddForeignKey(
                name: "FK_Payment_PaymentProvider",
                table: "Payments",
                column: "PaymentProviderId",
                principalTable: "PaymentProviders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payment_PaymentProvider",
                table: "Payments");

            migrationBuilder.DropTable(
                name: "PaymentProviders");

            migrationBuilder.DropIndex(
                name: "IX_Payments_PaymentProviderId",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "CardDigits",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "City",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "Company",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "FirstName",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "IsFinished",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "LastName",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "PaidAt",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "ProcessCreatedAt",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "ReceiptNumber",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "Result",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "ResultCode",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "ResultMessage",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "State",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "TransactionId",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "VoucherNumber",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "VoucherTransactionId",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "ZipCode",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "PaymentProviderId",
                table: "Payments");

            migrationBuilder.RenameColumn(
                name: "PaymentId",
                table: "PaymentTransactions",
                newName: "PaymentReference");

            migrationBuilder.RenameIndex(
                name: "IX_PaymentTransactions_PaymentId",
                table: "PaymentTransactions",
                newName: "IX_PaymentTransactions_PaymentReference");
        }
    }
}
