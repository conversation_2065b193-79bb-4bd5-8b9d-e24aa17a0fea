﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SubmissionInvoice : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormTemplates_Modules_ModuleId",
                table: "FormTemplates");

            migrationBuilder.AddColumn<Guid>(
                name: "InvoiceId",
                table: "Submissions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_InvoiceId",
                table: "Submissions",
                column: "InvoiceId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormTemplate_Module",
                table: "FormTemplates",
                column: "ModuleId",
                principalTable: "Modules",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Submission_Invoice",
                table: "Submissions",
                column: "InvoiceId",
                principalTable: "Invoices",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormTemplate_Module",
                table: "FormTemplates");

            migrationBuilder.DropForeignKey(
                name: "FK_Submission_Invoice",
                table: "Submissions");

            migrationBuilder.DropIndex(
                name: "IX_Submissions_InvoiceId",
                table: "Submissions");

            migrationBuilder.DropColumn(
                name: "InvoiceId",
                table: "Submissions");

            migrationBuilder.AddForeignKey(
                name: "FK_FormTemplates_Modules_ModuleId",
                table: "FormTemplates",
                column: "ModuleId",
                principalTable: "Modules",
                principalColumn: "Id");
        }
    }
}
