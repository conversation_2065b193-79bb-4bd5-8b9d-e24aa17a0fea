﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class JurisdictiononCompany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MasterClient_Jurisdiction",
                table: "MasterClients");

            migrationBuilder.AlterColumn<Guid>(
                name: "JurisdictionId",
                table: "MasterClients",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<string>(
                name: "LegacyCode",
                table: "MasterClients",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddForeignKey(
                name: "FK_MasterClients_Jurisdictions_JurisdictionId",
                table: "MasterClients",
                column: "JurisdictionId",
                principalTable: "Jurisdictions",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MasterClients_Jurisdictions_JurisdictionId",
                table: "MasterClients");

            migrationBuilder.DropColumn(
                name: "LegacyCode",
                table: "MasterClients");

            migrationBuilder.AlterColumn<Guid>(
                name: "JurisdictionId",
                table: "MasterClients",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MasterClient_Jurisdiction",
                table: "MasterClients",
                column: "JurisdictionId",
                principalTable: "Jurisdictions",
                principalColumn: "Id");
        }
    }
}
