﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class BODirCompany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Directors",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AppointmentDate",
                table: "Directors",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CessationDate",
                table: "Directors",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfFormation",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                table: "Directors",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FormerName",
                table: "Directors",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "IncorporationDate",
                table: "Directors",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationNr",
                table: "Directors",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsIndividual",
                table: "Directors",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "JurisdictionOfRegulator",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Nationality",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficerType",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PlaceOfBirth",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResidentialAddress",
                table: "Directors",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServiceAddress",
                table: "Directors",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SovereignState",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "DirectorHistory",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AppointmentDate",
                table: "DirectorHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CessationDate",
                table: "DirectorHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfFormation",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                table: "DirectorHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FormerName",
                table: "DirectorHistory",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "IncorporationDate",
                table: "DirectorHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationNr",
                table: "DirectorHistory",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsIndividual",
                table: "DirectorHistory",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "JurisdictionOfRegulator",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Nationality",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficerType",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PlaceOfBirth",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResidentialAddress",
                table: "DirectorHistory",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServiceAddress",
                table: "DirectorHistory",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SovereignState",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "BeneficialOwners",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfFormation",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                table: "BeneficialOwners",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "IncorporationDate",
                table: "BeneficialOwners",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationNr",
                table: "BeneficialOwners",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsIndividual",
                table: "BeneficialOwners",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "JurisdictionOfRegulator",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NameOfRegulator",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Nationality",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficerType",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PlaceOfBirth",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResidentialAddress",
                table: "BeneficialOwners",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SovereignState",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TIN",
                table: "BeneficialOwners",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfFormation",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateOfBirth",
                table: "BeneficialOwnerHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "IncorporationDate",
                table: "BeneficialOwnerHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationNr",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsIndividual",
                table: "BeneficialOwnerHistory",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "JurisdictionOfRegulator",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NameOfRegulator",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Nationality",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficerType",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PlaceOfBirth",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResidentialAddress",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SovereignState",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TIN",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "SyncBODirector",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncBODirector", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SyncCompany",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncCompany", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SyncMasterClient",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncMasterClient", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SyncBODirector");

            migrationBuilder.DropTable(
                name: "SyncCompany");

            migrationBuilder.DropTable(
                name: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "AppointmentDate",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "CessationDate",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "CountryOfFormation",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "FormerName",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "IncorporationDate",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "IncorporationNr",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "IsIndividual",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "JurisdictionOfRegulator",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "Nationality",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "OfficerType",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "PlaceOfBirth",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "ResidentialAddress",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "ServiceAddress",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "SovereignState",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "AppointmentDate",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "CessationDate",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "CountryOfFormation",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "FormerName",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "IncorporationDate",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "IncorporationNr",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "IsIndividual",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "JurisdictionOfRegulator",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "Nationality",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "OfficerType",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "PlaceOfBirth",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "ResidentialAddress",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "ServiceAddress",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "SovereignState",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "CountryOfFormation",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "IncorporationDate",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "IncorporationNr",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "IsIndividual",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "JurisdictionOfRegulator",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "NameOfRegulator",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "Nationality",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "OfficerType",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "PlaceOfBirth",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "ResidentialAddress",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "SovereignState",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "TIN",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "CountryOfFormation",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "DateOfBirth",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "IncorporationDate",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "IncorporationNr",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "IsIndividual",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "JurisdictionOfRegulator",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "NameOfRegulator",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "Nationality",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "OfficerType",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "PlaceOfBirth",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "ResidentialAddress",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "SovereignState",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "TIN",
                table: "BeneficialOwnerHistory");
        }
    }
}
