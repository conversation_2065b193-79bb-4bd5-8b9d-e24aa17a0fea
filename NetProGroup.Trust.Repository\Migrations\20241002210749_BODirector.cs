﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class BODirector : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SyncBODirector");

            migrationBuilder.AddColumn<string>(
                name: "StockCode",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StockExchange",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StockCode",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StockExchange",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StockCode",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "StockExchange",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "StockCode",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "StockExchange",
                table: "BeneficialOwnerHistory");

            migrationBuilder.CreateTable(
                name: "SyncBODirector",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncBODirector", x => x.Id);
                });
        }
    }
}
