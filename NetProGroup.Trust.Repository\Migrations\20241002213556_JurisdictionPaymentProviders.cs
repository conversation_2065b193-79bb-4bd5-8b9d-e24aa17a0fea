﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class JurisdictionPaymentProviders : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "JurisdictionId",
                table: "PaymentProviders",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_PaymentProviders_JurisdictionId",
                table: "PaymentProviders",
                column: "JurisdictionId");

            migrationBuilder.AddForeignKey(
                name: "FK_PaymentProviders_Jurisdictions_JurisdictionId",
                table: "PaymentProviders",
                column: "JurisdictionId",
                principalTable: "Jurisdictions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PaymentProviders_Jurisdictions_JurisdictionId",
                table: "PaymentProviders");

            migrationBuilder.DropIndex(
                name: "IX_PaymentProviders_JurisdictionId",
                table: "PaymentProviders");

            migrationBuilder.DropColumn(
                name: "JurisdictionId",
                table: "PaymentProviders");
        }
    }
}
