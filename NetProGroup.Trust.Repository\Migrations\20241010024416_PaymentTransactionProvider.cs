﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class PaymentTransactionProvider : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payment_PaymentProvider",
                table: "Payments");

            migrationBuilder.DropIndex(
                name: "IX_Payments_PaymentProviderId",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "PaymentProviderId",
                table: "Payments");

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentProviderId",
                table: "PaymentTransactions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_PaymentTransactions_PaymentProviderId",
                table: "PaymentTransactions",
                column: "PaymentProviderId");

            migrationBuilder.AddForeignKey(
                name: "FK_PaymentTransaction_PaymentProvider",
                table: "PaymentTransactions",
                column: "PaymentProviderId",
                principalTable: "PaymentProviders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PaymentTransaction_PaymentProvider",
                table: "PaymentTransactions");

            migrationBuilder.DropIndex(
                name: "IX_PaymentTransactions_PaymentProviderId",
                table: "PaymentTransactions");

            migrationBuilder.DropColumn(
                name: "PaymentProviderId",
                table: "PaymentTransactions");

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentProviderId",
                table: "Payments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Payments_PaymentProviderId",
                table: "Payments",
                column: "PaymentProviderId");

            migrationBuilder.AddForeignKey(
                name: "FK_Payment_PaymentProvider",
                table: "Payments",
                column: "PaymentProviderId",
                principalTable: "PaymentProviders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
