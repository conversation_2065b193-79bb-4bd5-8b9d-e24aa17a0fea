﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SyncMasterClientCompany : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LegacyCode",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserEmail",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityStatus",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntitySubStatus",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityType",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationDate",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationNr",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LegacyCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MasterClientCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RefrerralOffice",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RiskGroup",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServiceOffice",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "MessageTemplates",
                keyColumn: "Id",
                keyValue: new Guid("a68733e2-de6b-4f82-bb0a-f8f547be3183"),
                columns: new[] { "HtmlBody", "PlainBody" },
                values: new object[] { "<html>  <head> <meta http-equiv=Content-Type content=\"text/html; charset=windows-1252\"> <meta name=Generator content=\"Microsoft Word 15 (filtered)\"> <style> <!--  /* Font Definitions */  @font-face 	{font-family:\"Cambria Math\"; 	panose-1:2 4 5 3 5 4 6 3 2 4;} @font-face 	{font-family:Aptos;}  /* Style Definitions */  p.MsoNormal, li.MsoNormal, div.MsoNormal 	{margin-top:0in; 	margin-right:0in; 	margin-bottom:8.0pt; 	margin-left:0in; 	line-height:107%; 	font-size:11.0pt; 	font-family:\"Aptos\",sans-serif;} .MsoChpDefault 	{font-size:11.0pt;} .MsoPapDefault 	{margin-bottom:8.0pt; 	line-height:107%;} @page WordSection1 	{size:595.3pt 841.9pt; 	margin:70.85pt 70.85pt 70.85pt 70.85pt;} div.WordSection1 	{page:WordSection1;} --> </style>  </head>  <body lang=NL style='word-wrap:break-word'>  <div class=WordSection1>  <p class=MsoNormal><b><span lang=EN-SG>Entity Name</span></b><span lang=EN-SG>:&nbsp;{company.name} <br> <b>Entity Code</b>:&nbsp;{company.code}<br> <b>Master Client Code</b>:&nbsp;{masterclient.code}<br> <b>VP&nbsp;</b></span><b><span lang=EN-US>{masterfile.label} Masterfile Code:&nbsp;</span></b><span lang=EN-US>{masterfile.code}<br> <b>Requestor:&nbsp;</b>{requestor} <br> <b>Position:&nbsp;</b>{position} <br> <b>Type of Request:&nbsp;</b>{request}<br> <b>Comment:&nbsp;</b>{comment}</span></p>  <p class=MsoNormal><span lang=EN-US>&nbsp;</span></p>  </div>  </body>  </html> ", "Entity Name: {company.name}  Entity Code: {company.code} Master Client Code: {masterclient.code} VP {masterfile.label} Masterfile Code: {masterfile.code} Requestor: {requestor}  Position: {position}  Type of Request: {request} Comment: {comment}  " });

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "MessageTemplates",
                keyColumn: "Id",
                keyValue: new Guid("c382294b-67cb-456d-a59b-986cddaaeac3"),
                columns: new[] { "HtmlBody", "PlainBody" },
                values: new object[] { "<html>  <head> <meta http-equiv=Content-Type content=\"text/html; charset=windows-1252\"> <meta name=Generator content=\"Microsoft Word 15 (filtered)\"> <style> <!--  /* Font Definitions */  @font-face 	{font-family:\"Cambria Math\"; 	panose-1:2 4 5 3 5 4 6 3 2 4;} @font-face 	{font-family:Aptos;}  /* Style Definitions */  p.MsoNormal, li.MsoNormal, div.MsoNormal 	{margin-top:0in; 	margin-right:0in; 	margin-bottom:8.0pt; 	margin-left:0in; 	line-height:107%; 	font-size:11.0pt; 	font-family:\"Aptos\",sans-serif;} .MsoChpDefault 	{font-size:11.0pt;} .MsoPapDefault 	{margin-bottom:8.0pt; 	line-height:107%;} @page WordSection1 	{size:595.3pt 841.9pt; 	margin:70.85pt 70.85pt 70.85pt 70.85pt;} div.WordSection1 	{page:WordSection1;} --> </style>  </head>  <body lang=NL style='word-wrap:break-word'>  <div class=WordSection1>  <p class=MsoNormal><b><span lang=EN-SG>Entity Name</span></b><span lang=EN-SG>:&nbsp;{company.name} <br> <b>Entity Code</b>:&nbsp;{company.code}<br> <b>Master Client Code</b>:&nbsp;{masterclient.code}</span><span lang=EN-US><br> <b>Requestor</b>:&nbsp;</span><span lang=EN-SG>{requestor}</span><span lang=EN-US><br> <b>Type of Request</b>: {request}</span></p>  </div>  </body>  </html> ", "Entity Name: {company.name}  Entity Code: {company.code} Master Client Code: {masterclient.code} Requestor: {requestor} Type of Request: {request} " });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                table: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "LegacyCode",
                table: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "UserEmail",
                table: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityStatus",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntitySubStatus",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityType",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "IncorporationDate",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "IncorporationNr",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "LegacyCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "MasterClientCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "RefrerralOffice",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "RiskGroup",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "ServiceOffice",
                table: "SyncCompany");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "MessageTemplates",
                keyColumn: "Id",
                keyValue: new Guid("a68733e2-de6b-4f82-bb0a-f8f547be3183"),
                columns: new[] { "HtmlBody", "PlainBody" },
                values: new object[] { "TBD", "TBD" });

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "MessageTemplates",
                keyColumn: "Id",
                keyValue: new Guid("c382294b-67cb-456d-a59b-986cddaaeac3"),
                columns: new[] { "HtmlBody", "PlainBody" },
                values: new object[] { "TBD", "TBD" });
        }
    }
}
