﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class Configuration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Action",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("1d3e063b-c066-499e-a7ae-fc1793673dd4"),
                column: "Key",
                value: "communication.notifications.updaterequest.recipient.tcyp");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("3fed38cc-1cb7-4846-80b2-ae7c93b1bd75"),
                column: "Key",
                value: "communication.notifications.updaterequest.recipient.tpanvg");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("7bacfbc7-e987-4f05-bae8-cc9efbbc151d"),
                column: "Key",
                value: "communication.notifications.updaterequest.recipient.thko");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("bbcf4766-03d3-40e4-825e-0f9e21133174"),
                column: "Key",
                value: "communication.notifications.updaterequest.recipient.default");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("f9a6d733-d01b-4dec-ae22-b9a3c7bd5362"),
                column: "Key",
                value: "communication.notifications.updaterequest.recipient.tbvi");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Action",
                table: "SyncMasterClient");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("1d3e063b-c066-499e-a7ae-fc1793673dd4"),
                column: "Key",
                value: "communication.notifications.requestupdate.recipient.tcyp");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("3fed38cc-1cb7-4846-80b2-ae7c93b1bd75"),
                column: "Key",
                value: "communication.notifications.requestupdate.recipient.tpanvg");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("7bacfbc7-e987-4f05-bae8-cc9efbbc151d"),
                column: "Key",
                value: "communication.notifications.requestupdate.recipient.thko");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("bbcf4766-03d3-40e4-825e-0f9e21133174"),
                column: "Key",
                value: "communication.notifications.requestupdate.recipient.default");

            migrationBuilder.UpdateData(
                schema: "NetPro",
                table: "Configurations",
                keyColumn: "Id",
                keyValue: new Guid("f9a6d733-d01b-4dec-ae22-b9a3c7bd5362"),
                column: "Key",
                value: "communication.notifications.requestupdate.recipient.tbvi");
        }
    }
}
