﻿using Microsoft.EntityFrameworkCore.Migrations;
using System.Reflection;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class StoredProcs20241014 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //// https://clearmeasure.com/creating-stored-procs-in-ef-migrations/
            //var assembly = Assembly.GetExecutingAssembly();
            //var resourceNames =
            //            assembly.GetManifestResourceNames().
            //            Where(str => str.EndsWith(".sql"));
            //foreach (string resourceName in resourceNames)
            //{
            //    using (Stream stream = assembly.GetManifestResourceStream(resourceName))
            //    using (StreamReader reader = new StreamReader(stream))
            //    {
            //        string sql = reader.ReadToEnd();
            //        migrationBuilder.Sql(sql);
            //    }
            //}
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
