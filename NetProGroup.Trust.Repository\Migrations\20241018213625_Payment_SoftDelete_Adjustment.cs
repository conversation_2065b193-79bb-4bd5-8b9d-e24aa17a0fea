using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class Payment_SoftDelete_Adjustment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<Guid>(
                name: "LegalEntityId",
                table: "Payments",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedAt",
                table: "Payments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ExpirationDate",
                table: "Payments",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2000, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "Payments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "JurisdictionId",
                table: "Payments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MasterClientId",
                table: "Payments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<TimeSpan>(
                name: "GatewayExpirationTimestamp",
                table: "PaymentProviders",
                type: "TIME",
                nullable: false,
                defaultValue: new TimeSpan(0, 0, 5, 0, 0));

            migrationBuilder.CreateIndex(
                name: "IX_Payments_JurisdictionId",
                table: "Payments",
                column: "JurisdictionId");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_MasterClientId",
                table: "Payments",
                column: "MasterClientId");

            migrationBuilder.AddForeignKey(
                name: "FK_Payment_Jurisdiction",
                table: "Payments",
                column: "JurisdictionId",
                principalTable: "Jurisdictions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Payment_MasterClient",
                table: "Payments",
                column: "MasterClientId",
                principalTable: "MasterClients",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payment_Jurisdiction",
                table: "Payments");

            migrationBuilder.DropForeignKey(
                name: "FK_Payment_MasterClient",
                table: "Payments");

            migrationBuilder.DropIndex(
                name: "IX_Payments_JurisdictionId",
                table: "Payments");

            migrationBuilder.DropIndex(
                name: "IX_Payments_MasterClientId",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "DeletedAt",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "ExpirationDate",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "JurisdictionId",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "MasterClientId",
                table: "Payments");

            migrationBuilder.DropColumn(
                name: "GatewayExpirationTimestamp",
                table: "PaymentProviders");

            migrationBuilder.AlterColumn<Guid>(
                name: "LegalEntityId",
                table: "Payments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);
        }
    }
}
