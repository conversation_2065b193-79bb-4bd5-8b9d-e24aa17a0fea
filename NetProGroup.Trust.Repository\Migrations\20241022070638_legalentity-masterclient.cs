﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class legalentitymasterclient : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LegacyCode",
                table: "MasterClients");

            migrationBuilder.RenameColumn(
                name: "UserEmail",
                table: "SyncMasterClient",
                newName: "UserEmailAddress");

            migrationBuilder.RenameColumn(
                name: "LegacyCode",
                table: "SyncMasterClient",
                newName: "ClientName");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "SyncMasterClient",
                newName: "ClientCode");

            migrationBuilder.RenameColumn(
                name: "ServiceOffice",
                table: "SyncCompany",
                newName: "ProductionOffice");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "SyncCompany",
                newName: "Manager");

            migrationBuilder.RenameColumn(
                name: "MasterClientCode",
                table: "SyncCompany",
                newName: "JurisdictionCode");

            migrationBuilder.RenameColumn(
                name: "IncorporationNr",
                table: "SyncCompany",
                newName: "IncorporationNumber");

            migrationBuilder.RenameColumn(
                name: "Code",
                table: "SyncCompany",
                newName: "EntityUniqueNr");

            migrationBuilder.RenameColumn(
                name: "ServiceOffice",
                table: "LegalEntityHistory",
                newName: "ProductionOffice");

            migrationBuilder.RenameColumn(
                name: "CompanyType",
                table: "LegalEntityHistory",
                newName: "Manager");

            migrationBuilder.RenameColumn(
                name: "ServiceOffice",
                table: "LegalEntities",
                newName: "ProductionOffice");

            migrationBuilder.RenameColumn(
                name: "CompanyType",
                table: "LegalEntities",
                newName: "Manager");

            migrationBuilder.AddColumn<string>(
                name: "Administrator",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityName",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityTypeCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Administrator",
                table: "LegalEntityHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityTypeCode",
                table: "LegalEntityHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "EntityTypeName",
                table: "LegalEntityHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExternalUniqueId",
                table: "LegalEntityHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Administrator",
                table: "LegalEntities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityTypeCode",
                table: "LegalEntities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "EntityTypeName",
                table: "LegalEntities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExternalUniqueId",
                table: "LegalEntities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Administrator",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "ClientCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityName",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityTypeCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "Administrator",
                table: "LegalEntityHistory");

            migrationBuilder.DropColumn(
                name: "EntityTypeCode",
                table: "LegalEntityHistory");

            migrationBuilder.DropColumn(
                name: "EntityTypeName",
                table: "LegalEntityHistory");

            migrationBuilder.DropColumn(
                name: "ExternalUniqueId",
                table: "LegalEntityHistory");

            migrationBuilder.DropColumn(
                name: "Administrator",
                table: "LegalEntities");

            migrationBuilder.DropColumn(
                name: "EntityTypeCode",
                table: "LegalEntities");

            migrationBuilder.DropColumn(
                name: "EntityTypeName",
                table: "LegalEntities");

            migrationBuilder.DropColumn(
                name: "ExternalUniqueId",
                table: "LegalEntities");

            migrationBuilder.RenameColumn(
                name: "UserEmailAddress",
                table: "SyncMasterClient",
                newName: "UserEmail");

            migrationBuilder.RenameColumn(
                name: "ClientName",
                table: "SyncMasterClient",
                newName: "LegacyCode");

            migrationBuilder.RenameColumn(
                name: "ClientCode",
                table: "SyncMasterClient",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "ProductionOffice",
                table: "SyncCompany",
                newName: "ServiceOffice");

            migrationBuilder.RenameColumn(
                name: "Manager",
                table: "SyncCompany",
                newName: "Name");

            migrationBuilder.RenameColumn(
                name: "JurisdictionCode",
                table: "SyncCompany",
                newName: "MasterClientCode");

            migrationBuilder.RenameColumn(
                name: "IncorporationNumber",
                table: "SyncCompany",
                newName: "IncorporationNr");

            migrationBuilder.RenameColumn(
                name: "EntityUniqueNr",
                table: "SyncCompany",
                newName: "Code");

            migrationBuilder.RenameColumn(
                name: "ProductionOffice",
                table: "LegalEntityHistory",
                newName: "ServiceOffice");

            migrationBuilder.RenameColumn(
                name: "Manager",
                table: "LegalEntityHistory",
                newName: "CompanyType");

            migrationBuilder.RenameColumn(
                name: "ProductionOffice",
                table: "LegalEntities",
                newName: "ServiceOffice");

            migrationBuilder.RenameColumn(
                name: "Manager",
                table: "LegalEntities",
                newName: "CompanyType");

            migrationBuilder.AddColumn<string>(
                name: "LegacyCode",
                table: "MasterClients",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);
        }
    }
}
