﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class bodir : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryOfFormation",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "OfficerType",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "CountryOfFormation",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "OfficerType",
                table: "DirectorHistory");

            migrationBuilder.RenameColumn(
                name: "SovereignState",
                table: "Directors",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "JurisdictionOfRegulator",
                table: "Directors",
                newName: "CompanyNumber");

            migrationBuilder.RenameColumn(
                name: "SovereignState",
                table: "DirectorHistory",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "JurisdictionOfRegulator",
                table: "DirectorHistory",
                newName: "CompanyNumber");

            migrationBuilder.RenameColumn(
                name: "StockExchange",
                table: "BeneficialOwners",
                newName: "StockExchangeName");

            migrationBuilder.RenameColumn(
                name: "StockCode",
                table: "BeneficialOwners",
                newName: "StockExchangeCode");

            migrationBuilder.RenameColumn(
                name: "CountryOfFormation",
                table: "BeneficialOwners",
                newName: "Country");

            migrationBuilder.RenameColumn(
                name: "StockExchange",
                table: "BeneficialOwnerHistory",
                newName: "StockExchangeName");

            migrationBuilder.RenameColumn(
                name: "StockCode",
                table: "BeneficialOwnerHistory",
                newName: "StockExchangeCode");

            migrationBuilder.RenameColumn(
                name: "CountryOfFormation",
                table: "BeneficialOwnerHistory",
                newName: "Country");

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "Directors",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorIsAlternateToCode",
                table: "Directors",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorIsAlternateToName",
                table: "Directors",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileType",
                table: "Directors",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "RelationType",
                table: "Directors",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "DirectorHistory",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorIsAlternateToCode",
                table: "DirectorHistory",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorIsAlternateToName",
                table: "DirectorHistory",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileType",
                table: "DirectorHistory",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "RelationType",
                table: "DirectorHistory",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "AppointmentDate",
                table: "BeneficialOwners",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CessationDate",
                table: "BeneficialOwners",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "BeneficialOwners",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyNumber",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileType",
                table: "BeneficialOwners",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FormerName",
                table: "BeneficialOwners",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServiceAddress",
                table: "BeneficialOwners",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AppointmentDate",
                table: "BeneficialOwnerHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CessationDate",
                table: "BeneficialOwnerHistory",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompanyNumber",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileType",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FormerName",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServiceAddress",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Code",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "DirectorIsAlternateToCode",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "DirectorIsAlternateToName",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "FileType",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "RelationType",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "DirectorIsAlternateToCode",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "DirectorIsAlternateToName",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "FileType",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "RelationType",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "AppointmentDate",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "CessationDate",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "CompanyNumber",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "FileType",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "FormerName",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "ServiceAddress",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "AppointmentDate",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "CessationDate",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "CompanyNumber",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "FileType",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "FormerName",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "ServiceAddress",
                table: "BeneficialOwnerHistory");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "Directors",
                newName: "SovereignState");

            migrationBuilder.RenameColumn(
                name: "CompanyNumber",
                table: "Directors",
                newName: "JurisdictionOfRegulator");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "DirectorHistory",
                newName: "SovereignState");

            migrationBuilder.RenameColumn(
                name: "CompanyNumber",
                table: "DirectorHistory",
                newName: "JurisdictionOfRegulator");

            migrationBuilder.RenameColumn(
                name: "StockExchangeName",
                table: "BeneficialOwners",
                newName: "StockExchange");

            migrationBuilder.RenameColumn(
                name: "StockExchangeCode",
                table: "BeneficialOwners",
                newName: "StockCode");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "BeneficialOwners",
                newName: "CountryOfFormation");

            migrationBuilder.RenameColumn(
                name: "StockExchangeName",
                table: "BeneficialOwnerHistory",
                newName: "StockExchange");

            migrationBuilder.RenameColumn(
                name: "StockExchangeCode",
                table: "BeneficialOwnerHistory",
                newName: "StockCode");

            migrationBuilder.RenameColumn(
                name: "Country",
                table: "BeneficialOwnerHistory",
                newName: "CountryOfFormation");

            migrationBuilder.AddColumn<string>(
                name: "CountryOfFormation",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficerType",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "CountryOfFormation",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficerType",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");
        }
    }
}
