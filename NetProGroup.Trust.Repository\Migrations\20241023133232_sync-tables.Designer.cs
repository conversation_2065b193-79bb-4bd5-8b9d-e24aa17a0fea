﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetProGroup.Trust.Domain.Repository;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    [DbContext(typeof(TrustDbContext))]
    [Migration("20241023133232_sync-tables")]
    partial class synctables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.ToTable("RoleClaims", "NetPro");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.ToTable("UserClaims", "NetPro");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.ToTable("UserLogins", "NetPro");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRoles", "NetPro");

                    b.HasData(
                        new
                        {
                            UserId = new Guid("6f886506-48cb-44b5-ad12-a2382e292795"),
                            RoleId = new Guid("62390aa0-4979-41f5-a5b9-e52c2790d05d")
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserTokens", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.ActivityLogs.EFModels.ActivityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("ActivityType")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("ActivityType");

                    b.Property<Guid?>("ContextId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid?>("CreatedByIdentityUserId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatedByIdentityUserId");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EntityId");

                    b.Property<string>("EntityName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("EntityName");

                    b.Property<string>("ShortDescription")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ShortDescription");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("Text");

                    b.HasKey("Id");

                    b.ToTable("ActivityLogs", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.EmailAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EnableSsl")
                        .HasColumnType("bit");

                    b.Property<string>("Host")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Password")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Port")
                        .HasColumnType("int");

                    b.Property<string>("SystemName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<bool>("UseDefaultCredentials")
                        .HasColumnType("bit");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("EmailAccounts", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.Inbox", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Body")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid?>("FromRoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("FromRoleId");

                    b.Property<Guid?>("FromUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("FromUserId");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Subject")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("ToRoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ToRoleId");

                    b.Property<Guid>("ToUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ToUserId");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("FromRoleId");

                    b.HasIndex("FromUserId");

                    b.HasIndex("ToRoleId");

                    b.HasIndex("ToUserId");

                    b.ToTable("Inboxes", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.InboxDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DocumentId");

                    b.Property<Guid>("InboxId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("InboxId");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.HasIndex("InboxId");

                    b.ToTable("InboxDocuments", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.MessageTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BccEmailAddresses")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BodyType")
                        .HasColumnType("int");

                    b.Property<string>("CcEmailAddresses")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("EmailAccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HtmlBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("PlainBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SystemName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ToEmailAddresses")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("MessageTemplates", "NetPro");

                    b.HasData(
                        new
                        {
                            Id = new Guid("9d59bd15-cd2f-4674-a3f5-ea6d9be11452"),
                            BodyType = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Message with the verification code for MFA.",
                            DisplayName = "MFACode Message.",
                            HtmlBody = "The verification code is: {code}",
                            IsActive = true,
                            PlainBody = "The verification code is: {code}",
                            Subject = "Your Trident Trust Verification Code",
                            SystemName = "MFACodeMessage",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("cd3b60ab-98c0-403c-9360-fdd93d2ab62b"),
                            BodyType = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Message with the verification code for reset of MFA.",
                            DisplayName = "MFACode Reset Message.",
                            HtmlBody = "The confirmation code for resetting MFA is: {code}",
                            IsActive = true,
                            PlainBody = "The confirmation code for resetting MFA is: {code}",
                            Subject = "Your Trident Trust Reset Verification Code",
                            SystemName = "MFAResetRequestMessage",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("598c0bc9-c358-402a-a9ec-04e7f7bca20d"),
                            BodyType = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Message for the inviation of a MasterClient User.",
                            DisplayName = "UserInvitation Message.",
                            HtmlBody = "<html>  <head> <meta http-equiv=Content-Type content=\"text/html; charset=unicode\"> <meta name=Generator content=\"Microsoft Word 15 (filtered)\"> <style> <!--  /* Font Definitions */  @font-face 	{font-family:Wingdings; 	panose-1:5 0 0 0 0 0 0 0 0 0;} @font-face 	{font-family:\"Cambria Math\"; 	panose-1:2 4 5 3 5 4 6 3 2 4;} @font-face 	{font-family:Aptos;} @font-face 	{font-family:\"Polaris Book\";}  /* Style Definitions */  p.MsoNormal, li.MsoNormal, div.MsoNormal 	{margin:0in; 	font-size:11.0pt; 	font-family:\"Aptos\",sans-serif;} h2 	{mso-style-link:\"Heading 2 Char\"; 	margin-right:0in; 	margin-left:0in; 	font-size:18.0pt; 	font-family:\"Aptos\",sans-serif; 	font-weight:bold;} h4 	{mso-style-link:\"Heading 4 Char\"; 	margin-right:0in; 	margin-left:0in; 	font-size:12.0pt; 	font-family:\"Aptos\",sans-serif; 	font-weight:bold;} a:link, span.MsoHyperlink 	{color:#467886; 	text-decoration:underline;} p 	{margin-right:0in; 	margin-left:0in; 	font-size:12.0pt; 	font-family:\"Aptos\",sans-serif;} span.Heading2Char 	{mso-style-name:\"Heading 2 Char\"; 	mso-style-link:\"Heading 2\"; 	font-family:\"Aptos\",sans-serif; 	font-weight:bold;} span.Heading4Char 	{mso-style-name:\"Heading 4 Char\"; 	mso-style-link:\"Heading 4\"; 	font-family:\"Aptos\",sans-serif; 	font-weight:bold;} .MsoChpDefault 	{font-size:10.0pt;} @page WordSection1 	{size:8.5in 11.0in; 	margin:1.0in 1.0in 1.0in 1.0in;} div.WordSection1 	{page:WordSection1;}  /* List Definitions */  ol 	{margin-bottom:0in;} ul 	{margin-bottom:0in;} --> </style>  </head>  <body lang=NL link=\"#467886\" vlink=\"#96607D\" style='word-wrap:break-word'>  <div class=WordSection1>  <h2><a name=\"_MailOriginal\"><span lang=EN-US style='font-size:24.0pt; font-family:\"Polaris Book\";color:#0082C3'>Private Client Portal</span></a></h2>  <p class=MsoNormal><span lang=EN-US style='font-size:12.0pt'>&nbsp;</span></p>  <h4><span lang=EN-US style='font-size:13.0pt;font-family:\"Polaris Book\"; color:#0082C3'>Registration Details:</span></h4>  <p><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>To complete the secure registration process and activate your account, please use the following credentials:</span></p>  <ul type=disc>  <li class=MsoNormal style='color:#233981'><span lang=EN-US style='font-size:      10.0pt;font-family:\"Polaris Book\"'>Your master client code : <span>{masterclient.code}</span></span></li>  <li class=MsoNormal style='color:#233981'><span lang=EN-US style='font-size:      10.0pt;font-family:\"Polaris Book\"'>Your registered email address : </span><a      href=\"mailto:{email}\"><span lang=EN-US style='font-size:10.0pt;      font-family:\"Polaris Book\"'>{email}</span></a></li> </ul>  <h4><span lang=EN-US style='font-size:13.0pt;font-family:\"Polaris Book\"; color:#0082C3'>Registration Process:</span></h4>  <ul type=disc>  <li class=MsoNormal style='color:#233981'><span lang=EN-US style='font-size:      10.0pt;font-family:\"Polaris Book\"'>Proceed to the address displayed on      this email: </span><a href=\"{portal.url}\"><span      lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"'>{portal.url}</span></a></li>  <li class=MsoNormal style='color:#233981'><span lang=EN-US style='font-size:      10.0pt;font-family:\"Polaris Book\"'>Click on <b>Register</b> and enter your      master client code and email address as indicated above (case sensitive).</span></li> </ul>  <p><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>A secure email will be generated with the final activation link to activate your account and create a password.</span><span lang=EN-US> </span></p>  <p><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>Additionally, there is an extra level of security: a 2-factor authentication code is required for further activation, and you may select to have the requisite codes generated via email or a downloaded authenticator app to your mobile device. A new code will be generated for each login session to the portal. If using an authentication app, we recommend the Google Authenticator App, which can be downloaded from the Play Store or Apple Store (iPhone). The downloaded app will be used to scan the QR code prompted on the portal upon first login. Once the app and portal are successfully synced, the app will generate a 6-digit code for activation of the account.</span></p>  <h4><span lang=EN-US style='font-size:13.0pt;font-family:\"Polaris Book\"; color:#0082C3'>Support</span></h4>  <p><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>For assistance with classification or data submission, please contact your usual Trident representative or email us at </span><a href=\"mailto:<EMAIL>\" target=\"_blank\" title=\"mailto:<EMAIL>\"><span lang=EN-US style='font-size: 10.0pt;font-family:\"Polaris Book\"'><EMAIL></span></a><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\";color:#233981'>.</span></p>  <h4><span lang=EN-US style='font-size:13.0pt;font-family:\"Polaris Book\"; color:#0082C3'>Data Protection</span></h4>  <p style='margin-top:12.0pt;margin-right:0in;margin-bottom:0in;margin-left: 0in'><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>As Registered Agent of your Entity, Trident will collect and process the Personal Data of individuals related to the Entity (i.e. directors, employees etc.) once such Personal Data is submitted using Trident’s data collection portal. This processing is required for compliance with regulatory requirements&nbsp; by both ourselves as the Registered Agent and the Entity and may include the transfer of Personal Data to BVI and foreign competent authorities where applicable.</span></p>  <p style='margin-top:12.0pt;margin-right:0in;margin-bottom:0in;margin-left: 0in'><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>Any processing of Personal Data by Trident will be conducted in accordance with the General Data Protection Regulation (EU) 2016/679, the </span><a href=\"https://www.tridenttrust.com/media/txjlcrd2/global-privacy-notice-v20240607.pdf#link\" title=\"https://tridenttrust.com/media/4396/ttg-gdpr.pdf\"><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"'>Trident Trust Group Fair Processing Notice</span></a><span lang=EN-US style='font-size:10.0pt; font-family:\"Polaris Book\";color:#233981'> and any applicable BVI law.</span></p>  <p style='margin-top:12.0pt;margin-right:0in;margin-bottom:0in;margin-left: 0in'><span lang=EN-US style='font-size:10.0pt;font-family:\"Polaris Book\"; color:#233981'>&nbsp;</span></p>  </div>  </body>  </html> ",
                            IsActive = true,
                            PlainBody = "Private Client Portal  Registration Details: To complete the secure registration process and activate your account, please use the following credentials: •	Your master client code : {masterclient.code} •	Your registered email address : {email}  Registration Process: •	Proceed to the address displayed on this email: {portal.url} •	Click on Register and enter your master client code and email address as indicated above (case sensitive).  A secure email will be generated with the final activation link to activate your account and create a password.   Additionally, there is an extra level of security: a 2-factor authentication code is required for further activation, and you may select to have the requisite codes generated via email or a downloaded authenticator app to your mobile device. A new code will be generated for each login session to the portal. If using an authentication app, we recommend the Google Authenticator App, which can be downloaded from the Play Store or Apple Store (iPhone). The downloaded app will be used to scan the QR code prompted on the portal upon first login. Once the app and portal are successfully synced, the app will generate a 6-digit code for activation of the account.  Support  For assistance with classification or data submission, please contact your usual Trident representative or email <NAME_EMAIL>.  Data Protection  As Registered Agent of your Entity, Trident will collect and process the Personal Data of individuals related to the Entity (i.e. directors, employees etc.) once such Personal Data is submitted using Trident’s data collection portal. This processing is required for compliance with regulatory requirements  by both ourselves as the Registered Agent and the Entity and may include the transfer of Personal Data to BVI and foreign competent authorities where applicable.  Any processing of Personal Data by Trident will be conducted in accordance with the General Data Protection Regulation (EU) 2016/679, the Trident Trust Group Fair Processing Notice and any applicable BVI law. ",
                            Subject = "Inviation for Portal",
                            SystemName = "UserInvitationMessage",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("a68733e2-de6b-4f82-bb0a-f8f547be3183"),
                            BodyType = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Message for notification of a 'request for update'.",
                            DisplayName = "RequestUpdate Message.",
                            HtmlBody = "<html>  <head> <meta http-equiv=Content-Type content=\"text/html; charset=windows-1252\"> <meta name=Generator content=\"Microsoft Word 15 (filtered)\"> <style> <!--  /* Font Definitions */  @font-face 	{font-family:\"Cambria Math\"; 	panose-1:2 4 5 3 5 4 6 3 2 4;} @font-face 	{font-family:Aptos;}  /* Style Definitions */  p.MsoNormal, li.MsoNormal, div.MsoNormal 	{margin-top:0in; 	margin-right:0in; 	margin-bottom:8.0pt; 	margin-left:0in; 	line-height:107%; 	font-size:11.0pt; 	font-family:\"Aptos\",sans-serif;} .MsoChpDefault 	{font-size:11.0pt;} .MsoPapDefault 	{margin-bottom:8.0pt; 	line-height:107%;} @page WordSection1 	{size:595.3pt 841.9pt; 	margin:70.85pt 70.85pt 70.85pt 70.85pt;} div.WordSection1 	{page:WordSection1;} --> </style>  </head>  <body lang=NL style='word-wrap:break-word'>  <div class=WordSection1>  <p class=MsoNormal><b><span lang=EN-SG>Entity Name</span></b><span lang=EN-SG>:&nbsp;{company.name} <br> <b>Entity Code</b>:&nbsp;{company.code}<br> <b>Master Client Code</b>:&nbsp;{masterclient.code}<br> <b>VP&nbsp;</b></span><b><span lang=EN-US>{masterfile.label} Masterfile Code:&nbsp;</span></b><span lang=EN-US>{masterfile.code}<br> <b>Requestor:&nbsp;</b>{requestor} <br> <b>Position:&nbsp;</b>{position} <br> <b>Type of Request:&nbsp;</b>{request}<br> <b>Comment:&nbsp;</b>{comment}</span></p>  <p class=MsoNormal><span lang=EN-US>&nbsp;</span></p>  </div>  </body>  </html> ",
                            IsActive = true,
                            PlainBody = "Entity Name: {company.name}  Entity Code: {company.code} Master Client Code: {masterclient.code} VP {masterfile.label} Masterfile Code: {masterfile.code} Requestor: {requestor}  Position: {position}  Type of Request: {request} Comment: {comment}  ",
                            Subject = "{company.name} - Request update for Portal",
                            SystemName = "RequestUpdateMessage",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("c382294b-67cb-456d-a59b-986cddaaeac3"),
                            BodyType = 0,
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Message for notification of a 'request for assistance'.",
                            DisplayName = "RequestAssistance Message.",
                            HtmlBody = "<html>  <head> <meta http-equiv=Content-Type content=\"text/html; charset=windows-1252\"> <meta name=Generator content=\"Microsoft Word 15 (filtered)\"> <style> <!--  /* Font Definitions */  @font-face 	{font-family:\"Cambria Math\"; 	panose-1:2 4 5 3 5 4 6 3 2 4;} @font-face 	{font-family:Aptos;}  /* Style Definitions */  p.MsoNormal, li.MsoNormal, div.MsoNormal 	{margin-top:0in; 	margin-right:0in; 	margin-bottom:8.0pt; 	margin-left:0in; 	line-height:107%; 	font-size:11.0pt; 	font-family:\"Aptos\",sans-serif;} .MsoChpDefault 	{font-size:11.0pt;} .MsoPapDefault 	{margin-bottom:8.0pt; 	line-height:107%;} @page WordSection1 	{size:595.3pt 841.9pt; 	margin:70.85pt 70.85pt 70.85pt 70.85pt;} div.WordSection1 	{page:WordSection1;} --> </style>  </head>  <body lang=NL style='word-wrap:break-word'>  <div class=WordSection1>  <p class=MsoNormal><b><span lang=EN-SG>Entity Name</span></b><span lang=EN-SG>:&nbsp;{company.name} <br> <b>Entity Code</b>:&nbsp;{company.code}<br> <b>Master Client Code</b>:&nbsp;{masterclient.code}</span><span lang=EN-US><br> <b>Requestor</b>:&nbsp;</span><span lang=EN-SG>{requestor}</span><span lang=EN-US><br> <b>Type of Request</b>: {request}</span></p>  </div>  </body>  </html> ",
                            IsActive = true,
                            PlainBody = "Entity Name: {company.name}  Entity Code: {company.code} Master Client Code: {masterclient.code} Requestor: {requestor} Type of Request: {request} ",
                            Subject = "{company.name} - {issue} found in the Portal",
                            SystemName = "RequestAssistanceMessage",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.QueuedMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("AttachmentData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Bcc")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CC")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid?>("EmailAccountId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("From")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FromName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("HtmlBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MessageType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("NextAttempt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PlainBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Priority")
                        .HasColumnType("int");

                    b.Property<string>("ReplyTo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ReplyToName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("SentTries")
                        .HasColumnType("int");

                    b.Property<string>("Subject")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("To")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ToName")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.ToTable("QueuedMessages", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Configuration.EFModels.Configuration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId");

                    b.ToTable("Configurations", "NetPro");

                    b.HasData(
                        new
                        {
                            Id = new Guid("bbcf4766-03d3-40e4-825e-0f9e21133174"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Key = "communication.notifications.updaterequest.recipient.default",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Value = "<EMAIL>"
                        },
                        new
                        {
                            Id = new Guid("3fed38cc-1cb7-4846-80b2-ae7c93b1bd75"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Key = "communication.notifications.updaterequest.recipient.tpanvg",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Value = "<EMAIL>"
                        },
                        new
                        {
                            Id = new Guid("1d3e063b-c066-499e-a7ae-fc1793673dd4"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Key = "communication.notifications.updaterequest.recipient.tcyp",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Value = "<EMAIL>"
                        },
                        new
                        {
                            Id = new Guid("7bacfbc7-e987-4f05-bae8-cc9efbbc151d"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Key = "communication.notifications.updaterequest.recipient.thko",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Value = "<EMAIL>"
                        },
                        new
                        {
                            Id = new Guid("f9a6d733-d01b-4dec-ae22-b9a3c7bd5362"),
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Key = "communication.notifications.updaterequest.recipient.tbvi",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Value = "<EMAIL>"
                        });
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Documents.EFModels.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<DateTime>("AddedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("AddedAt");

                    b.Property<string>("BlobName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BlobPath")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BlobPath");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Container")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("Container");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(999)
                        .HasColumnType("nvarchar(999)")
                        .HasColumnName("Description");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("FileSize");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("Filename");

                    b.Property<string>("Hash")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Hash");

                    b.Property<bool>("MarkedForDelete")
                        .HasColumnType("bit");

                    b.Property<string>("ObjectState")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StorageAccount")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("StorageAccount");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("Type");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.ToTable("Documents", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Documents.EFModels.DocumentQueue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<byte[]>("Data")
                        .HasColumnType("varbinary(max)");

                    b.Property<Guid>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DocumentId")
                        .IsUnique();

                    b.ToTable("DocumentsQueue", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWork", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<DateTime>("ChangedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("ChangedBy");

                    b.Property<Guid?>("ChangedByIdentityUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContextId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("AuditUnitOfWork", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWorkDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid>("AuditUnitOfWorkEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Column")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("Column");

                    b.Property<string>("NewValue")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("NewValue");

                    b.Property<string>("OldValue")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("OldValue");

                    b.HasKey("Id");

                    b.HasIndex("AuditUnitOfWorkEntityId");

                    b.ToTable("AuditUnitOfWorkDetails", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWorkEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)")
                        .HasColumnName("Action");

                    b.Property<Guid>("AuditUnitOfWorkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EntityId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("EntityId");

                    b.Property<string>("EntityName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("EntityName");

                    b.HasKey("Id");

                    b.HasIndex("AuditUnitOfWorkId");

                    b.HasIndex(new[] { "EntityId" }, "IX_EntityId");

                    b.HasIndex(new[] { "EntityName" }, "IX_EntityName");

                    b.ToTable("AuditUnitOfWorkEntities", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Identity.EFModels.ApplicationRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid?>("ObjectId")
                        .HasMaxLength(256)
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Roles", "NetPro");

                    b.HasData(
                        new
                        {
                            Id = new Guid("62390aa0-4979-41f5-a5b9-e52c2790d05d"),
                            ConcurrencyStamp = "62390aa0-4979-41f5-a5b9-e52c2790d05d",
                            DisplayName = "System",
                            Name = "System",
                            NormalizedName = "SYSTEM"
                        });
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ObjectId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Surname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Users", "NetPro");

                    b.HasData(
                        new
                        {
                            Id = new Guid("6f886506-48cb-44b5-ad12-a2382e292795"),
                            AccessFailedCount = 0,
                            EmailConfirmed = false,
                            IsActive = true,
                            LockoutEnabled = false,
                            Name = "System",
                            PhoneNumberConfirmed = false,
                            Surname = "System",
                            TwoFactorEnabled = false
                        },
                        new
                        {
                            Id = new Guid("d3e856e6-4bdc-4412-95af-22f4b188d1b2"),
                            AccessFailedCount = 0,
                            EmailConfirmed = false,
                            IsActive = true,
                            LockoutEnabled = false,
                            Name = "Inbox",
                            PhoneNumberConfirmed = false,
                            Surname = "Inbox",
                            TwoFactorEnabled = false
                        });
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Locks.EFModels.Lock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("EntityId");

                    b.Property<string>("EntityName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("EntityName");

                    b.Property<Guid>("IdentityUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IdentityUserId");

                    b.Property<DateTime>("LastRefreshAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ObjectState")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Session")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)")
                        .HasColumnName("Session");

                    b.HasKey("Id");

                    b.HasIndex("IdentityUserId");

                    b.HasIndex("EntityName", "EntityId")
                        .IsUnique();

                    b.ToTable("Locks", "NetPro");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Currencies.Currency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.ToTable("Currencies", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("6ca5809f-06a3-4e99-9e0a-df570d3a482f"),
                            Code = "USD",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "United States Dollar",
                            Symbol = "$",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("a8ef9d12-0e2c-4231-8cb0-98f78513a8c3"),
                            Code = "EUR",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Euro",
                            Symbol = "€",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("b8c2e7a4-66c7-488c-9d02-6a4199b3e199"),
                            Code = "GBP",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "British Pound",
                            Symbol = "£",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("3f8f100d-5707-4756-9f0e-3541d07ac497"),
                            Code = "JPY",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Japanese Yen",
                            Symbol = "¥",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("bd4826fb-5562-472e-bd5c-8f0a3ffae49a"),
                            Code = "CHF",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Swiss Franc",
                            Symbol = "CHF",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("9e54f888-4d24-4c1d-9d0d-6b8b1d0b92ef"),
                            Code = "CAD",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Canadian Dollar",
                            Symbol = "$",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("c3b4b7f4-11b5-4b9f-9f66-1701b82b2e69"),
                            Code = "AUD",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Australian Dollar",
                            Symbol = "$",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("7b4e8c6e-5a7b-47a7-99ea-22d8b415d5f7"),
                            Code = "CNY",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Chinese Yuan",
                            Symbol = "¥",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<DateTime?>("FinalizedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("FormTemplateVersionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<int?>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FormTemplateVersionId");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("ModuleId");

                    b.ToTable("FormDocuments", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocumentAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("FormDocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("FormDocumentId");

                    b.ToTable("FormDocumentAttributes", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocumentRevision", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("DataAsJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("FormDocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Revision")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("FormDocumentId");

                    b.ToTable("FormDocumentRevisions", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.HasIndex("ModuleId");

                    b.ToTable("FormTemplates", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormTemplateVersion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("DataAsJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("FormTemplateId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("StartAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FormTemplateId");

                    b.ToTable("FormTemplateVersions", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.SubmissionAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId");

                    b.ToTable("SubmissionAttributes", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Inboxes.InboxOwner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("InboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("OwnerType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("InboxId");

                    b.ToTable("InboxOwners", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Inboxes.InboxReadStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("InboxId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("ReadAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("InboxId");

                    b.ToTable("InboxReadStatuses", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.ToTable("Jurisdictions", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("bdef352d-dedc-4271-888d-efa168404ce9"),
                            Code = "Nevis",
                            CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "Nevis",
                            UpdatedAt = new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Jurisdictions.JurisdictionTaxRate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("TaxRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.ToTable("JurisdictionTaxRates", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.BeneficialOwner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("AppointmentDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CessationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Code")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("CompanyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalUniqueId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FormerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("IncorporationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IncorporationNr")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsIndividual")
                        .HasColumnType("bit");

                    b.Property<string>("JurisdictionOfRegulator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("NameOfRegulator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OfficerType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PlaceOfBirth")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ResidentialAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ServiceAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("SovereignState")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StockExchangeCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StockExchangeName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TIN")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("LegalEntityId");

                    b.ToTable("BeneficialOwners", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.BeneficialOwnerHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("AppointmentDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CessationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Code")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("CompanyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ConfirmedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalUniqueId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FormerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("IncorporationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IncorporationNr")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsIndividual")
                        .HasColumnType("bit");

                    b.Property<string>("JurisdictionOfRegulator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("NameOfRegulator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("OfficerType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PlaceOfBirth")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ReceivedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ResidentialAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ServiceAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("SovereignState")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StockExchangeCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("StockExchangeName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TIN")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UpdateRequestComments")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<int?>("UpdateRequestType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateRequestedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateRequestedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("ConfirmedByUserId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CreatedAt");

                    b.HasIndex("ExternalUniqueId")
                        .HasDatabaseName("IX_ExternalUniqueId");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("UpdateRequestedByUserId");

                    b.ToTable("BeneficialOwnerHistory", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.Director", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("AppointmentDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CessationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Code")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("CompanyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("DirectorIsAlternateToCode")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("DirectorIsAlternateToName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ExternalUniqueId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FormerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("IncorporationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IncorporationNr")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IncorporationPlace")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsIndividual")
                        .HasColumnType("bit");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PlaceOfBirth")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RelationType")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("ResidentialAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ServiceAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("TIN")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("LegalEntityId");

                    b.ToTable("Directors", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.DirectorHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Address")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("AppointmentDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CessationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Code")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("CompanyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ConfirmedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("DirectorIsAlternateToCode")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("DirectorIsAlternateToName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ExternalUniqueId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FormerName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("IncorporationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IncorporationNr")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IncorporationPlace")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsIndividual")
                        .HasColumnType("bit");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PlaceOfBirth")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ReceivedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("RelationType")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("ResidentialAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ServiceAddress")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TIN")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UpdateRequestComments")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<int?>("UpdateRequestType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateRequestedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateRequestedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("ConfirmedByUserId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CreatedAt");

                    b.HasIndex("ExternalUniqueId")
                        .HasDatabaseName("IX_ExternalUniqueId");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("UpdateRequestedByUserId");

                    b.ToTable("DirectorHistory", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Administrator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("EntityStatus")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EntitySubStatus")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("EntityType")
                        .HasColumnType("int");

                    b.Property<string>("EntityTypeCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EntityTypeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ExternalUniqueId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("IncorporationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IncorporationNr")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<Guid?>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("JurisdictionOfRegistration")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LegacyCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Manager")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MasterClientCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("MasterClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ProductionOffice")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReferralOffice")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RiskGroup")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.HasIndex("MasterClientId");

                    b.ToTable("LegalEntities", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.LegalEntityHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Administrator")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ConfirmedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("EntityStatus")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EntitySubStatus")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("EntityType")
                        .HasColumnType("int");

                    b.Property<string>("EntityTypeCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EntityTypeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ExternalUniqueId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("IncorporationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IncorporationNr")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("JurisdictionOfRegistration")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LegacyCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Manager")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MasterClientCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ProductionOffice")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ReceivedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReferralOffice")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RiskGroup")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("ConfirmedByUserId");

                    b.ToTable("LegalEntityHistory", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.Shareholder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("ExternalUniqueId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("LegalEntityId");

                    b.ToTable("Shareholders", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.ShareholderHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("ConfirmedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("ExternalUniqueId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("ReceivedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("UpdateRequestComments")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<int?>("UpdateRequestType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateRequestedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateRequestedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("ConfirmedByUserId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CreatedAt");

                    b.HasIndex("ExternalUniqueId")
                        .HasDatabaseName("IX_ExternalUniqueId");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("UpdateRequestedByUserId");

                    b.ToTable("ShareholderHistory", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.MasterClients.MasterClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<bool?>("IsActive")
                        .HasColumnType("bit");

                    b.Property<Guid?>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.ToTable("MasterClients", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.MasterClients.MasterClientUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("MasterClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("MasterClientId");

                    b.HasIndex("UserId");

                    b.ToTable("MasterClientUsers", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Modules.JurisdictionModule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<bool?>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<Guid>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.HasIndex("ModuleId");

                    b.ToTable("JurisdictionModules", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Modules.LegalEntityModule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<bool?>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("ModuleId");

                    b.ToTable("LegalEntityModules", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Modules.Module", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.ToTable("Modules", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Invoices.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("CurrencyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDue")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("FinancialYear")
                        .HasColumnType("int");

                    b.Property<string>("InvoiceNr")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("DocumentId");

                    b.HasIndex("LegalEntityId");

                    b.ToTable("Invoices", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Invoices.InvoiceLine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("CurrencyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("InvoiceId");

                    b.ToTable("InvoiceLines", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("CurrencyId")
                        .HasMaxLength(3)
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("CurrencyId");

                    b.HasIndex("LegalEntityId");

                    b.ToTable("Payments", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.PaymentInvoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PaymentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("PaymentId");

                    b.ToTable("PaymentInvoices", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.PaymentTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("Address")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CardDigits")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("City")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Company")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Email")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("IsFinished")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime?>("PaidAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("PaymentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("PaymentProviderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime?>("ProcessCreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Result")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ResultCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ResultMessage")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("State")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Status")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("PaymentId");

                    b.HasIndex("PaymentProviderId");

                    b.ToTable("PaymentTransactions", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Provider.PaymentProvider", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasColumnType("varchar(MAX)");

                    b.Property<string>("ApiSecret")
                        .IsRequired()
                        .HasColumnType("varchar(MAX)");

                    b.Property<string>("BaseUrl")
                        .IsRequired()
                        .HasColumnType("varchar(MAX)");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("varchar(MAX)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("varchar(250)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.ToTable("PaymentProviders", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Scheduling.ScheduledJob", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<string>("CronExpression")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("LastRunAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("ScheduledJobs", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Settings.Setting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid?>("JurisdictionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("MasterClientId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("JurisdictionId");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("MasterClientId");

                    b.ToTable("Settings", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Submissions.Submission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<int?>("FinancialYear")
                        .HasColumnType("int");

                    b.Property<Guid?>("FormDocumentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("InvoiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("IsPaid")
                        .HasColumnType("bit");

                    b.Property<Guid>("LegalEntityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ModuleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("FormDocumentId");

                    b.HasIndex("InvoiceId");

                    b.HasIndex("LegalEntityId");

                    b.HasIndex("ModuleId");

                    b.ToTable("Submissions", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.SyncStoredProcedures.SyncBenificialOwner", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BOServiceAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CorporateRegistrationNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerFormerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerFromDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerIncorpDateOrDOB")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerIncorpNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerIncorpPlaceOrBirthPlace")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerNationality")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerRegisteredAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerRegisteredCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerRegulationCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerTIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerToDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityOwnerUniqueNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityUniqueNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NameOfRegulator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RelationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockExchangeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StockExchangeName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SyncBenificialOwner", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.SyncStoredProcedures.SyncCompany", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Administrator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntitySubStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityUniqueNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IncorporationDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IncorporationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JurisdictionCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegacyCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Manager")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductionOffice")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RefrerralOffice")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskGroup")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SyncCompany", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.SyncStoredProcedures.SyncDirector", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ClientCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CorporateRegistrationNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirFileType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirFormerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirFromDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirIncorpDateOrDOB")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirIncorpPlaceOrBirthPlace")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirIncorporationNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirNationality")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirServiceAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirTIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirToDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirUniqueNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirectorIsAlternateToCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DirectorIsAlternateToName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityUniqueNr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RelationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResidentialOrRegisteredAddress")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SyncDirector", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.SyncStoredProcedures.SyncMasterClient", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SyncMasterClient", (string)null);
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Users.UserAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("newid()");

                    b.Property<Guid?>("ConcurrencyStamp")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserAttributes", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", null)
                        .WithMany("ApplicationUserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.Inbox", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationRole", "FromRole")
                        .WithMany()
                        .HasForeignKey("FromRoleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "FromUser")
                        .WithMany()
                        .HasForeignKey("FromUserId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationRole", "ToRole")
                        .WithMany()
                        .HasForeignKey("ToRoleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "ToUser")
                        .WithMany()
                        .HasForeignKey("ToUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("FromRole");

                    b.Navigation("FromUser");

                    b.Navigation("ToRole");

                    b.Navigation("ToUser");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.InboxDocument", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Documents.EFModels.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("NetProGroup.Framework.Services.Communication.EFModels.Inbox", "Inbox")
                        .WithMany("Documents")
                        .HasForeignKey("InboxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");

                    b.Navigation("Inbox");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Configuration.EFModels.Configuration", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Documents.EFModels.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Document");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Documents.EFModels.DocumentQueue", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Documents.EFModels.Document", "Document")
                        .WithOne("DocumentQueue")
                        .HasForeignKey("NetProGroup.Framework.Services.Documents.EFModels.DocumentQueue", "DocumentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Document");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWorkDetail", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWorkEntity", "UnitOfWorkEntity")
                        .WithMany("Details")
                        .HasForeignKey("AuditUnitOfWorkEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UnitOfWorkEntity");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWorkEntity", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWork", "UnitOfWork")
                        .WithMany("Entities")
                        .HasForeignKey("AuditUnitOfWorkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UnitOfWork");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocument", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Forms.FormTemplateVersion", "FormTemplateVersion")
                        .WithMany()
                        .HasForeignKey("FormTemplateVersionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_FormDocument_FormTemplateVersion");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_FormDocument_LegalEntity");

                    b.HasOne("NetProGroup.Trust.Domain.Modules.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_FormDocument_Module");

                    b.Navigation("FormTemplateVersion");

                    b.Navigation("LegalEntity");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocumentAttribute", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Forms.FormDocument", "FormDocument")
                        .WithMany("Attributes")
                        .HasForeignKey("FormDocumentId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_FormDocumentAttribute_FormDocument");

                    b.Navigation("FormDocument");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocumentRevision", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Forms.FormDocument", "FormDocument")
                        .WithMany("FormDocumentRevisions")
                        .HasForeignKey("FormDocumentId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_FormDocumentRevision_FormDocument");

                    b.Navigation("FormDocument");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormTemplate", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", "Jurisdiction")
                        .WithMany()
                        .HasForeignKey("JurisdictionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_FormTemplate_Jurisdiction");

                    b.HasOne("NetProGroup.Trust.Domain.Modules.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_FormTemplate_Module");

                    b.Navigation("Jurisdiction");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormTemplateVersion", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Forms.FormTemplate", "FormTemplate")
                        .WithMany("FormTemplateVersions")
                        .HasForeignKey("FormTemplateId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_FormTemplateVersion_FormTemplate");

                    b.Navigation("FormTemplate");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.SubmissionAttribute", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Submissions.Submission", "Submission")
                        .WithMany("Attributes")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_SubmissionAttribute_Submission");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Inboxes.InboxOwner", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Communication.EFModels.Inbox", "Inbox")
                        .WithMany()
                        .HasForeignKey("InboxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InboxOwner_Inbox");

                    b.Navigation("Inbox");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Inboxes.InboxReadStatus", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Communication.EFModels.Inbox", "Inbox")
                        .WithMany()
                        .HasForeignKey("InboxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InboxReadStatus_Inbox");

                    b.Navigation("Inbox");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Jurisdictions.JurisdictionTaxRate", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", "Jurisdiction")
                        .WithMany("TaxRates")
                        .HasForeignKey("JurisdictionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Jurisdiction");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.BeneficialOwner", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_LegalEntity_BeneficialOwner");

                    b.Navigation("LegalEntity");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.BeneficialOwnerHistory", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "ConfirmedByUser")
                        .WithMany()
                        .HasForeignKey("ConfirmedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_ConfirmedByUser_BeneficialOwnerHistory");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "UpdateRequestedByUser")
                        .WithMany()
                        .HasForeignKey("UpdateRequestedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_UpdateRequestedByUser_BeneficialOwnerHistory");

                    b.Navigation("ConfirmedByUser");

                    b.Navigation("LegalEntity");

                    b.Navigation("UpdateRequestedByUser");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.Director", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_LegalEntity_Director");

                    b.Navigation("LegalEntity");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.DirectorHistory", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "ConfirmedByUser")
                        .WithMany()
                        .HasForeignKey("ConfirmedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_ConfirmedByUser_DirectorHistory");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "UpdateRequestedByUser")
                        .WithMany()
                        .HasForeignKey("UpdateRequestedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_UpdateRequestedByUser_DirectorHistory");

                    b.Navigation("ConfirmedByUser");

                    b.Navigation("LegalEntity");

                    b.Navigation("UpdateRequestedByUser");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", "Jurisdiction")
                        .WithMany()
                        .HasForeignKey("JurisdictionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_LegalEntity_Jurisdiction");

                    b.HasOne("NetProGroup.Trust.Domain.MasterClients.MasterClient", "MasterClient")
                        .WithMany("LegalEntities")
                        .HasForeignKey("MasterClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_LegalEntity_MasterClient");

                    b.Navigation("Jurisdiction");

                    b.Navigation("MasterClient");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.LegalEntityHistory", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "ConfirmedByUser")
                        .WithMany()
                        .HasForeignKey("ConfirmedByUserId");

                    b.Navigation("ConfirmedByUser");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.Shareholder", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_LegalEntity_Shareholder");

                    b.Navigation("LegalEntity");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.ShareholderHistory", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "ConfirmedByUser")
                        .WithMany()
                        .HasForeignKey("ConfirmedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_ConfirmedByUser_ShareholderHistory");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "UpdateRequestedByUser")
                        .WithMany()
                        .HasForeignKey("UpdateRequestedByUserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_UpdateRequestedByUser_ShareholderHistory");

                    b.Navigation("ConfirmedByUser");

                    b.Navigation("LegalEntity");

                    b.Navigation("UpdateRequestedByUser");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.MasterClients.MasterClient", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", null)
                        .WithMany("MasterClients")
                        .HasForeignKey("JurisdictionId");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.MasterClients.MasterClientUser", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.MasterClients.MasterClient", "MasterClient")
                        .WithMany("MasterClientUsers")
                        .HasForeignKey("MasterClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_MasterClientUser_MasterClient");

                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_MasterClientUser_ApplicationUser");

                    b.Navigation("MasterClient");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Modules.JurisdictionModule", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", "Jurisdiction")
                        .WithMany("JurisdictionModules")
                        .HasForeignKey("JurisdictionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_JurisdictionModule_Jurisdiction");

                    b.HasOne("NetProGroup.Trust.Domain.Modules.Module", "Module")
                        .WithMany("JurisdictionModules")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_JurisdictionModule_Module");

                    b.Navigation("Jurisdiction");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Modules.LegalEntityModule", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany("LegalEntityModules")
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_LegalEntityModule_LegalEntity");

                    b.HasOne("NetProGroup.Trust.Domain.Modules.Module", "Module")
                        .WithMany("LegalEntityModules")
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_LegalEntityModule_Module");

                    b.Navigation("LegalEntity");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Invoices.Invoice", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Currencies.Currency", "Currency")
                        .WithMany()
                        .HasForeignKey("CurrencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Invoice_Currency");

                    b.HasOne("NetProGroup.Framework.Services.Documents.EFModels.Document", "Document")
                        .WithMany()
                        .HasForeignKey("DocumentId");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Invoice_LegalEntity");

                    b.Navigation("Currency");

                    b.Navigation("Document");

                    b.Navigation("LegalEntity");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Invoices.InvoiceLine", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Currencies.Currency", "Currency")
                        .WithMany()
                        .HasForeignKey("CurrencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_InvoiceLine_Currency");

                    b.HasOne("NetProGroup.Trust.Domain.Payments.Invoices.Invoice", "Invoice")
                        .WithMany("InvoiceLines")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_InvoiceLine_Invoice");

                    b.Navigation("Currency");

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Payment", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Currencies.Currency", "Currency")
                        .WithMany()
                        .HasForeignKey("CurrencyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Payment_Currency");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_Payment_LegalEntity");

                    b.Navigation("Currency");

                    b.Navigation("LegalEntity");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.PaymentInvoice", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Payments.Invoices.Invoice", "Invoice")
                        .WithMany("PaymentInvoices")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentInvoice_Invoice");

                    b.HasOne("NetProGroup.Trust.Domain.Payments.Payment", "Payment")
                        .WithMany("PaymentInvoices")
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentInvoice_Payment");

                    b.Navigation("Invoice");

                    b.Navigation("Payment");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.PaymentTransaction", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Payments.Payment", "Payment")
                        .WithMany("PaymentTransactions")
                        .HasForeignKey("PaymentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentTransaction_Payment");

                    b.HasOne("NetProGroup.Trust.Domain.Payments.Provider.PaymentProvider", "PaymentProvider")
                        .WithMany("PaymentTransactions")
                        .HasForeignKey("PaymentProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("FK_PaymentTransaction_PaymentProvider");

                    b.Navigation("Payment");

                    b.Navigation("PaymentProvider");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Provider.PaymentProvider", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", "Jurisdiction")
                        .WithMany("PaymentProviders")
                        .HasForeignKey("JurisdictionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Jurisdiction");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Settings.Setting", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", "Jurisdiction")
                        .WithMany()
                        .HasForeignKey("JurisdictionId")
                        .HasConstraintName("FK_Jurisdictions_Settings");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .HasConstraintName("FK_LegalEntities_Settings");

                    b.HasOne("NetProGroup.Trust.Domain.MasterClients.MasterClient", "MasterClient")
                        .WithMany()
                        .HasForeignKey("MasterClientId")
                        .HasConstraintName("FK_MasterClients_Settings");

                    b.Navigation("Jurisdiction");

                    b.Navigation("LegalEntity");

                    b.Navigation("MasterClient");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Submissions.Submission", b =>
                {
                    b.HasOne("NetProGroup.Trust.Domain.Forms.FormDocument", "FormDocument")
                        .WithMany()
                        .HasForeignKey("FormDocumentId")
                        .HasConstraintName("FK_Submission_FormDocument");

                    b.HasOne("NetProGroup.Trust.Domain.Payments.Invoices.Invoice", "Invoice")
                        .WithMany()
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_Submission_Invoice");

                    b.HasOne("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", "LegalEntity")
                        .WithMany()
                        .HasForeignKey("LegalEntityId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_Submission_LegalEntity");

                    b.HasOne("NetProGroup.Trust.Domain.Modules.Module", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_Submission_Module");

                    b.Navigation("FormDocument");

                    b.Navigation("Invoice");

                    b.Navigation("LegalEntity");

                    b.Navigation("Module");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Users.UserAttribute", b =>
                {
                    b.HasOne("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_UserAttributes_Users");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Communication.EFModels.Inbox", b =>
                {
                    b.Navigation("Documents");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Documents.EFModels.Document", b =>
                {
                    b.Navigation("DocumentQueue");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWork", b =>
                {
                    b.Navigation("Entities");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.EFAuditing.EFModels.AuditUnitOfWorkEntity", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("NetProGroup.Framework.Services.Identity.EFModels.ApplicationUser", b =>
                {
                    b.Navigation("ApplicationUserRoles");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormDocument", b =>
                {
                    b.Navigation("Attributes");

                    b.Navigation("FormDocumentRevisions");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Forms.FormTemplate", b =>
                {
                    b.Navigation("FormTemplateVersions");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Jurisdictions.Jurisdiction", b =>
                {
                    b.Navigation("JurisdictionModules");

                    b.Navigation("MasterClients");

                    b.Navigation("PaymentProviders");

                    b.Navigation("TaxRates");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.LegalEntities.LegalEntity", b =>
                {
                    b.Navigation("LegalEntityModules");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.MasterClients.MasterClient", b =>
                {
                    b.Navigation("LegalEntities");

                    b.Navigation("MasterClientUsers");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Modules.Module", b =>
                {
                    b.Navigation("JurisdictionModules");

                    b.Navigation("LegalEntityModules");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Invoices.Invoice", b =>
                {
                    b.Navigation("InvoiceLines");

                    b.Navigation("PaymentInvoices");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Payment", b =>
                {
                    b.Navigation("PaymentInvoices");

                    b.Navigation("PaymentTransactions");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Payments.Provider.PaymentProvider", b =>
                {
                    b.Navigation("PaymentTransactions");
                });

            modelBuilder.Entity("NetProGroup.Trust.Domain.Submissions.Submission", b =>
                {
                    b.Navigation("Attributes");
                });
#pragma warning restore 612, 618
        }
    }
}
