﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
#pragma warning disable CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
    public partial class synctables : Migration
#pragma warning restore CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SyncBODirector");

            migrationBuilder.CreateTable(
                name: "SyncBenificialOwner",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    EntityUniqueNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerUniqueNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClientCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FileType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RelationType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerFormerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerFromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerToDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BOServiceAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerRegisteredAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerIncorpDateOrDOB = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerIncorpPlaceOrBirthPlace = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerNationality = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerRegisteredCountry = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorporateRegistrationNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerTIN = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NameOfRegulator = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockExchangeName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockExchangeCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerRegulationCountry = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityOwnerIncorpNr = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncBenificialOwner", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SyncDirector",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    EntityUniqueNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirUniqueNr = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClientCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RelationType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirFormerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirFileType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirFromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirToDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirServiceAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ResidentialOrRegisteredAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirIncorpDateOrDOB = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirIncorpPlaceOrBirthPlace = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirNationality = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirCountry = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorporateRegistrationNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirTIN = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirIncorporationNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirectorIsAlternateToCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DirectorIsAlternateToName = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncDirector", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SyncBenificialOwner");

            migrationBuilder.DropTable(
                name: "SyncDirector");

            migrationBuilder.CreateTable(
                name: "SyncBODirector",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    BoDirIncorporationNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CompanyNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorporateRegistrationNo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Country = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateOfBirthOrIncorp = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntityName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FileType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FormerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    JurisdictionOfRegulationOrSovereignState = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MasterClientCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NameOfRegulator = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Nationality = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OfficerType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PlaceOfBirthOrIncorp = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductionOffice = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RelationType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ResidentialOrRegisteredAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ServiceAddress = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StockExchange = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TIN = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ToDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UniqueRelationId = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncBODirector", x => x.Id);
                });
        }
    }
}
