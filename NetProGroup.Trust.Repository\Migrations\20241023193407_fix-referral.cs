﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
#pragma warning disable CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
    public partial class fixreferral : Migration
#pragma warning restore CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "RefrerralOffice",
                table: "SyncCompany",
                newName: "ReferralOffice");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ReferralOffice",
                table: "SyncCompany",
                newName: "RefrerralOffice");
        }
    }
}
