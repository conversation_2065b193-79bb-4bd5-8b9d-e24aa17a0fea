﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
#pragma warning disable CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
    public partial class legalentityoptionaluniqueid : Migration
#pragma warning restore CS8981 // The type name only contains lower-cased ascii characters. Such names may become reserved for the language.
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropColumn(
            //    name: "ConfirmedBy",
            //    table: "Invoices");

            //migrationBuilder.DropColumn(
            //    name: "PaidAt",
            //    table: "Invoices");

            //migrationBuilder.DropColumn(
            //    name: "Reference",
            //    table: "Invoices");

            //migrationBuilder.DropColumn(
            //    name: "Type",
            //    table: "Invoices");

            migrationBuilder.AlterColumn<string>(
                name: "ExternalUniqueId",
                table: "LegalEntityHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "ExternalUniqueId",
                table: "LegalEntities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ExternalUniqueId",
                table: "LegalEntityHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ExternalUniqueId",
                table: "LegalEntities",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            //migrationBuilder.AddColumn<string>(
            //    name: "ConfirmedBy",
            //    table: "Invoices",
            //    type: "nvarchar(max)",
            //    nullable: true);

            //migrationBuilder.AddColumn<DateTime>(
            //    name: "PaidAt",
            //    table: "Invoices",
            //    type: "datetime2",
            //    nullable: true);

            //migrationBuilder.AddColumn<string>(
            //    name: "Reference",
            //    table: "Invoices",
            //    type: "nvarchar(max)",
            //    nullable: true);

            //migrationBuilder.AddColumn<int>(
            //    name: "Type",
            //    table: "Invoices",
            //    type: "int",
            //    nullable: false,
            //    defaultValue: 0);
        }
    }
}
