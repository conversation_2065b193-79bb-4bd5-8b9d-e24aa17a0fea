﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class moduleapproval : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ApprovedByUserId",
                table: "LegalEntityModules",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsApproved",
                table: "LegalEntityModules",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_LegalEntityModules_ApprovedByUserId",
                table: "LegalEntityModules",
                column: "ApprovedByUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_LegalEntityModule_ApplicationUser",
                table: "LegalEntityModules",
                column: "ApprovedByUserId",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LegalEntityModule_ApplicationUser",
                table: "LegalEntityModules");

            migrationBuilder.DropIndex(
                name: "IX_LegalEntityModules_ApprovedByUserId",
                table: "LegalEntityModules");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserId",
                table: "LegalEntityModules");

            migrationBuilder.DropColumn(
                name: "IsApproved",
                table: "LegalEntityModules");
        }
    }
}
