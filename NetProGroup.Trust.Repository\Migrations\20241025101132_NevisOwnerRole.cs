﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class NevisOwnerRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "NetPro",
                table: "Roles",
                columns: new[] { "Id", "ConcurrencyStamp", "DisplayName", "Name", "NormalizedName", "ObjectId" },
                values: new object[] { new Guid("a00d4e6c-dcd6-4fca-b080-666eeb9ae6ce"), "A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE", "Nevis.Owner", "Nevis.Owner", "NEVIS.OWNER", null });

            migrationBuilder.InsertData(
                schema: "NetPro",
                table: "UserRoles",
                columns: new[] { "RoleId", "UserId" },
                values: new object[] { new Guid("a00d4e6c-dcd6-4fca-b080-666eeb9ae6ce"), new Guid("6f886506-48cb-44b5-ad12-a2382e292795") });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "NetPro",
                table: "UserRoles",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { new Guid("a00d4e6c-dcd6-4fca-b080-666eeb9ae6ce"), new Guid("6f886506-48cb-44b5-ad12-a2382e292795") });

            migrationBuilder.DeleteData(
                schema: "NetPro",
                table: "Roles",
                keyColumn: "Id",
                keyValue: new Guid("a00d4e6c-dcd6-4fca-b080-666eeb9ae6ce"));
        }
    }
}
