﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SystemSuperAdminRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "NetPro",
                table: "Roles",
                columns: new[] { "Id", "ConcurrencyStamp", "DisplayName", "Name", "NormalizedName", "ObjectId" },
                values: new object[] { new Guid("daaaf3b2-e632-4ec8-8418-dc012239fd73"), "DAAAF3B2-E632-4EC8-8418-DC012239FD73", "SuperAdmin", "SuperAdmin", "SUPERADMIN", null });

            migrationBuilder.InsertData(
                schema: "NetPro",
                table: "UserRoles",
                columns: new[] { "RoleId", "UserId" },
                values: new object[] { new Guid("daaaf3b2-e632-4ec8-8418-dc012239fd73"), new Guid("6f886506-48cb-44b5-ad12-a2382e292795") });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "NetPro",
                table: "UserRoles",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { new Guid("daaaf3b2-e632-4ec8-8418-dc012239fd73"), new Guid("6f886506-48cb-44b5-ad12-a2382e292795") });

            migrationBuilder.DeleteData(
                schema: "NetPro",
                table: "Roles",
                keyColumn: "Id",
                keyValue: new Guid("daaaf3b2-e632-4ec8-8418-dc012239fd73"));
        }
    }
}
