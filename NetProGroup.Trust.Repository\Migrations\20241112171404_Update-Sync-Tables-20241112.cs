﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSyncTables20241112 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ResidentialOrRegisteredAddress",
                table: "SyncDirector",
                newName: "UniqueRelationID");

            migrationBuilder.RenameColumn(
                name: "DirectorIsAlternateToName",
                table: "SyncDirector",
                newName: "EntityLegacyId");

            migrationBuilder.RenameColumn(
                name: "DirectorIsAlternateToCode",
                table: "SyncDirector",
                newName: "DirStatus");

            migrationBuilder.RenameColumn(
                name: "DirIncorporationNumber",
                table: "SyncDirector",
                newName: "DirRegisteredCountry");

            migrationBuilder.RenameColumn(
                name: "DirCountry",
                table: "SyncDirector",
                newName: "DirRegisteredAddress");

            migrationBuilder.RenameColumn(
                name: "CorporateRegistrationNo",
                table: "SyncDirector",
                newName: "DirProductionOffice");

            migrationBuilder.RenameColumn(
                name: "CompanyNumber",
                table: "SyncDirector",
                newName: "DirOfficerType");

            migrationBuilder.RenameColumn(
                name: "LegacyCode",
                table: "SyncCompany",
                newName: "RiskGroupCode");

            migrationBuilder.RenameColumn(
                name: "StockExchangeName",
                table: "SyncBenificialOwner",
                newName: "UniqueRelationID");

            migrationBuilder.RenameColumn(
                name: "StockExchangeCode",
                table: "SyncBenificialOwner",
                newName: "EntityLegacyId");

            migrationBuilder.RenameColumn(
                name: "NameOfRegulator",
                table: "SyncBenificialOwner",
                newName: "ClientUniqueNr");

            migrationBuilder.RenameColumn(
                name: "FileType",
                table: "SyncBenificialOwner",
                newName: "ClientName");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerUniqueNr",
                table: "SyncBenificialOwner",
                newName: "BOUniqueNr");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerType",
                table: "SyncBenificialOwner",
                newName: "BOToDate");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerToDate",
                table: "SyncBenificialOwner",
                newName: "BOTIN");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerTIN",
                table: "SyncBenificialOwner",
                newName: "BOStockExchange");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerRegulationCountry",
                table: "SyncBenificialOwner",
                newName: "BOStockCode");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerRegisteredCountry",
                table: "SyncBenificialOwner",
                newName: "BORegulationCountry");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerRegisteredAddress",
                table: "SyncBenificialOwner",
                newName: "BORegisteredCountry");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerNationality",
                table: "SyncBenificialOwner",
                newName: "BORegisteredAddress");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerName",
                table: "SyncBenificialOwner",
                newName: "BOProductionOffice");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerIncorpPlaceOrBirthPlace",
                table: "SyncBenificialOwner",
                newName: "BOOwnerType");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerIncorpNr",
                table: "SyncBenificialOwner",
                newName: "BONationality");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerIncorpDateOrDOB",
                table: "SyncBenificialOwner",
                newName: "BONameOfRegulator");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerFromDate",
                table: "SyncBenificialOwner",
                newName: "BOName");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerFormerName",
                table: "SyncBenificialOwner",
                newName: "BOIncorpPlaceOrBirthPlace");

            migrationBuilder.RenameColumn(
                name: "EntityOwnerCode",
                table: "SyncBenificialOwner",
                newName: "BOIncorpNrOrPassportNr");

            migrationBuilder.RenameColumn(
                name: "CompanyNumber",
                table: "SyncBenificialOwner",
                newName: "BOIncorpDateOrDOB");

            migrationBuilder.AddColumn<string>(
                name: "UserCode",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserName",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserPermission",
                table: "SyncMasterClient",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AlternateToDirCode",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AlternateToDirName",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientName",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientUniqueNr",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirIncorpNrOrPassportNr",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AdministratorCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ClientName",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityLegacyID",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntityStatusCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntitySubStatusCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Jurisdiction",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ManagerCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReferralOfficeCode",
                table: "SyncCompany",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOCode",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOFileType",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOFormerName",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOFromDate",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserCode",
                table: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "UserName",
                table: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "UserPermission",
                table: "SyncMasterClient");

            migrationBuilder.DropColumn(
                name: "AlternateToDirCode",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "AlternateToDirName",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "ClientName",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "ClientUniqueNr",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "DirIncorpNrOrPassportNr",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "AdministratorCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "ClientName",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityLegacyID",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntityStatusCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "EntitySubStatusCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "Jurisdiction",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "ManagerCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "ReferralOfficeCode",
                table: "SyncCompany");

            migrationBuilder.DropColumn(
                name: "BOCode",
                table: "SyncBenificialOwner");

            migrationBuilder.DropColumn(
                name: "BOFileType",
                table: "SyncBenificialOwner");

            migrationBuilder.DropColumn(
                name: "BOFormerName",
                table: "SyncBenificialOwner");

            migrationBuilder.DropColumn(
                name: "BOFromDate",
                table: "SyncBenificialOwner");

            migrationBuilder.RenameColumn(
                name: "UniqueRelationID",
                table: "SyncDirector",
                newName: "ResidentialOrRegisteredAddress");

            migrationBuilder.RenameColumn(
                name: "EntityLegacyId",
                table: "SyncDirector",
                newName: "DirectorIsAlternateToName");

            migrationBuilder.RenameColumn(
                name: "DirStatus",
                table: "SyncDirector",
                newName: "DirectorIsAlternateToCode");

            migrationBuilder.RenameColumn(
                name: "DirRegisteredCountry",
                table: "SyncDirector",
                newName: "DirIncorporationNumber");

            migrationBuilder.RenameColumn(
                name: "DirRegisteredAddress",
                table: "SyncDirector",
                newName: "DirCountry");

            migrationBuilder.RenameColumn(
                name: "DirProductionOffice",
                table: "SyncDirector",
                newName: "CorporateRegistrationNo");

            migrationBuilder.RenameColumn(
                name: "DirOfficerType",
                table: "SyncDirector",
                newName: "CompanyNumber");

            migrationBuilder.RenameColumn(
                name: "RiskGroupCode",
                table: "SyncCompany",
                newName: "LegacyCode");

            migrationBuilder.RenameColumn(
                name: "UniqueRelationID",
                table: "SyncBenificialOwner",
                newName: "StockExchangeName");

            migrationBuilder.RenameColumn(
                name: "EntityLegacyId",
                table: "SyncBenificialOwner",
                newName: "StockExchangeCode");

            migrationBuilder.RenameColumn(
                name: "ClientUniqueNr",
                table: "SyncBenificialOwner",
                newName: "NameOfRegulator");

            migrationBuilder.RenameColumn(
                name: "ClientName",
                table: "SyncBenificialOwner",
                newName: "FileType");

            migrationBuilder.RenameColumn(
                name: "BOUniqueNr",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerUniqueNr");

            migrationBuilder.RenameColumn(
                name: "BOToDate",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerType");

            migrationBuilder.RenameColumn(
                name: "BOTIN",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerToDate");

            migrationBuilder.RenameColumn(
                name: "BOStockExchange",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerTIN");

            migrationBuilder.RenameColumn(
                name: "BOStockCode",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerRegulationCountry");

            migrationBuilder.RenameColumn(
                name: "BORegulationCountry",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerRegisteredCountry");

            migrationBuilder.RenameColumn(
                name: "BORegisteredCountry",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerRegisteredAddress");

            migrationBuilder.RenameColumn(
                name: "BORegisteredAddress",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerNationality");

            migrationBuilder.RenameColumn(
                name: "BOProductionOffice",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerName");

            migrationBuilder.RenameColumn(
                name: "BOOwnerType",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerIncorpPlaceOrBirthPlace");

            migrationBuilder.RenameColumn(
                name: "BONationality",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerIncorpNr");

            migrationBuilder.RenameColumn(
                name: "BONameOfRegulator",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerIncorpDateOrDOB");

            migrationBuilder.RenameColumn(
                name: "BOName",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerFromDate");

            migrationBuilder.RenameColumn(
                name: "BOIncorpPlaceOrBirthPlace",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerFormerName");

            migrationBuilder.RenameColumn(
                name: "BOIncorpNrOrPassportNr",
                table: "SyncBenificialOwner",
                newName: "EntityOwnerCode");

            migrationBuilder.RenameColumn(
                name: "BOIncorpDateOrDOB",
                table: "SyncBenificialOwner",
                newName: "CompanyNumber");
        }
    }
}
