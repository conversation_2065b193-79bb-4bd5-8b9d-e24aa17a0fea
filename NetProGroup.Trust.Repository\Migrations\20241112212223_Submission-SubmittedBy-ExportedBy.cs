﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SubmissionSubmittedByExportedBy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "SubmittedBy",
                table: "Submissions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_ExportedBy",
                table: "Submissions",
                column: "ExportedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_SubmittedBy",
                table: "Submissions",
                column: "SubmittedBy");

            migrationBuilder.AddForeignKey(
                name: "FK_Submission_ApplicationUser_1",
                table: "Submissions",
                column: "SubmittedBy",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Submission_ApplicationUser_2",
                table: "Submissions",
                column: "ExportedBy",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Submission_ApplicationUser_1",
                table: "Submissions");

            migrationBuilder.DropForeignKey(
                name: "FK_Submission_ApplicationUser_2",
                table: "Submissions");

            migrationBuilder.DropIndex(
                name: "IX_Submissions_ExportedBy",
                table: "Submissions");

            migrationBuilder.DropIndex(
                name: "IX_Submissions_SubmittedBy",
                table: "Submissions");

            migrationBuilder.DropColumn(
                name: "SubmittedBy",
                table: "Submissions");
        }
    }
}
