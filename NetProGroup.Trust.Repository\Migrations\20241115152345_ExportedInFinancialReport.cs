﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class ExportedInFinancialReport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ExportedInFinancialReportId",
                table: "Submissions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ArticleNr",
                table: "InvoiceLines",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Reports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "newid()"),
                    ReportName = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
                    DocumentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    ConcurrencyStamp = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Reports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Report_Document",
                        column: x => x.DocumentId,
                        principalSchema: "NetPro",
                        principalTable: "Documents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_ExportedInFinancialReportId",
                table: "Submissions",
                column: "ExportedInFinancialReportId");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_DocumentId",
                table: "Reports",
                column: "DocumentId");

            migrationBuilder.AddForeignKey(
                name: "FK_Submission_Report",
                table: "Submissions",
                column: "ExportedInFinancialReportId",
                principalTable: "Reports",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Submission_Report",
                table: "Submissions");

            migrationBuilder.DropTable(
                name: "Reports");

            migrationBuilder.DropIndex(
                name: "IX_Submissions_ExportedInFinancialReportId",
                table: "Submissions");

            migrationBuilder.DropColumn(
                name: "ExportedInFinancialReportId",
                table: "Submissions");

            migrationBuilder.DropColumn(
                name: "ArticleNr",
                table: "InvoiceLines");
        }
    }
}
