﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class UniqueCodes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_MasterClients_Code",
                table: "MasterClients",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LegalEntities_Code",
                table: "LegalEntities",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Directors_ExternalUniqueId",
                table: "Directors",
                column: "ExternalUniqueId",
                unique: true,
                filter: "[ExternalUniqueId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_BeneficialOwners_ExternalUniqueId",
                table: "BeneficialOwners",
                column: "ExternalUniqueId",
                unique: true,
                filter: "[ExternalUniqueId] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_MasterClients_Code",
                table: "MasterClients");

            migrationBuilder.DropIndex(
                name: "IX_LegalEntities_Code",
                table: "LegalEntities");

            migrationBuilder.DropIndex(
                name: "IX_Directors_ExternalUniqueId",
                table: "Directors");

            migrationBuilder.DropIndex(
                name: "IX_BeneficialOwners_ExternalUniqueId",
                table: "BeneficialOwners");
        }
    }
}
