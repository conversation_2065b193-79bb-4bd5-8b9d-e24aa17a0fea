﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class ReferenceBoDir : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "DirectorId",
                table: "DirectorHistory",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "BeneficialOwnerId",
                table: "BeneficialOwnerHistory",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DirectorHistory_DirectorId",
                table: "DirectorHistory",
                column: "DirectorId");

            migrationBuilder.CreateIndex(
                name: "IX_BeneficialOwnerHistory_BeneficialOwnerId",
                table: "BeneficialOwnerHistory",
                column: "BeneficialOwnerId");

            migrationBuilder.AddForeignKey(
                name: "FK_BeneficialOwner_BeneficialOwnerHistory",
                table: "BeneficialOwnerHistory",
                column: "BeneficialOwnerId",
                principalTable: "BeneficialOwners",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Director_DirectorHistory",
                table: "DirectorHistory",
                column: "DirectorId",
                principalTable: "Directors",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BeneficialOwner_BeneficialOwnerHistory",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_Director_DirectorHistory",
                table: "DirectorHistory");

            migrationBuilder.DropIndex(
                name: "IX_DirectorHistory_DirectorId",
                table: "DirectorHistory");

            migrationBuilder.DropIndex(
                name: "IX_BeneficialOwnerHistory_BeneficialOwnerId",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "DirectorId",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "BeneficialOwnerId",
                table: "BeneficialOwnerHistory");
        }
    }
}
