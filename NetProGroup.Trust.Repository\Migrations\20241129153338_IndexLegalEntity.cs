﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class IndexLegalEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_LegalEntity_EntityType_OnboardingStatus_JurisdictionId",
                table: "LegalEntities",
                columns: new[] { "EntityType", "OnboardingStatus", "JurisdictionId" })
                .Annotation("SqlServer:Include", new[] { "Code", "IncorporationNr", "LegacyCode", "MasterClientCode", "MasterClientId", "Name" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_LegalEntity_EntityType_OnboardingStatus_JurisdictionId",
                table: "LegalEntities");
        }
    }
}
