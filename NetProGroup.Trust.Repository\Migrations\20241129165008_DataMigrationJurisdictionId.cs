﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class DataMigrationJurisdictionId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "JurisdictionId",
                table: "DataMigrations",
                type: "uniqueidentifier",
                maxLength: 50,
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));
            
            migrationBuilder.Sql("EXEC(N'UPDATE DataMigrations SET JurisdictionId = (SELECT Id FROM Jurisdictions WHERE Code = ''Nevis'') WHERE Region = ''TNEV'';')");
            
            migrationBuilder.DropColumn(
                name: "Region",
                table: "DataMigrations");

            migrationBuilder.CreateIndex(
                name: "IX_DataMigrations_JurisdictionId",
                table: "DataMigrations",
                column: "JurisdictionId");

            migrationBuilder.AddForeignKey(
                name: "FK_DataMigrations_Jurisdictions_JurisdictionId",
                table: "DataMigrations",
                column: "JurisdictionId",
                principalTable: "Jurisdictions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataMigrations_Jurisdictions_JurisdictionId",
                table: "DataMigrations");

            migrationBuilder.DropIndex(
                name: "IX_DataMigrations_JurisdictionId",
                table: "DataMigrations");

            migrationBuilder.DropColumn(
                name: "JurisdictionId",
                table: "DataMigrations");

            migrationBuilder.AddColumn<string>(
                name: "Region",
                table: "DataMigrations",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");
        }
    }
}
