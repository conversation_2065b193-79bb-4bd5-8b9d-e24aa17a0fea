﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class BoDirHistoryOnDeleteSetNull : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BeneficialOwner_BeneficialOwnerHistory",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_Director_DirectorHistory",
                table: "DirectorHistory");

            migrationBuilder.AddForeignKey(
                name: "FK_BeneficialOwner_BeneficialOwnerHistory",
                table: "BeneficialOwnerHistory",
                column: "BeneficialOwnerId",
                principalTable: "BeneficialOwners",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Director_DirectorHistory",
                table: "DirectorHistory",
                column: "DirectorId",
                principalTable: "Directors",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BeneficialOwner_BeneficialOwnerHistory",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_Director_DirectorHistory",
                table: "DirectorHistory");

            migrationBuilder.AddForeignKey(
                name: "FK_BeneficialOwner_BeneficialOwnerHistory",
                table: "BeneficialOwnerHistory",
                column: "BeneficialOwnerId",
                principalTable: "BeneficialOwners",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Director_DirectorHistory",
                table: "DirectorHistory",
                column: "DirectorId",
                principalTable: "Directors",
                principalColumn: "Id");
        }
    }
}
