﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SyncExcludedLegalEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LegalEntities_Settings",
                table: "Settings");

            migrationBuilder.CreateTable(
                name: "SyncExcludedLegalEntities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "newid()"),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    LegacyCode = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DeletedByMigrationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "getutcdate()"),
                    ConcurrencyStamp = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncExcludedLegalEntities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SyncExcludedLegalEntity_DataMigration",
                        column: x => x.DeletedByMigrationId,
                        principalTable: "DataMigrations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SyncExcludedLegalEntities_Code",
                table: "SyncExcludedLegalEntities",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_SyncExcludedLegalEntities_Code_DeletedByMigrationId",
                table: "SyncExcludedLegalEntities",
                columns: new[] { "Code", "DeletedByMigrationId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SyncExcludedLegalEntities_DeletedByMigrationId",
                table: "SyncExcludedLegalEntities",
                column: "DeletedByMigrationId");

            migrationBuilder.AddForeignKey(
                name: "FK_LegalEntities_Settings",
                table: "Settings",
                column: "LegalEntityId",
                principalTable: "LegalEntities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LegalEntities_Settings",
                table: "Settings");

            migrationBuilder.DropTable(
                name: "SyncExcludedLegalEntities");

            migrationBuilder.AddForeignKey(
                name: "FK_LegalEntities_Settings",
                table: "Settings",
                column: "LegalEntityId",
                principalTable: "LegalEntities",
                principalColumn: "Id");
        }
    }
}
