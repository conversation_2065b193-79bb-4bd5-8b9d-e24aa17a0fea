﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class NewUserFKrelationshipInboxOwnerInboxReadStatus : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OwnerId",
                table: "InboxOwners",
                newName: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_InboxReadStatuses_UserId",
                table: "InboxReadStatuses",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_InboxOwners_UserId",
                table: "InboxOwners",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_InboxOwner_Owner",
                table: "InboxOwners",
                column: "UserId",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_InboxReadStatus_User",
                table: "InboxReadStatuses",
                column: "UserId",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InboxOwner_Owner",
                table: "InboxOwners");

            migrationBuilder.DropForeignKey(
                name: "FK_InboxReadStatus_User",
                table: "InboxReadStatuses");

            migrationBuilder.DropIndex(
                name: "IX_InboxReadStatuses_UserId",
                table: "InboxReadStatuses");

            migrationBuilder.DropIndex(
                name: "IX_InboxOwners_UserId",
                table: "InboxOwners");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "InboxOwners",
                newName: "OwnerId");
        }
    }
}
