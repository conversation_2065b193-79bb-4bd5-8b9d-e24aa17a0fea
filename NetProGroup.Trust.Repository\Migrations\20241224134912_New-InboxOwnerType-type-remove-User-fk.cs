﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class NewInboxOwnerTypetyperemoveUserfk : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_InboxOwner_Owner",
                table: "InboxOwners");

            migrationBuilder.DropIndex(
                name: "IX_InboxOwners_UserId",
                table: "InboxOwners");

            migrationBuilder.DropColumn(
                name: "OwnerType",
                table: "InboxOwners");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "InboxOwners",
                newName: "OwnerId");

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "InboxOwners",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Type",
                table: "InboxOwners");

            migrationBuilder.RenameColumn(
                name: "OwnerId",
                table: "InboxOwners",
                newName: "UserId");

            migrationBuilder.AddColumn<string>(
                name: "OwnerType",
                table: "InboxOwners",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_InboxOwners_UserId",
                table: "InboxOwners",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_InboxOwner_Owner",
                table: "InboxOwners",
                column: "UserId",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
