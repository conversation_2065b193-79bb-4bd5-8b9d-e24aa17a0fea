﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class SubmissionCreatedBy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CreatedB<PERSON>",
                table: "Submissions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Submissions_CreatedBy",
                table: "Submissions",
                column: "CreatedBy");

            migrationBuilder.AddForeignKey(
                name: "FK_Submission_ApplicationUser_3",
                table: "Submissions",
                column: "CreatedBy",
                principalSchema: "NetPro",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Submission_ApplicationUser_3",
                table: "Submissions");

            migrationBuilder.DropIndex(
                name: "IX_Submissions_CreatedBy",
                table: "Submissions");

            migrationBuilder.DropColumn(
                name: "<PERSON>B<PERSON>",
                table: "Submissions");
        }
    }
}
