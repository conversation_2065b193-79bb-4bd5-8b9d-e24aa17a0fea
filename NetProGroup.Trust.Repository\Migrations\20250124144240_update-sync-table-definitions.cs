﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSyncTableDefinitions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DirIncorpCountryOrBirthCountry",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirIncorpCountryOrBirthCountryCode",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOIncorpCountryOrBirthCountry",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOIncorpCountryOrBirthCountryCode",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BOOwnerTypeCode",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DirIncorpCountryOrBirthCountry",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "DirIncorpCountryOrBirthCountryCode",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "BOIncorpCountryOrBirthCountry",
                table: "SyncBenificialOwner");

            migrationBuilder.DropColumn(
                name: "BOIncorpCountryOrBirthCountryCode",
                table: "SyncBenificialOwner");

            migrationBuilder.DropColumn(
                name: "BOOwnerTypeCode",
                table: "SyncBenificialOwner");
        }
    }
}
