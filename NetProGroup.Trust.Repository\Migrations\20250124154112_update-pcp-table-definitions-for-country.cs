﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePCPTableDefinitionsForCountry : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirth",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirthCode",
                table: "Directors",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationCountry",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationCountryCode",
                table: "Directors",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirth",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirthCode",
                table: "DirectorHistory",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationCountry",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IncorporationCountryCode",
                table: "DirectorHistory",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirth",
                table: "BeneficialOwners",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirthCode",
                table: "BeneficialOwners",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirth",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfBirthCode",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryOfBirth",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "CountryOfBirthCode",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "IncorporationCountry",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "IncorporationCountryCode",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "CountryOfBirth",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "CountryOfBirthCode",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "IncorporationCountry",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "IncorporationCountryCode",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "CountryOfBirth",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "CountryOfBirthCode",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "CountryOfBirth",
                table: "BeneficialOwnerHistory");

            migrationBuilder.DropColumn(
                name: "CountryOfBirthCode",
                table: "BeneficialOwnerHistory");
        }
    }
}
