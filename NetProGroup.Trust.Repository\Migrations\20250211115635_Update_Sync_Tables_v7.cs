﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class Update_Sync_Tables_v7 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CorporateRegistrationNo",
                table: "SyncBenificialOwner");

            migrationBuilder.AddColumn<string>(
                name: "DirectorCapacity",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorID",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenseeEntityCode",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenseeEntityName",
                table: "SyncDirector",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorCapacity",
                table: "Directors",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorID",
                table: "Directors",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenseeEntityCode",
                table: "Directors",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenseeEntityName",
                table: "Directors",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DirectorCapacity",
                table: "DirectorHistory",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenseeEntityCode",
                table: "DirectorHistory",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenseeEntityName",
                table: "DirectorHistory",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VPDirectorID",
                table: "DirectorHistory",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DirectorCapacity",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "DirectorID",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "LicenseeEntityCode",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "LicenseeEntityName",
                table: "SyncDirector");

            migrationBuilder.DropColumn(
                name: "DirectorCapacity",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "DirectorID",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "LicenseeEntityCode",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "LicenseeEntityName",
                table: "Directors");

            migrationBuilder.DropColumn(
                name: "DirectorCapacity",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "LicenseeEntityCode",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "LicenseeEntityName",
                table: "DirectorHistory");

            migrationBuilder.DropColumn(
                name: "VPDirectorID",
                table: "DirectorHistory");

            migrationBuilder.AddColumn<string>(
                name: "CorporateRegistrationNo",
                table: "SyncBenificialOwner",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
