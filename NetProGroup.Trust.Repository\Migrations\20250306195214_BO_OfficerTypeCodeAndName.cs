﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class BO_OfficerTypeCodeAndName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OfficerType",
                table: "BeneficialOwners",
                newName: "OfficerTypeCode");

            migrationBuilder.RenameColumn(
                name: "OfficerType",
                table: "BeneficialOwnerHistory",
                newName: "OfficerTypeCode");

            migrationBuilder.AddColumn<string>(
                name: "OfficerTypeName",
                table: "BeneficialOwners",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OfficerTypeName",
                table: "BeneficialOwnerHistory",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OfficerTypeName",
                table: "BeneficialOwners");

            migrationBuilder.DropColumn(
                name: "OfficerTypeName",
                table: "BeneficialOwnerHistory");

            migrationBuilder.RenameColumn(
                name: "OfficerTypeCode",
                table: "BeneficialOwners",
                newName: "OfficerType");

            migrationBuilder.RenameColumn(
                name: "OfficerTypeCode",
                table: "BeneficialOwnerHistory",
                newName: "OfficerType");
        }
    }
}
