﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class Director_RenameOfficerTypeName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OfficerType",
                table: "Directors",
                newName: "OfficerTypeName");

            migrationBuilder.RenameColumn(
                name: "OfficerType",
                table: "DirectorHistory",
                newName: "OfficerTypeName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "OfficerTypeName",
                table: "Directors",
                newName: "OfficerType");

            migrationBuilder.RenameColumn(
                name: "OfficerTypeName",
                table: "DirectorHistory",
                newName: "OfficerType");
        }
    }
}
