﻿// <copyright file="JurisdictionModuleModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Model configuration for a JurisdictionModule.
    /// </summary>
    public class JurisdictionModuleModelConfiguration : IEntityTypeConfiguration<JurisdictionModule>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<JurisdictionModule> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "JurisdictionModules", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<JurisdictionModule>(builder);

            builder.Property(x => x.IsEnabled).HasDefaultValue(true);

            builder.HasOne(x => x.Module).WithMany(x => x.JurisdictionModules).HasForeignKey(d => d.ModuleId).OnDelete(DeleteBehavior.NoAction).HasConstraintName("FK_JurisdictionModule_Module");

            builder.HasOne(d => d.Jurisdiction).WithMany(x=>x.JurisdictionModules).HasForeignKey(d => d.JurisdictionId).HasConstraintName("FK_JurisdictionModule_Jurisdiction");
        }
    }
}
