﻿// <copyright file="JurisdictionModulesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Repository for JurisdictionModules.
    /// </summary>
    public class JurisdictionModulesRepository : RepositoryBase<TrustDbContext, JurisdictionModule, Guid>, IJurisdictionModulesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionModulesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public JurisdictionModulesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {            
        }

    }
}
