﻿// <copyright file="LegalEntityModuleModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Model configuration for a LegalEntityModule.
    /// </summary>
    public class LegalEntityModuleModelConfiguration : IEntityTypeConfiguration<LegalEntityModule>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<LegalEntityModule> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "LegalEntityModules", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<LegalEntityModule>(builder);

            builder.Property(x => x.IsEnabled).HasDefaultValue(true);

            builder.HasOne(d => d.Module).WithMany(x => x.LegalEntityModules).HasForeignKey(d => d.ModuleId).OnDelete(DeleteBehavior.NoAction).HasConstraintName("FK_LegalEntityModule_Module");

            builder.HasOne(d => d.LegalEntity)
                   .WithMany(x => x.LegalEntityModules)
                   .HasForeignKey(d => d.LegalEntityId)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_LegalEntityModule_LegalEntity");

            builder.HasOne(x => x.ApprovedByUser).WithMany()
                .HasForeignKey(x => x.ApprovedByUserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_LegalEntityModule_ApplicationUser");
        }
    }
}

