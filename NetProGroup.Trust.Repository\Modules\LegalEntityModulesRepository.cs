﻿// <copyright file="LegalEntityModulesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Repository for LegalEntityModules.
    /// </summary>
    public class LegalEntityModulesRepository : RepositoryBase<TrustDbContext, LegalEntityModule, Guid>, ILegalEntityModulesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntityModulesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public LegalEntityModulesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }
    }
}
