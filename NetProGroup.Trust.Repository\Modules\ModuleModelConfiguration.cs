﻿// <copyright file="ModuleModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Model configuration for a Module.
    /// </summary>
    public class ModuleModelConfiguration : IEntityTypeConfiguration<Module>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<Module> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "Modules", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<Module>(builder);

            builder.Property(x => x.Name).IsRequired().HasMaxLength(ModuleConsts.NameMaxLength);
            builder.Property(x => x.Key).IsRequired().HasMaxLength(ModuleConsts.CodeMaxLength);

            //builder.HasMany(x => x.Jurisdictions).WithOne(jm => jm.Module)
            //    .HasForeignKey(jm => jm.JurisdictionId)
            //    .HasConstraintName("FK_JurisdictionModule_Module");

            //builder.HasMany(x => x.LegalEntities).WithOne(le => le.Module)
            //    .HasForeignKey(le => le.LegalEntityId)
            //    .HasConstraintName("FK_LegalEntityModule_Module");
        }
    }
}
