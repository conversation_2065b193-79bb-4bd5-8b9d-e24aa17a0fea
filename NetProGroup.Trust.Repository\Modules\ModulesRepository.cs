﻿// <copyright file="ModulesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Repository for Modules.
    /// </summary>
    public class ModulesRepository : RepositoryBase<TrustDbContext, Module, Guid>, IModulesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ModulesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public ModulesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }
    }
}
