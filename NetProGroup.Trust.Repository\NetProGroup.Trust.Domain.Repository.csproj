﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="stylecop.json" />
  </ItemGroup>
  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>
  <ItemGroup>
	  <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.22" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.Application.Contracts\NetProGroup.Trust.Application.Contracts.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Domain\NetProGroup.Trust.Domain.csproj" />
  </ItemGroup>
</Project>