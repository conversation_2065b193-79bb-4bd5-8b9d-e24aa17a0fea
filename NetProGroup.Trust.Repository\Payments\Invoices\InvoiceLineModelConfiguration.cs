// <copyright file="InvoiceLineModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Payments.Invoices;

namespace NetProGroup.Trust.Domain.Repository.Payments.Invoices
{
    /// <summary>
    /// Model configuration for the <see cref="InvoiceLine"/> entity.
    /// </summary>
    public class InvoiceLineModelConfiguration : IEntityTypeConfiguration<InvoiceLine>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="InvoiceLine"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<InvoiceLine> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "InvoiceLines", TrustDbContext.DbSchema);

            // Primary key
            builder.HasKey(il => il.Id);
            builder.Property(il => il.Id).ValueGeneratedOnAdd();

            // Default settings for stamped entity
            Helpers.ModelBuilder.SetStampedEntityDefaults<InvoiceLine>(builder);

            // Properties configuration
            builder.Property(il => il.InvoiceId).IsRequired();
            builder.Property(il => il.CurrencyId).IsRequired();
            builder.Property(il => il.Description).HasMaxLength(255);
            builder.Property(il => il.Sequence).IsRequired();
            builder.Property(il => il.Amount).IsRequired().HasColumnType("decimal(18,2)");
            builder.Property(il => il.ArticleNr).IsRequired(false).HasMaxLength(50);

            // Relationships
            builder.HasOne(il => il.Invoice)
                .WithMany(i => i.InvoiceLines)
                .HasForeignKey(il => il.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_InvoiceLine_Invoice");

            builder.HasOne(il => il.Currency)
                .WithMany() // Adjust if Currency has a collection of InvoiceLines
                .HasForeignKey(il => il.CurrencyId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_InvoiceLine_Currency");
        }
    }
}
