// <copyright file="InvoiceLinesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Payments.Invoices;

namespace NetProGroup.Trust.Domain.Repository.Payments.Invoices
{
    /// <summary>
    /// Repository for InvoiceLines.
    /// </summary>
    public class InvoiceLinesRepository : RepositoryBase<TrustDbContext, InvoiceLine, Guid>, IInvoiceLineRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceLinesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public InvoiceLinesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IInvoiceLineRepository.DbContext => base.DbContext;
    }
}
