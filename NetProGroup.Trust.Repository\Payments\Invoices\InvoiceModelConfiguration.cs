// <copyright file="InvoiceModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Payments.Invoices
{
    /// <summary>
    /// Model configuration for the <see cref="Invoice"/> entity.
    /// </summary>
    public class InvoiceModelConfiguration : IEntityTypeConfiguration<Invoice>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="Invoice"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<Invoice> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "Invoices", TrustDbContext.DbSchema);

            // Primary key
            builder.HasKey(i => i.Id);
            builder.Property(i => i.Id).ValueGeneratedOnAdd();

            // Default settings for stamped entity
            Helpers.ModelBuilder.SetStampedEntityDefaults<Invoice>(builder);

            // Properties configuration
            builder.Property(i => i.LegalEntityId).IsRequired();
            builder.Property(i => i.CurrencyId).IsRequired();
            builder.Property(i => i.Amount).IsRequired().HasColumnType("decimal(18,2)");
            builder.Property(i => i.InvoiceNr).IsRequired().HasMaxLength(50);
            builder.Property(i => i.Date).IsRequired();
            builder.Property(i => i.Layout).IsRequired().HasMaxLength(LayoutConsts.LayoutNameMaxLength);

            // Relationships
            builder.HasOne(i => i.LegalEntity)
                .WithMany()
                .HasForeignKey(i => i.LegalEntityId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Invoice_LegalEntity");

            builder.HasOne(i => i.Currency)
                .WithMany()
                .HasForeignKey(i => i.CurrencyId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Invoice_Currency");

            builder.HasMany(i => i.InvoiceLines)
                .WithOne(il => il.Invoice)
                .HasForeignKey(il => il.InvoiceId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_InvoiceLine_Invoice");
        }
    }
}
