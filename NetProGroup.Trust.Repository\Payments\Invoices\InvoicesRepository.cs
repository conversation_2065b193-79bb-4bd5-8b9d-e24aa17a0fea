// <copyright file="InvoicesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Payments.Invoices;

namespace NetProGroup.Trust.Domain.Repository.Payments.Invoices
{
    /// <summary>
    /// Repository for Invoices.
    /// </summary>
    public class InvoiceRepository : RepositoryBase<TrustDbContext, Invoice, Guid>, IInvoiceRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public InvoiceRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IInvoiceRepository.DbContext => base.DbContext;

        /// <summary>
        /// Gets the Jurisdiction by id.
        /// </summary>
        /// <param name="invoiceId">The invoice id.</param>
        /// <returns>The jurisdiction.</returns>
        /// <exception cref="NotImplementedException">NUllReferenceException.</exception>
        public async Task<Jurisdiction> GetJurisdictionByInvoiceId(Guid invoiceId)
        {
            var invoice = await GetQueryable().Where(i => i.Id == invoiceId)
                 .Include(i => i.LegalEntity).ThenInclude(l => l.Jurisdiction).SingleOrDefaultAsync();
            return invoice.LegalEntity.Jurisdiction;
        }

        /// <summary>
        /// Gets the legal entities by invoiceids.
        /// </summary>
        /// <param name="invoiceIds">The invoice ids.</param>
        /// <returns>The LegalEntities.</returns>
        public async Task<List<LegalEntity>> GetLegalEntitiesByInvoiceIds(List<Guid> invoiceIds)
        {
            var invoices = await GetQueryable().Where(i => invoiceIds.Contains(i.Id)).Include(i => i.LegalEntity).ToListAsync();
            return invoices.Select(i => i.LegalEntity).Distinct().ToList();
        }

        /// <summary>
        /// Gets the MasterClient by the id of the invoice.
        /// </summary>
        /// <param name="invoiceId">The invoice id.</param>
        /// <returns>The MasterClient.</returns>
        /// <exception cref="NotImplementedException">NUllReferenceException.</exception>
        public async Task<MasterClient> GetMasterClientByInvoiceId(Guid invoiceId)
        {
            var invoice = await GetQueryable().Where(i => i.Id == invoiceId)
                 .Include(i => i.LegalEntity).ThenInclude(l => l.MasterClient).SingleOrDefaultAsync();
            return invoice.LegalEntity.MasterClient;
        }

        /// <summary>
        /// Gets the payment provider settings for a company.
        /// </summary>
        /// <param name="invoiceId">The ID of the Invoice.</param>
        /// <returns>The payment provider settings for the company, or null if not found.</returns>
        /// <exception cref="ArgumentNullException">Thrown if <paramref name="invoiceId"/> is null.</exception>
        public Domain.Payments.Provider.PaymentProvider GetPaymentProviderSettingsForCompany(Guid invoiceId)
        {
            ArgumentNullException.ThrowIfNull(invoiceId);

            var invoice = GetById(invoiceId, q =>
                q.Include(le => le.LegalEntity)
                    .ThenInclude(le => le.Jurisdiction)
                    .ThenInclude(j => j.PaymentProviders));

            return invoice.LegalEntity.Jurisdiction?.PaymentProviders?.FirstOrDefault();
        }

        /// <summary>
        /// Checks if the invoices are in the same currency.
        /// </summary>
        /// <param name="invoiceIds">The invoice IDs.</param>
        /// <param name="currencyId">The currency ID.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> IsSameCurrency(IEnumerable<Guid> invoiceIds, Guid currencyId)
        {
            return await GetQueryable()
                .Where(i => invoiceIds.Contains(i.Id))
                .AllAsync(i => i.CurrencyId == currencyId);
        }

        /// <summary>
        /// Checks if the invoices are in the same jurisdiction.
        /// </summary>
        /// <param name="invoiceIds">The invoice id's to check.</param>
        /// <returns>Returns True if in the same jurisdiction.</returns>
        public async Task<bool> IsSameJurisdiction(IEnumerable<Guid> invoiceIds)
        {
            var distinctJurisdictions = await GetQueryable()
                .Include(i => i.LegalEntity)
                .Where(i => invoiceIds.Contains(i.Id))
                .Select(i => i.LegalEntity.JurisdictionId)
                .Distinct()
                .Take(2)
                .ToListAsync();

            return distinctJurisdictions.Count == 1;
        }
    }
}
