// <copyright file="PaymentInvoiceModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Framework.EF;

namespace NetProGroup.Trust.Domain.Repository.Payments
{
    /// <summary>
    /// Model configuration for the <see cref="PaymentInvoice"/> entity.
    /// </summary>
    public class PaymentInvoiceModelConfiguration : IEntityTypeConfiguration<PaymentInvoice>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="PaymentInvoice"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<PaymentInvoice> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "PaymentInvoices", TrustDbContext.DbSchema);

            // Primary key
            builder.HasKey(pi => pi.Id);
            builder.Property(pi => pi.Id).ValueGeneratedOnAdd();

            // Default settings for stamped entity
            Helpers.ModelBuilder.SetStampedEntityDefaults<PaymentInvoice>(builder);

            // Properties configuration
            builder.Property(pi => pi.PaymentId).IsRequired();
            builder.Property(pi => pi.InvoiceId).IsRequired();

            // Relationships
            builder.HasOne(pi => pi.Payment)
                .WithMany(p => p.PaymentInvoices)
                .HasForeignKey(pi => pi.PaymentId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_PaymentInvoice_Payment");

            builder.HasOne(pi => pi.Invoice)
                .WithMany(i => i.PaymentInvoices)
                .HasForeignKey(pi => pi.InvoiceId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_PaymentInvoice_Invoice");
        }
    }
}
