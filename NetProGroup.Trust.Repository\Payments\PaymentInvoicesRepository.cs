// <copyright file="PaymentInvoicesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Domain.Repository.Payments
{
    /// <summary>
    /// Repository for PaymentInvoices.
    /// </summary>
    public class PaymentInvoicesRepository : RepositoryBase<TrustDbContext, PaymentInvoice, Guid>, IPaymentInvoiceRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentInvoicesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public PaymentInvoicesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IPaymentInvoiceRepository.DbContext => base.DbContext;
    }
}