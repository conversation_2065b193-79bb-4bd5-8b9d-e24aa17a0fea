// <copyright file="PaymentModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Framework.EF;

namespace NetProGroup.Trust.Domain.Repository.Payments
{
    /// <summary>
    /// Model configuration for the <see cref="Payment"/> entity.
    /// </summary>
    public class PaymentModelConfiguration : IEntityTypeConfiguration<Payment>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="Payment"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<Payment> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "Payments", TrustDbContext.DbSchema);

            // Primary key
            builder.HasKey(p => p.Id);
            builder.Property(p => p.Id).ValueGeneratedOnAdd();

            // Default settings for stamped entity
            Helpers.ModelBuilder.SetStampedEntityDefaults<Payment>(builder);

            // Properties configuration
            builder.Property(p => p.LegalEntityId).IsRequired(false); // LegalEntityId can be null
            builder.Property(p => p.CurrencyId).IsRequired().HasMaxLength(3); // Assuming ISO 4217 code format
            builder.Property(p => p.Amount).IsRequired().HasColumnType("decimal(18,2)");
            builder.Property(p => p.ExpirationDate).IsRequired()
                .HasDefaultValue(new DateTime(2000, 1, 1));

            // Relationships
            // LegalEntity relationship (optional)
            builder.HasOne(p => p.LegalEntity)
                .WithMany() // Assuming LegalEntity does not have a collection of Payments
                .HasForeignKey(p => p.LegalEntityId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Payment_LegalEntity");

            // Currency relationship (required)
            builder.HasOne(p => p.Currency)
                .WithMany() // Assuming Currency does not have a collection of Payments
                .HasForeignKey(p => p.CurrencyId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Payment_Currency");

            // MasterClient relationship (optional)
            builder.HasOne(p => p.MasterClient)
                .WithMany() // Assuming MasterClient does not have a collection of Payments
                .HasForeignKey(p => p.MasterClientId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Payment_MasterClient");

            // Jurisdiction relationship (optional)
            builder.HasOne(p => p.Jurisdiction)
                .WithMany(x => x.Payments)
                .HasForeignKey(p => p.JurisdictionId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Payment_Jurisdiction");

            // PaymentInvoices relationship (one-to-many)
            builder.HasMany(p => p.PaymentInvoices)
                .WithOne(pi => pi.Payment)
                .HasForeignKey(pi => pi.PaymentId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_Payment_PaymentInvoices");

            // PaymentTransactions relationship (one-to-many)
            builder.HasMany(p => p.PaymentTransactions)
                .WithOne(pt => pt.Payment)
                .HasForeignKey(pt => pt.PaymentId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_Payment_PaymentTransactions");
        }
    }
}
