// <copyright file="PaymentTransactionModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Framework.EF;

namespace NetProGroup.Trust.Domain.Repository.Payments
{
    /// <summary>
    /// Model configuration for the <see cref="PaymentTransaction"/> entity.
    /// </summary>
    public class PaymentTransactionModelConfiguration : IEntityTypeConfiguration<PaymentTransaction>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="PaymentTransaction"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<PaymentTransaction> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "PaymentTransactions", TrustDbContext.DbSchema);

            // Primary key
            builder.HasKey(pt => pt.Id);
            builder.Property(pt => pt.Id).ValueGeneratedOnAdd();
            builder.Property(pt => pt.Result).HasMaxLength(50);
            builder.Property(pt => pt.ResultCode).HasMaxLength(50);
            builder.Property(pt => pt.ResultMessage).HasMaxLength(450);
            builder.Property(pt => pt.TransactionId).HasMaxLength(50);
            builder.Property(pt => pt.Status).HasMaxLength(250);
            builder.Property(pt => pt.CardDigits).HasMaxLength(250);
            builder.Property(pt => pt.FirstName).HasMaxLength(250);
            builder.Property(pt => pt.LastName).HasMaxLength(250);
            builder.Property(pt => pt.Address).HasMaxLength(450);
            builder.Property(pt => pt.City).HasMaxLength(250);
            builder.Property(pt => pt.State).HasMaxLength(450);
            builder.Property(pt => pt.ZipCode).HasMaxLength(250);
            builder.Property(pt => pt.Company).HasMaxLength(250);
            builder.Property(pt => pt.PhoneNumber).HasMaxLength(250);
            builder.Property(pt => pt.Email).HasMaxLength(250);

            // Default settings for stamped entity
            Helpers.ModelBuilder.SetStampedEntityDefaults(builder);

            // Relationships
            // Updated Relationships
            builder.HasOne(pt => pt.Payment)
                .WithMany(p => p.PaymentTransactions)
                .HasForeignKey(pt => pt.PaymentId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_PaymentTransaction_Payment");

            // Added relationship: One PaymentProvider can have many PaymentTransactions
            builder.HasOne(pt => pt.PaymentProvider)
                .WithMany(pp => pp.PaymentTransactions) // One PaymentProvider can have many PaymentTransactions
                .HasForeignKey(pt => pt.PaymentProviderId) // Foreign key in the PaymentTransaction entity
                .OnDelete(DeleteBehavior.Restrict) // Prevent cascade delete to avoid unintended deletions
                .HasConstraintName("FK_PaymentTransaction_PaymentProvider");
        }
    }
}
