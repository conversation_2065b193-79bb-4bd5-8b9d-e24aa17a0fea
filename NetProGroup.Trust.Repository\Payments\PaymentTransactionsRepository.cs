// <copyright file="PaymentTransactionsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Payments;

namespace NetProGroup.Trust.Domain.Repository.Payments
{
    /// <summary>
    /// Repository for PaymentTransactions.
    /// </summary>
    public class PaymentTransactionsRepository : RepositoryBase<TrustDbContext, PaymentTransaction, Guid>, IPaymentTransactionRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentTransactionsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public PaymentTransactionsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IPaymentTransactionRepository.DbContext => base.DbContext;
    }
}