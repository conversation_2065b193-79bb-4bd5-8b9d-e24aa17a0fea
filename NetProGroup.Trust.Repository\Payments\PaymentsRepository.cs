// <copyright file="PaymentsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;

namespace NetProGroup.Trust.Domain.Repository.Payments
{
    /// <summary>
    /// Repository for Payments.
    /// </summary>
    public class PaymentsRepository : RepositoryBase<TrustDbContext, Payment, Guid>, IPaymentRepository
    {
        private readonly IDateTimeProvider _dateTimeProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        /// <param name="dateTimeProvider">The provider for date and time operations.</param>
        public PaymentsRepository(TrustDbContext dbContext,
            IDateTimeProvider dateTimeProvider)
            : base(dbContext) =>
            _dateTimeProvider = dateTimeProvider;

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IPaymentRepository.DbContext => base.DbContext;

        /// <summary>
        /// Gets the active payment for the invoices.
        /// </summary>
        /// <param name="invoiceIds">The invoice IDs.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<Payment> GetActivePaymentForInvoices(IEnumerable<Guid> invoiceIds)
        {
            return await GetQueryable()
                .Include(p => p.PaymentInvoices)
                .Include(p => p.PaymentTransactions)
                .Where(p => p.PaymentInvoices.Any(i => invoiceIds.Contains(i.InvoiceId)) && !p.IsDeleted)
                .OrderByDescending(p => p.ExpirationDate)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Gets the payment by its ID.
        /// </summary>
        /// <param name="payment">the payment entity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<Payment> SoftDeletePayment(Payment payment)
        {
            payment.IsDeleted = true;
            payment.DeletedAt = _dateTimeProvider.UtcNow;
            await SaveChangesAsync();
            return payment;
        }
    }
}
