// <copyright file="PaymentProviderModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Payments.Provider;

namespace NetProGroup.Trust.Domain.Repository.Payments.PaymentProvider
{
    /// <summary>
    /// Model configuration for the <see cref="PaymentProvider"/> entity.
    /// </summary>
    public class PaymentProviderModelConfiguration : IEntityTypeConfiguration<NetProGroup.Trust.Domain.Payments.Provider.PaymentProvider>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="PaymentProvider"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<NetProGroup.Trust.Domain.Payments.Provider.PaymentProvider> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "PaymentProviders", TrustDbContext.DbSchema);

            // Primary key configuration
            builder.HasKey(p => p.Id);  // Configures the Id property as the primary key
            builder.Property(p => p.Id)
                   .ValueGeneratedOnAdd();  // Ensures the Id is auto-generated upon insertion

            // Properties configuration
            builder.Property(p => p.Name)
                   .IsRequired() // Makes the Name property required (not null)
                   .HasMaxLength(250) // Sets the maximum length to 250 characters
                   .HasColumnType("varchar(250)");  // Specifies the column type as varchar(250)

            builder.Property(p => p.Key)
                .IsRequired()
                .HasColumnType("varchar(MAX)");

            builder.Property(p => p.BaseUrl)
                .IsRequired()
                .HasColumnType("varchar(MAX)");

            builder.Property(p => p.ApiKey)
                .IsRequired()
                .HasColumnType("varchar(MAX)");

            builder.Property(p => p.ApiSecret)
                .IsRequired()
                .HasColumnType("varchar(MAX)");

            builder.Property(p => p.GatewayExpirationTimestamp)
                .IsRequired()
                .HasColumnType("TIME")
                .HasDefaultValue(new TimeSpan(0, 5, 0));
        }
    }
}