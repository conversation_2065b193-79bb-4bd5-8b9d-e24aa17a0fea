using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Payments.PaymentProvider;

namespace NetProGroup.Trust.Domain.Repository.Payments.PaymentProvider
{
    /// <summary>
    /// Repository for Payment Providers.
    /// </summary>
    public class PaymentProviderRepository : RepositoryBase<TrustDbContext, Domain.Payments.Provider.PaymentProvider, Guid>, IPaymentProviderRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentProviderRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public PaymentProviderRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IPaymentProviderRepository.DbContext => base.DbContext;
    }
}