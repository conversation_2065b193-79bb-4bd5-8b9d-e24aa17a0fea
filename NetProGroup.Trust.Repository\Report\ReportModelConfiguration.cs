// <copyright file="ReportModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;

namespace NetProGroup.Trust.Domain.Repository.Report
{
    /// <summary>
    /// Model configuration for the <see cref="Domain.Report.Report"/> entity.
    /// </summary>
    public class ReportModelConfiguration : IEntityTypeConfiguration<Domain.Report.Report>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the <see cref="Domain.Report.Report"/> entity for the database.
        /// </summary>
        /// <param name="builder">The builder to use for configuration.</param>
        public void Configure(EntityTypeBuilder<Domain.Report.Report> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            // Table configuration
            builder.ToTable(TrustDbContext.DbTablePrefix + "Reports", TrustDbContext.DbSchema);

            // Primary key
            builder.HasKey(p => p.Id);
            builder.Property(p => p.Id).ValueGeneratedOnAdd();

            // Default settings for stamped entity
            Helpers.ModelBuilder.SetStampedEntityDefaults(builder);

            // Properties configuration
            builder.Property(p => p.ReportName).IsRequired().HasMaxLength(250);
            builder.Property(p => p.DocumentId).IsRequired(); // Assuming ISO 4217 code format
            builder.Property(p => p.Type).IsRequired();

            // Relationships

            // Document relationship (required)
            builder.HasOne(p => p.Document)
                .WithMany()
                .HasForeignKey(p => p.DocumentId)
                .OnDelete(DeleteBehavior.Restrict)
                .HasConstraintName("FK_Report_Document");
        }
    }
}