using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Report;

namespace NetProGroup.Trust.Domain.Repository.Report
{
    /// <summary>
    /// Repository for Reports.
    /// </summary>
    public class ReportRepository : RepositoryBase<TrustDbContext, Domain.Report.Report, Guid>, IReportRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ReportRepository"/> class.
        /// </summary>
        /// <param name="context">The DbContext to use in the repository.</param>
        public ReportRepository(TrustDbContext context) : base(context)
        {
            DbContext = context;
        }

        /// <summary>
        ///     Gets the DbContext of the repository.
        /// </summary>
        public DbContext DbContext { get; }
    }
}