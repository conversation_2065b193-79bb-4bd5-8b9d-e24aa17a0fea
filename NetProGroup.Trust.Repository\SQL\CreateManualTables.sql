/****** Object:  Table [dbo].[Staging_PCP_BeneficialOwnersHistory]    Script Date: 2/12/2025 8:04:30 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_BeneficialOwnersHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_BeneficialOwnersHistory]
(
    [UniqueRelationID] [nvarchar](42) NULL,
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [EntityCode] [nvarchar](10) NULL,
    [EntityName] [nvarchar](356) NULL,
    [EntityUniqueNr] [int] NULL,
    [EntityLegacyID] [nvarchar](30) NULL,
    [BOCode] [nvarchar](10) NULL,
    [BOName] [nvarchar](356) NULL,
    [BOUniqueNr] [int] NULL,
    [BOFormerName] [nvarchar](255) NULL,
    [BOFileType] [nvarchar](50) NULL,
    [RelationType] [varchar](16) NULL,
    [BOOwnerType] [nvarchar](255) NULL,
    [BOOwnerTypeCode] [nvarchar](255) NULL,
    [BOFromDate] [datetime] NULL,
    [BOToDate] [datetime] NULL,
    [BOServiceAddress] [nvarchar](max) NULL,
    [BORegisteredAddress] [nvarchar](max) NULL,
    [BOIncorpDateOrDOB] [datetime] NULL,
    [BOIncorpCountryOrBirthCountryCode] [nvarchar](100) NULL,
    [BOIncorpCountryOrBirthCountry] [nvarchar](100) NULL,
    [BOIncorpPlaceOrBirthPlace] [nvarchar](100) NULL,
    [BOIncorpNrOrPassportNr] [nvarchar](100) NULL,
    [BONationality] [nvarchar](35) NULL,
    [BORegisteredCountry] [nvarchar](10) NULL,
    [BOProductionOffice] [nvarchar](10) NULL,
    [BOTIN] [nvarchar](100) NULL,
    [BONameOfRegulator] [nvarchar](255) NULL,
    [BORegulationCountry] [nvarchar](50) NULL,
    [BOStockExchange] [nvarchar](255) NULL,
    [BOStockCode] [nvarchar](100) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Staging_PCP_DirectorsHistory]    Script Date: 3/28/2025 6:40:49 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_DirectorsHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_DirectorsHistory]
(
    [UniqueRelationID] [nvarchar](42) NULL,
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [EntityCode] [nvarchar](10) NULL,
    [EntityName] [nvarchar](356) NULL,
    [EntityUniqueNr] [int] NULL,
    [EntityLegacyID] [nvarchar](30) NULL,
    [DirCode] [nvarchar](10) NULL,
    [DirName] [nvarchar](356) NULL,
    [DirUniqueNr] [int] NULL,
    [DirFormerName] [nvarchar](255) NULL,
    [DirFileType] [nvarchar](50) NULL,
    [RelationType] [nvarchar](150) NULL,
    [DirOfficerType] [nvarchar](100) NULL,
    [DirFromDate] [datetime] NULL,
    [DirToDate] [datetime] NULL,
    [DirStatus] [varchar](9) NULL,
    [DirServiceAddress] [nvarchar](max) NULL,
    [DirRegisteredAddress] [nvarchar](max) NULL,
    [DirIncorpDateOrDOB] [datetime] NULL,
    [DirIncorpCountryOrBirthCountryCode] [nvarchar](10) NULL,
    [DirIncorpCountryOrBirthCountry] [nvarchar](50) NULL,
    [DirIncorpPlaceOrBirthPlace] [nvarchar](100) NULL,
    [DirIncorpNrOrPassportNr] [nvarchar](100) NULL,
    [DirNationality] [nvarchar](35) NULL,
    [DirRegisteredCountry] [nvarchar](10) NULL,
    [DirProductionOffice] [nvarchar](10) NULL,
    [DirTIN] [nvarchar](100) NULL,
    [AlternateToDirCode] [nvarchar](10) NULL,
    [AlternateToDirName] [nvarchar](356) NULL,
    [LicenseeEntityCode] [nvarchar](10) NULL,
    [LicenseeEntityName] [nvarchar](356) NULL,
    [DirectorCapacity] [nvarchar](255) NULL,
    [DirectorID] [nvarchar](100) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Staging_PCP_EntitiesHistory]    Script Date: 2/12/2025 8:04:55 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_EntitiesHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_EntitiesHistory]
(
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [EntityCode] [nvarchar](10) NULL,
    [EntityName] [nvarchar](356) NULL,
    [EntityUniqueNr] [int] NULL,
    [IncorporationNumber] [nvarchar](30) NULL,
    [IncorporationDate] [datetime] NULL,
    [JurisdictionCode] [nvarchar](10) NULL,
    [Jurisdiction] [nvarchar](50) NULL,
    [EntityTypeCode] [nvarchar](10) NULL,
    [EntityType] [nvarchar](50) NULL,
    [EntityLegacyID] [nvarchar](30) NULL,
    [AdministratorCode] [nvarchar](20) NULL,
    [Administrator] [nvarchar](35) NULL,
    [ManagerCode] [nvarchar](20) NULL,
    [Manager] [nvarchar](35) NULL,
    [ReferralOfficeCode] [nvarchar](10) NULL,
    [ReferralOffice] [nvarchar](356) NULL,
    [ProductionOffice] [nvarchar](10) NULL,
    [EntityStatusCode] [nvarchar](4) NULL,
    [EntityStatus] [nvarchar](50) NULL,
    [EntitySubStatusCode] [nvarchar](10) NULL,
    [EntitySubStatus] [nvarchar](50) NULL,
    [RiskGroupCode] [nvarchar](4) NULL,
    [RiskGroup] [nvarchar](50) NULL
) ON [PRIMARY]
GO

/****** Object:  Table [dbo].[Staging_PCP_MasterClientsHistory]    Script Date: 2/12/2025 8:05:06 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT *
FROM sysobjects
WHERE name='Staging_PCP_MasterClientsHistory' AND xtype='U')
CREATE TABLE [dbo].[Staging_PCP_MasterClientsHistory]
(
    [ClientCode] [nvarchar](10) NULL,
    [ClientName] [nvarchar](356) NULL,
    [ClientUniqueNr] [int] NULL,
    [UserCode] [nvarchar](10) NULL,
    [UserName] [nvarchar](356) NULL,
    [UserPermission] [nvarchar](100) NULL,
    [UserEmailAddress] [nvarchar](250) NULL,
    [MCInfoActivated] [nvarchar](255) NULL,
    [MCInformation] [nvarchar](50) NULL,
    [BOInfoActivated] [nvarchar](255) NULL,
    [BOInformation] [nvarchar](50) NULL,
    [SHInfoActivated] [nvarchar](255) NULL,
    [SHInformation] [nvarchar](50) NULL,
    [DIRInfoActivated] [nvarchar](255) NULL,
    [DIRInformation] [nvarchar](50) NULL
) ON [PRIMARY]
GO

