﻿// <copyright file="QueuedJobModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Model configuration for a QueuedJob.
    /// </summary>
    public class QueuedJobModelConfiguration : IEntityTypeConfiguration<QueuedJob>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<QueuedJob> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "QueuedJobs", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd().HasDefaultValueSql("newid()");

            builder.Property(x => x.Job).IsRequired().HasMaxLength(ScheduledJobConsts.NameMaxLength);
            builder.Property(x => x.Data).IsRequired(false);
            builder.Property(x => x.Prio).IsRequired();
            builder.Property(x => x.CreatedAt).IsRequired();
        }
    }
}
