﻿// <copyright file="QueuedJobsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Domain.Repository.Scheduling
{
    /// <summary>
    /// Repository for ScheduledJobs.
    /// </summary>
    public class QueuedJobsRepository : RepositoryBase<TrustDbContext, QueuedJob, Guid>, IQueuedJobsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="QueuedJobsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public QueuedJobsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }
    }
}
