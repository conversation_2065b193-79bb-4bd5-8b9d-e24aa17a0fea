﻿// <copyright file="ScheduledJobModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Modules
{
    /// <summary>
    /// Model configuration for a ScheduledJob.
    /// </summary>
    public class ScheduledJobModelConfiguration : IEntityTypeConfiguration<ScheduledJob>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<ScheduledJob> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "ScheduledJobs", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });            
            builder.Property(x => x.Id).ValueGeneratedOnAdd().HasDefaultValueSql("newid()");

            builder.Property(x => x.Name).IsRequired().HasMaxLength(ScheduledJobConsts.NameMaxLength);
            builder.Property(x => x.Key).IsRequired().HasMaxLength(ScheduledJobConsts.CodeMaxLength);
            builder.Property(x => x.CronExpression).IsRequired().HasMaxLength(ScheduledJobConsts.CronExpressionMaxLength);

        }
    }
}
