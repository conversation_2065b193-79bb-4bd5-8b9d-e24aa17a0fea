﻿// <copyright file="ScheduledJobsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Scheduling;

namespace NetProGroup.Trust.Domain.Repository.Scheduling
{
    /// <summary>
    /// Repository for ScheduledJobs.
    /// </summary>
    public class ScheduledJobsRepository : RepositoryBase<TrustDbContext, ScheduledJob, Guid>, IScheduledJobsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ScheduledJobsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public ScheduledJobsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }
    }
}
