﻿// <copyright file="SettingModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Settings;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Settings
{
    /// <summary>
    /// Model configuration for a Module.
    /// </summary>
    public class SettingModelConfiguration : IEntityTypeConfiguration<Setting>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<Setting> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "Settings", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<Setting>(builder);

            builder.Property(x => x.Name).IsRequired().HasMaxLength(SettingConsts.NameMaxLength);
            builder.Property(x => x.Key).IsRequired().HasMaxLength(SettingConsts.KeyMaxLength);

            builder.HasOne(x => x.Jurisdiction).WithMany()
                .HasForeignKey(jm => jm.JurisdictionId)
                .HasConstraintName("FK_Jurisdictions_Settings");

            builder.HasOne(x => x.MasterClient).WithMany()
                .HasForeignKey(jm => jm.MasterClientId)
                .HasConstraintName("FK_MasterClients_Settings");

            builder.HasOne(x => x.LegalEntity).WithMany(entity => entity.Settings)
                .HasForeignKey(jm => jm.LegalEntityId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_LegalEntities_Settings");
        }
    }
}
