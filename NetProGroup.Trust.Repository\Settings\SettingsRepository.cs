﻿// <copyright file="SettingsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Settings;

namespace NetProGroup.Trust.Domain.Repository.Scheduling
{
    /// <summary>
    /// Repository for ScheduledJobs.
    /// </summary>
    public class SettingsRepository : RepositoryBase<TrustDbContext, Setting, Guid>, ISettingsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SettingsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SettingsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }
    }
}
