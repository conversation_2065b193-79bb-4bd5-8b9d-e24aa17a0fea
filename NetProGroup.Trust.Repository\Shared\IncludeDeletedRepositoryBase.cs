﻿// <copyright file="IncludeDeletedRepositoryBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Domain.Shared;

namespace NetProGroup.Trust.Domain.Repository.Shared
{
    /// <summary>
    /// Base repository for entities that support soft deletion.
    /// This repository includes soft-deleted entities in all queries.
    /// </summary>
    /// <typeparam name="TDbContext">The type of the DbContext for the repository.</typeparam>
    /// <typeparam name="TEntity">The entity type that the repository handles.</typeparam>
    /// <typeparam name="TKey">The type of the primary key of the entity.</typeparam>
    public class IncludeDeletedRepositoryBase<TDbContext, TEntity, TKey> : RepositoryBase<TDbContext, TEntity, TKey>
        where TEntity : Entity<TKey>, ISoftDeleted
        where TDbContext : DbContext
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="IncludeDeletedRepositoryBase{TDbContext, TEntity, TKey}"/> class.
        /// </summary>
        /// <param name="context">The DbContext for the repository.</param>
        public IncludeDeletedRepositoryBase(TDbContext context)
            : base(context)
        {
        }

        // No overrides needed as the base repository already includes all entities
    }
}
