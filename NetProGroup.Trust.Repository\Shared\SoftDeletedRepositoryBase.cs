﻿// <copyright file="SoftDeletedRepositoryBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.Domain.Repository.Shared
{
    /// <summary>
    /// Base repository for entities that support soft deletion.
    /// This repository automatically filters out soft-deleted entities in all queries.
    /// </summary>
    /// <typeparam name="TDbContext">The type of the DbContext for the repository.</typeparam>
    /// <typeparam name="TEntity">The entity type that the repository handles.</typeparam>
    /// <typeparam name="TK<PERSON>">The type of the primary key of the entity.</typeparam>
    public class SoftDeletedRepositoryBase<TDbContext, TEntity, TKey> : RepositoryBase<TDbContext, TEntity, TKey>
        where TEntity : Entity<TKey>, ISoftDeleted
        where TDbContext : DbContext
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SoftDeletedRepositoryBase{TDbContext, TEntity, TKey}"/> class.
        /// </summary>
        /// <param name="context">The DbContext for the repository.</param>
        public SoftDeletedRepositoryBase(TDbContext context)
            : base(context)
        {
        }

        /// <summary>
        /// Gets a table with "no tracking" enabled, excluding soft-deleted entities.
        /// </summary>
        public new IQueryable<TEntity> TableNoTracking => base.TableNoTracking.Where(e => !e.IsDeleted);

        /// <summary>
        /// Gets a queryable collection of entities, excluding soft-deleted entities.
        /// </summary>
        /// <returns>A queryable collection of entities.</returns>
        public new IQueryable<TEntity> GetQueryable()
        {
            return base.GetQueryable().Where(e => !e.IsDeleted);
        }

        /// <summary>
        /// Gets an entity by its ID, excluding soft-deleted entities.
        /// </summary>
        /// <param name="id">The ID of the entity to get.</param>
        /// <returns>The entity with the specified ID.</returns>
        public new TEntity GetById(TKey id)
        {
            return base.GetById(id, ExcludeDeleted);
        }

        /// <summary>
        /// Gets an entity by its ID with options, excluding soft-deleted entities.
        /// </summary>
        /// <param name="id">The ID of the entity to get.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>The entity with the specified ID.</returns>
        public new TEntity GetById(TKey id, Func<IQueryable<TEntity>, IQueryable<TEntity>> options)
        {
            return base.GetById(id, q => options == null ? ExcludeDeleted(q) : options(ExcludeDeleted(q)));
        }

        /// <summary>
        /// Gets an entity by its ID asynchronously, excluding soft-deleted entities.
        /// </summary>
        /// <param name="id">The ID of the entity to get.</param>
        /// <returns>A task that represents the asynchronous operation, containing the entity.</returns>
        public new Task<TEntity> GetByIdAsync(TKey id)
        {
            return base.GetByIdAsync(id, ExcludeDeleted);
        }

        /// <summary>
        /// Gets an entity by its ID asynchronously with options, excluding soft-deleted entities.
        /// </summary>
        /// <param name="id">The ID of the entity to get.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A task that represents the asynchronous operation, containing the entity.</returns>
        public new Task<TEntity> GetByIdAsync(TKey id, Func<IQueryable<TEntity>, IQueryable<TEntity>> options)
        {
            return base.GetByIdAsync(id, q => options == null ? ExcludeDeleted(q) : options(ExcludeDeleted(q)));
        }

        /// <summary>
        /// Finds entities by condition with options, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to filter entities.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A task that represents the asynchronous operation, containing the filtered entities.</returns>
        public new Task<IEnumerable<TEntity>> FindByConditionAsync(Expression<Func<TEntity, bool>> expression, Func<IQueryable<TEntity>, IQueryable<TEntity>> options)
        {
            return base.FindByConditionAsync(ExcludeDeleted(expression), options);
        }

        /// <summary>
        /// Finds the first entity that matches the condition with options, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to filter entities.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A task that represents the asynchronous operation, containing the first entity that matches the condition.</returns>
        public new Task<TEntity> FindFirstOrDefaultByConditionAsync(Expression<Func<TEntity, bool>> expression, Func<IQueryable<TEntity>, IQueryable<TEntity>> options)
        {
            return base.FindFirstOrDefaultByConditionAsync(ExcludeDeleted(expression), options);
        }

        /// <summary>
        /// Finds entities by condition and applies paging, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to filter entities.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The size of the page.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A task that represents the asynchronous operation, containing the paged list of entities.</returns>
        public new Task<IPagedList<TEntity>> FindByConditionAsPagedListAsync(
            Expression<Func<TEntity, bool>> expression,
            int pageNumber = 1,
            int pageSize = int.MaxValue,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null)
        {
            return base.FindByConditionAsPagedListAsync(
                ExcludeDeleted(expression),
                pageNumber,
                pageSize,
                q => options == null ? q : options(q));
        }

        /// <summary>
        /// Gets all entities with options, excluding soft-deleted entities.
        /// </summary>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A collection of all entities.</returns>
        public new IEnumerable<TEntity> FindAll(Func<IQueryable<TEntity>, IQueryable<TEntity>> options)
        {
            return base.FindAll(q => options == null ? ExcludeDeleted(q) : options(ExcludeDeleted(q)));
        }

        /// <summary>
        /// Gets all entities asynchronously with options, excluding soft-deleted entities.
        /// </summary>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A task that represents the asynchronous operation, containing all entities.</returns>
        public new Task<IEnumerable<TEntity>> FindAllAsync(Func<IQueryable<TEntity>, IQueryable<TEntity>> options)
        {
            return base.FindAllAsync(q => options == null ? ExcludeDeleted(q) : options(ExcludeDeleted(q)));
        }

        /// <summary>
        /// Finds entities by condition, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to filter entities.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A collection of entities that match the condition.</returns>
        public new IEnumerable<TEntity> FindByCondition(
            Expression<Func<TEntity, bool>> expression,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null)
        {
            return base.FindByCondition(ExcludeDeleted(expression), options);
        }

        /// <summary>
        /// Finds the first entity that matches the condition, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to filter entities.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>The first entity that matches the condition.</returns>
        public new TEntity FindFirstOrDefaultByCondition(
            Expression<Func<TEntity, bool>> expression,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null)
        {
            return base.FindFirstOrDefaultByCondition(ExcludeDeleted(expression), options);
        }

        /// <summary>
        /// Checks if any entity matches the condition, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to check.</param>
        /// <returns>True if any entity matches the condition, otherwise false.</returns>
        public new bool AnyByCondition(Expression<Func<TEntity, bool>> expression)
        {
            return base.AnyByCondition(ExcludeDeleted(expression));
        }

        /// <summary>
        /// Checks if any entity matches the condition asynchronously, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to check.</param>
        /// <returns>A task that represents the asynchronous operation, containing a boolean indicating if any entity matches the condition.</returns>
        public new Task<bool> AnyByConditionAsync(Expression<Func<TEntity, bool>> expression)
        {
            return base.AnyByConditionAsync(ExcludeDeleted(expression));
        }

        /// <summary>
        /// Finds entities by condition and applies paging, excluding soft-deleted entities.
        /// </summary>
        /// <param name="expression">The condition to filter entities.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The size of the page.</param>
        /// <param name="options">Additional options for the query.</param>
        /// <returns>A paged list of entities that match the condition.</returns>
        public new IPagedList<TEntity> FindByConditionAsPagedList(
            Expression<Func<TEntity, bool>> expression,
            int pageNumber = 1,
            int pageSize = int.MaxValue,
            Func<IQueryable<TEntity>, IQueryable<TEntity>> options = null)
        {
            return base.FindByConditionAsPagedList(
                ExcludeDeleted(expression),
                pageNumber,
                pageSize,
                q => options == null ? q : options(q));
        }

        /// <summary>
        /// Inserts a new entity.
        /// </summary>
        /// <param name="entity">The entity to insert.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>The inserted entity.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the entity is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new TEntity Insert(TEntity entity, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            if (entity.IsDeleted)
            {
                throw new InvalidOperationException("Cannot insert a soft-deleted entity.");
            }

            return base.Insert(entity, saveChanges);
        }

        /// <summary>
        /// Inserts a list of new entities.
        /// </summary>
        /// <param name="entities">The entities to insert.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>The inserted entities.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any entity is soft-deleted.</exception>
        public new IEnumerable<TEntity> Insert(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            if (entities.Any(e => e.IsDeleted))
            {
                throw new InvalidOperationException("Cannot insert soft-deleted entities.");
            }

            return base.Insert(entities, saveChanges);
        }

        /// <summary>
        /// Inserts a new entity asynchronously.
        /// </summary>
        /// <param name="entity">The entity to insert.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation, containing the inserted entity.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the entity is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new Task<TEntity> InsertAsync(TEntity entity, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            if (entity.IsDeleted)
            {
                throw new InvalidOperationException("Cannot insert a soft-deleted entity.");
            }

            return base.InsertAsync(entity, saveChanges);
        }

        /// <summary>
        /// Inserts a list of new entities asynchronously.
        /// </summary>
        /// <param name="entities">The entities to insert.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation, containing the inserted entities.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any entity is soft-deleted.</exception>
        public new Task<IEnumerable<TEntity>> InsertAsync(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            if (entities.Any(e => e.IsDeleted))
            {
                throw new InvalidOperationException("Cannot insert soft-deleted entities.");
            }

            return base.InsertAsync(entities, saveChanges);
        }

        /// <summary>
        /// Updates an entity.
        /// </summary>
        /// <param name="entity">The entity to update.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>The updated entity.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the entity is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new TEntity Update(TEntity entity, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            if (entity.IsDeleted)
            {
                throw new InvalidOperationException("Cannot update a soft-deleted entity.");
            }

            return base.Update(entity, saveChanges);
        }

        /// <summary>
        /// Updates a list of entities.
        /// </summary>
        /// <param name="entities">The entities to update.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>The updated entities.</returns>
        /// <exception cref="InvalidOperationException">Thrown if any entity is soft-deleted.</exception>
        public new IEnumerable<TEntity> Update(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            if (entities.Any(e => e.IsDeleted))
            {
                throw new InvalidOperationException("Cannot update soft-deleted entities.");
            }

            return base.Update(entities, saveChanges);
        }

        /// <summary>
        /// Updates an entity asynchronously.
        /// </summary>
        /// <param name="entity">The entity to update.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation, containing the updated entity.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the entity is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new Task<TEntity> UpdateAsync(TEntity entity, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            if (entity.IsDeleted)
            {
                throw new InvalidOperationException("Cannot update a soft-deleted entity.");
            }

            return base.UpdateAsync(entity, saveChanges);
        }

        /// <summary>
        /// Updates a list of entities asynchronously.
        /// </summary>
        /// <param name="entities">The entities to update.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation, containing the updated entities.</returns>
        /// <exception cref="ArgumentNullException">Thrown if entities is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if any entity is soft-deleted.</exception>
        public new Task<IEnumerable<TEntity>> UpdateAsync(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entities, nameof(entities));

            if (entities.Any(e => e.IsDeleted))
            {
                throw new InvalidOperationException("Cannot update soft-deleted entities.");
            }

            return base.UpdateAsync(entities, saveChanges);
        }

        /// <summary>
        /// Deletes an entity by ID.
        /// </summary>
        /// <param name="id">The ID of the entity to delete.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <exception cref="ArgumentNullException">Thrown if id is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new void DeleteById(TKey id, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(id, nameof(id));

            TEntity entity = base.GetById(id, ExcludeDeleted);
            if (entity == null)
            {
                // Check if the entity exists but is soft-deleted
                TEntity deletedEntity = base.GetById(id);
                if (deletedEntity != null && deletedEntity.IsDeleted)
                {
                    throw new InvalidOperationException("Cannot delete a soft-deleted entity.");
                }

                return; // Entity not found, nothing to delete
            }

            base.DeleteById(id, saveChanges);
        }

        /// <summary>
        /// Deletes an entity.
        /// </summary>
        /// <param name="entity">The entity to delete.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <exception cref="ArgumentNullException">Thrown if entity is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new void Delete(TEntity entity, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            if (entity.IsDeleted)
            {
                throw new InvalidOperationException("Cannot delete a soft-deleted entity.");
            }

            base.Delete(entity, saveChanges);
        }

        /// <summary>
        /// Deletes a list of entities.
        /// </summary>
        /// <param name="entities">The entities to delete.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <exception cref="ArgumentNullException">Thrown if entities is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if any entity is soft-deleted.</exception>
        public new void Delete(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entities, nameof(entities));

            if (entities.Any(e => e.IsDeleted))
            {
                throw new InvalidOperationException("Cannot delete soft-deleted entities.");
            }

            base.Delete(entities, saveChanges);
        }

        /// <summary>
        /// Deletes an entity by ID asynchronously.
        /// </summary>
        /// <param name="id">The ID of the entity to delete.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <exception cref="ArgumentNullException">Thrown if id is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new async Task DeleteByIdAsync(TKey id, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(id, nameof(id));

            TEntity entity = await base.GetByIdAsync(id, ExcludeDeleted);
            if (entity == null)
            {
                // Check if the entity exists but is soft-deleted
                TEntity deletedEntity = await base.GetByIdAsync(id);
                if (deletedEntity != null && deletedEntity.IsDeleted)
                {
                    throw new InvalidOperationException("Cannot delete a soft-deleted entity.");
                }

                return; // Entity not found, nothing to delete
            }

            await base.DeleteByIdAsync(id, saveChanges);
        }

        /// <summary>
        /// Deletes an entity asynchronously.
        /// </summary>
        /// <param name="entity">The entity to delete.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <exception cref="ArgumentNullException">Thrown if entity is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if the entity is soft-deleted.</exception>
        public new Task DeleteAsync(TEntity entity, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entity, nameof(entity));

            if (entity.IsDeleted)
            {
                throw new InvalidOperationException("Cannot delete a soft-deleted entity.");
            }

            return base.DeleteAsync(entity, saveChanges);
        }

        /// <summary>
        /// Deletes a list of entities asynchronously.
        /// </summary>
        /// <param name="entities">The entities to delete.</param>
        /// <param name="saveChanges">Whether to save changes to the database.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <exception cref="ArgumentNullException">Thrown if entities is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown if any entity is soft-deleted.</exception>
        public new Task DeleteAsync(IEnumerable<TEntity> entities, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(entities, nameof(entities));

            if (entities.Any(e => e.IsDeleted))
            {
                throw new InvalidOperationException("Cannot delete soft-deleted entities.");
            }

            return base.DeleteAsync(entities, saveChanges);
        }

        /// <summary>
        /// Gets the DbSet for the entity type.
        /// </summary>
        /// <returns>The DbSet for the entity type.</returns>
#pragma warning disable CA1024 // Use properties where appropriate
        public new DbSet<TEntity> GetDbSet()
#pragma warning restore CA1024 // Use properties where appropriate
        {
            throw new InvalidOperationException("If you want to get the DbSet, use the IncludingDeleted repository class");
        }

        /// <summary>
        /// Gets the local entities.
        /// </summary>
        /// <returns>The local entities.</returns>
#pragma warning disable CA1024 // Use properties where appropriate
        public new IEnumerable<TEntity> GetLocalEntities()
#pragma warning restore CA1024 // Use properties where appropriate
        {
            throw new InvalidOperationException("If you want to get the LocalEntities, use the IncludingDeleted repository class");
        }

        /// <summary>
        /// Excludes soft-deleted entities from the query.
        /// </summary>
        /// <param name="query">The query to filter.</param>
        /// <returns>The filtered query.</returns>
        protected static IQueryable<TEntity> ExcludeDeleted(IQueryable<TEntity> query)
        {
            return query.Where(e => !e.IsDeleted);
        }

        /// <summary>
        /// Combines the given expression with a condition to exclude soft-deleted entities.
        /// </summary>
        /// <param name="expression">The expression to combine.</param>
        /// <returns>The combined expression.</returns>
        protected static Expression<Func<TEntity, bool>> ExcludeDeleted(Expression<Func<TEntity, bool>> expression)
        {
            return expression.And(e => !e.IsDeleted);
        }
    }
}
