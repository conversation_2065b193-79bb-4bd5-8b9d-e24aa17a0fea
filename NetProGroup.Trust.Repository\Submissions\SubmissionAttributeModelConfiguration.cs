﻿// <copyright file="SubmissionAttributeModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Submissions
{
    /// <summary>
    /// Model configuration for a SubmissionAttribute.
    /// </summary>
    public class SubmissionAttributeModelConfiguration : IEntityTypeConfiguration<SubmissionAttribute>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<SubmissionAttribute> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "SubmissionAttributes", TrustDbContext.DbSchema);
            builder.HasKey(fda => new { fda.Id });
            builder.Property(fda => fda.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<SubmissionAttribute>(builder);

            builder.Property(fda => fda.Key).IsRequired().HasMaxLength(SettingConsts.KeyMaxLength);
            builder.Property(fda => fda.Name).IsRequired().HasMaxLength(SettingConsts.NameMaxLength);

            builder.HasOne(fda => fda.Submission).WithMany(fd=>fd.Attributes)
                .HasForeignKey(fda => fda.SubmissionId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_SubmissionAttribute_Submission");
        }
    }
}
