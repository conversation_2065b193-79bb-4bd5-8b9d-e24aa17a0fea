﻿// <copyright file="SubmissionAttributesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Domain.Repository.Submissions
{
    /// <summary>
    /// Repository for Submissions.
    /// </summary>
    public class SubmissionAttributesRepository : RepositoryBase<TrustDbContext, SubmissionAttribute, Guid>, ISubmissionAttributesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionAttributesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SubmissionAttributesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISubmissionAttributesRepository.DbContext => base.DbContext;
    }
}
