﻿// <copyright file="SubmissionModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Domain.Repository.Submissions
{
    /// <summary>
    /// Model configuration for a Submission.
    /// </summary>
    public class SubmissionModelConfiguration : IEntityTypeConfiguration<Submission>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<Submission> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "Submissions", TrustDbContext.DbSchema);
            builder.HasKey(s => new { s.Id });
            builder.Property(s => s.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<Submission>(builder);

            builder.Property(s => s.Name).IsRequired().HasMaxLength(SubmissionConsts.NameMaxLength);
            builder.Property(s => s.ReportId).IsRequired().HasMaxLength(SubmissionConsts.ReportIdMaxLength);
            builder.Property(s => s.Layout).IsRequired().HasMaxLength(LayoutConsts.LayoutNameMaxLength);
            builder.Property(s => s.ExportedInFinancialReportId).IsRequired(false);

            builder.HasOne(s => s.FormDocument).WithMany()
                   .HasForeignKey(s => s.FormDocumentId)
                   .HasConstraintName("FK_Submission_FormDocument");

            builder.HasOne(s => s.LegalEntity).WithMany(entity => entity.Submissions)
                .HasForeignKey(s => s.LegalEntityId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_LegalEntity");

            builder.HasOne(s => s.Module).WithMany()
                .HasForeignKey(s => s.ModuleId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_Module");

            builder.HasOne(s => s.Invoice).WithMany(entity => entity.Submissions)
                .HasForeignKey(s => s.InvoiceId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_Invoice");

            builder.HasOne(s => s.ExportedInFinancialReport).WithMany()
                .HasForeignKey(s => s.ExportedInFinancialReportId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_Report");

            builder.HasOne(s => s.SubmittedByUser).WithMany()
                .HasForeignKey(s => s.SubmittedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_ApplicationUser_1");

            builder.HasOne(s => s.ExportedByUser).WithMany()
                .HasForeignKey(s => s.ExportedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_ApplicationUser_2");

            builder.HasOne(s => s.CreatedByUser).WithMany()
                .HasForeignKey(s => s.CreatedBy)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_Submission_ApplicationUser_3");
        }
    }
}
