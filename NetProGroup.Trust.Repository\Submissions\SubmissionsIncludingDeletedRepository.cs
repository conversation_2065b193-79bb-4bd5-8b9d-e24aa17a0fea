﻿// <copyright file="SubmissionsIncludingDeletedRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Repository.Shared;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Domain.Repository.Submissions
{
    /// <summary>
    /// Repository for Submissions that includes deleted submissions.
    /// </summary>
    public class SubmissionsIncludingDeletedRepository : IncludeDeletedRepositoryBase<TrustDbContext, Submission, Guid>, ISubmissionsIncludingDeletedRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsIncludingDeletedRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SubmissionsIncludingDeletedRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISubmissionsIncludingDeletedRepository.DbContext => base.DbContext;

        /// <summary>
        /// Updates the payment status of submissions associated with a given transaction ID.
        /// </summary>
        /// <param name="transactionId">The ID of the payment transaction.</param>
        public void UpdateSubmissionPaymentStatus(Guid transactionId)
        {
            var submissions = GetQueryable()
                .Where(s => s.Invoice.PaymentInvoices
                    .Any(pi => pi.Payment.PaymentTransactions
                        .Any(pt => pt.Id == transactionId)))
                .ToList();

            if (submissions.Count != 0)
            {
                foreach (var submission in submissions)
                {
                    submission.SetPaid(true, DateTime.UtcNow);
                }

                SaveChanges();
            }
        }
    }
}
