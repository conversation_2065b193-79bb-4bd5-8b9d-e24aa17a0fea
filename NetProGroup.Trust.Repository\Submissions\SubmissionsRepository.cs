﻿// <copyright file="SubmissionsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Repository.Shared;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.Domain.Repository.Submissions
{
    /// <summary>
    /// Repository for Submissions.
    /// This repository automatically filters out soft-deleted submissions.
    /// </summary>
    public class SubmissionsRepository : SoftDeletedRepositoryBase<TrustDbContext, Submission, Guid>, ISubmissionsRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SubmissionsRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISubmissionsRepository.DbContext => base.DbContext;

        /// <summary>
        /// Updates the payment status of submissions associated with a given transaction ID.
        /// </summary>
        /// <param name="transactionId">The ID of the payment transaction.</param>
        public void UpdateSubmissionPaymentStatus(Guid transactionId) // todo move to datamanager
        {
            var submissions = GetQueryable()
                .Where(s => s.Invoice.PaymentInvoices
                    .Any(pi => pi.Payment.PaymentTransactions
                        .Any(pt => pt.Id == transactionId)))
                .ToList();

            if (submissions.Count != 0)
            {
                foreach (var submission in submissions)
                {
                    submission.SetPaid(true, DateTime.UtcNow);
                }

                SaveChanges();
            }
        }
    }
}
