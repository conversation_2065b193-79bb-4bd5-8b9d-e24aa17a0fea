﻿// <copyright file="SQLBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Tools;
using System.Text;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// SQL Query helper methods.
    /// </summary>
    public static class SQLBuilder
    {
        /// <summary>
        /// Creates a SQL statement for drop of a histyory table if exists.
        /// </summary>
        /// <param name="stagingTableName">Name of the staging table.</param>
        /// <param name="whereClause">The where clause to append to the delete query.</param>
        /// <returns>The created SQL statement.</returns>
        public static string GetDropHistoryTable(string stagingTableName, string whereClause)
        {
            var bldr = new StringBuilder();

            // bldr.AppendLine($"IF (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = '{stagingTableName}History')) ");
            // bldr.AppendLine("BEGIN ");
            // bldr.AppendLine($"Drop Table [{stagingTableName}History] ");
            // bldr.AppendLine("END");
            // bldr.AppendLine($"Truncate table {stagingTableName}History");
            bldr.AppendLine($"DELETE FROM {stagingTableName}History ");

            if (!string.IsNullOrEmpty(whereClause))
            {
                bldr.AppendLine(whereClause);
            }

            return bldr.ToString();
        }

        /// <summary>
        /// Creates a SQL statement for selecting data into the history table.
        /// </summary>
        /// <param name="stagingTableName">Name of the staging table.</param>
        /// <param name="whereClause">The where clause to append to the select query.</param>
        /// <returns>The created SQL statement.</returns>
        public static string SelectIntoHistoryTable(string stagingTableName, string whereClause)
        {
            var bldr = new StringBuilder();

            // bldr.AppendLine("SELECT * ");
            // bldr.AppendLine($"INTO [{stagingTableName}History] ");
            // bldr.AppendLine($"FROM [{stagingTableName}] ");
            bldr.AppendLine($"INSERT INTO [{stagingTableName}History] ");
            bldr.AppendLine($"SELECT * FROM [{stagingTableName}] ");

            if (!string.IsNullOrEmpty(whereClause))
            {
                bldr.AppendLine(whereClause);
            }

            return bldr.ToString();
        }

        /// <summary>
        /// Creates the SQL for creating a stored procedure using migration.
        /// </summary>
        /// <remarks>
        /// This is to wrap the actual creation of the stored procedure in an 'exec'.
        /// </remarks>
        /// <param name="storedProcedureName">Name of the stored procedure to create.</param>
        /// <param name="script">The sql script for the stored procedure.</param>
        /// <returns>The generated sql.</returns>
        public static string GetScriptForCreateStoredProcedure(string storedProcedureName, string script)
        {
            Check.NotNullOrWhiteSpace(storedProcedureName, nameof(storedProcedureName));
            Check.NotNullOrWhiteSpace(script, nameof(script));

            storedProcedureName = storedProcedureName.Replace(".sql", "", StringComparison.OrdinalIgnoreCase).Split(".").Last();

            var bldr = new StringBuilder();

            bldr.AppendLine($"IF OBJECT_ID('{storedProcedureName}', 'P') IS NOT NULL\r\nDROP PROC {storedProcedureName}\r\nGO");

            script = script.Replace("'", "''", StringComparison.OrdinalIgnoreCase);

            bldr.AppendLine($"exec ('\r\n{script}\r\n');");

            return bldr.ToString();
        }

        /// <summary>
        /// Gets a sub selection for entities in one or multiple jurisdictions.
        /// </summary>
        /// <param name="field">The name of the output field.</param>
        /// <returns>The sql.</returns>
        public static string GetEntityJurisdictionClause(string field)
        {
            var codes = SyncHelper.JurisdictionCodes.Select(jc => $"'{jc}'");

            var condition = $"({string.Join(',', codes)})";

            var sql = $"(SELECT [{field}] FROM [Staging_PCP_Entities] WHERE JurisdictionCode in {condition})";
            return sql;
        }

        /// <summary>
        /// Creates the query for insertign the fields from a staging table to the staging history table.
        /// </summary>
        /// <typeparam name="TEntity">The type of the staging entity.</typeparam>
        /// <param name="dbContext">The DBContext to get the model from.</param>
        /// <param name="stagingTableName">The name of the staging table to insert from (the source).</param>
        /// <param name="whereClause">The where cluaase to use to limit the rows to insert.</param>
        /// <returns>The resulting query.</returns>
        public static string InsertIntoHistoryTable<TEntity>(TrustDbContext dbContext, string stagingTableName, string whereClause)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));

            var fieldClause = SyncHelper.GetStagingTableFieldsClause<TEntity>(dbContext, includeId: false);

            var bldr = new StringBuilder();
            bldr.AppendLine($"INSERT INTO [{stagingTableName}History] ({fieldClause}) ");
            bldr.AppendLine($"SELECT {fieldClause} FROM [{stagingTableName}] ");

            if (!string.IsNullOrEmpty(whereClause))
            {
                bldr.AppendLine(whereClause);
            }

            return bldr.ToString();
        }
    }
}
