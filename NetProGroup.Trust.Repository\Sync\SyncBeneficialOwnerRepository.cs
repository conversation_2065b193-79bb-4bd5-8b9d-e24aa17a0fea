﻿// <copyright file="SyncBeneficialOwnerRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Repository for SyncBODirector.
    /// </summary>
    public class SyncBeneficialOwnerRepository : RepositoryBase<TrustDbContext, SyncBeneficialOwner, string>, ISyncBeneficialOwnerRepository
    {
        private const string TableName = "Staging_PCP_BeneficialOwners";

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncBeneficialOwnerRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SyncBeneficialOwnerRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISyncBeneficialOwnerRepository.DbContext => base.DbContext;

        private List<string> AllFields()
        {
            return SyncHelper.GetStagingTableFieldsAsList<SyncBeneficialOwner>(base.DbContext, includeId: false);
        }

        /// <inheritdoc/>
        public async Task<ICollection<SyncBeneficialOwner>> GetChangedBeneficialOwnersAsync()
        {
            var sqlBldr = new StringBuilder();

            var clause = SQLBuilder.GetEntityJurisdictionClause("EntityCode");

            sqlBldr.AppendLine($"IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = '{TableName}History')) ");
            sqlBldr.AppendLine("BEGIN ");
            sqlBldr.AppendLine($"SELECT convert(varchar(50), newid()) as Id, *, null AS CorporateRegistrationNo FROM [{TableName}] WHERE EntityCode IN {clause} ");
            sqlBldr.AppendLine("return ");
            sqlBldr.AppendLine("END");

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, BO.*, null AS CorporateRegistrationNo ");
            sqlBldr.AppendLine($"FROM (SELECT DISTINCT * FROM dbo.{TableName}) AS BO ");
            sqlBldr.AppendLine($"LEFT JOIN (SELECT DISTINCT * FROM dbo.{TableName}History) hist ON BO.UniqueRelationID = hist.UniqueRelationID ");
            sqlBldr.AppendLine("WHERE ( ");

            sqlBldr.AppendLine($"(BO.EntityCode IN {clause}) AND (");

            SyncHelper.AddFieldComparison(sqlBldr, "BO", AllFields());

            sqlBldr.AppendLine(")) ");
            sqlBldr.AppendLine("ORDER BY BO.UniqueRelationID, BO.ClientUniqueNr");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncBeneficialOwner>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<ICollection<SyncBeneficialOwner>> GetDeletedBeneficialOwnersAsync()
        {
            var sqlBldr = new StringBuilder();

            var fields = AllFields();
            fields.Remove("UniqueRelationID");
            fields.Remove("BOCode");
            fields.Remove("BOName");

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, ");
            sqlBldr.AppendLine("BO.ExternalUniqueId AS [UniqueRelationID], ");
            sqlBldr.AppendLine("BO.Name AS [BOName], ");
            sqlBldr.AppendLine("BO.Code AS [BOCode], ");
            sqlBldr.AppendLine("NULL AS [CorporateRegistrationNo], ");

            SyncHelper.AddFieldsAsNull(sqlBldr, fields);

            sqlBldr.AppendLine("FROM dbo.BeneficialOwners AS BO ");
            sqlBldr.AppendLine($"LEFT JOIN dbo.{TableName} SBO ON BO.ExternalUniqueId = SBO.UniqueRelationID ");
            sqlBldr.AppendLine("WHERE SBO.BOUniqueNr is null");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncBeneficialOwner>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<int> GetStagingCountAsync()
        {
            return await SyncHelper.GetStagingCountAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task<bool> StagingTableExistsAsync()
        {
            return await SyncHelper.StagingTableExistsAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task SaveLastStateAsync()
        {
            var clause = SQLBuilder.GetEntityJurisdictionClause("EntityCode");
            var whereClause = $"WHERE [EntityCode] IN {clause}";
            await SyncHelper.SaveLastStateAsync<SyncBeneficialOwner>(DbContext, TableName, whereClause);
        }
    }
}
