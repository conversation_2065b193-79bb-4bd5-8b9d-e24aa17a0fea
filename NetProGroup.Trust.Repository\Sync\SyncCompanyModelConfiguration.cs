﻿// <copyright file="SyncCompanyModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Model configuration for SyncStoredProcedures.
    /// </summary>
    public class SyncCompanyModelConfiguration : IEntityTypeConfiguration<SyncCompany>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the stored procedure result.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<SyncCompany> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "SyncCompany", TrustDbContext.DbSchema);
            builder.HasKey(s => new { s.Id });
            builder.Property(s => s.Id).ValueGeneratedNever();
        }
    }
}
