﻿// <copyright file="SyncCompanyRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Repository for SyncId.
    /// </summary>
    public class SyncCompanyRepository : RepositoryBase<TrustDbContext, SyncCompany, string>, ISyncCompanyRepository
    {
        private const string TableName = "Staging_PCP_Entities";

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncCompanyRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SyncCompanyRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISyncCompanyRepository.DbContext => base.DbContext;

        /// <inheritdoc/>
        public async Task<ICollection<SyncCompany>> GetChangedCompaniesAsync()
        {
            var sqlBldr = new StringBuilder();

            var fields = SyncHelper.GetStagingTableFieldsAsList<SyncCompany>(base.DbContext, includeId: false);

            var clause = SQLBuilder.GetEntityJurisdictionClause("EntityCode");

            sqlBldr.AppendLine($"IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = '{TableName}History')) ");
            sqlBldr.AppendLine("BEGIN ");
            sqlBldr.AppendLine($"SELECT convert(varchar(50), newid()) as Id, * FROM [{TableName}] WHERE EntityCode IN {clause}");
            sqlBldr.AppendLine("return ");
            sqlBldr.AppendLine("END");

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, C.* ");
            sqlBldr.AppendLine($"FROM dbo.{TableName} AS C ");
            sqlBldr.AppendLine($"LEFT JOIN dbo.{TableName}History hist ON C.EntityCode = hist.EntityCode");
            sqlBldr.AppendLine("WHERE ( ");

            sqlBldr.AppendLine($"(C.EntityCode IN {clause}) AND (");

            SyncHelper.AddFieldComparison(sqlBldr, "C", fields);

            sqlBldr.AppendLine(")) ");
            sqlBldr.AppendLine("ORDER BY C.ClientCode, C.ClientUniqueNr");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncCompany>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<int> GetStagingCountAsync()
        {
            return await SyncHelper.GetStagingCountAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task<bool> StagingTableExistsAsync()
        {
            return await SyncHelper.StagingTableExistsAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task SaveLastStateAsync()
        {
            var clause = SQLBuilder.GetEntityJurisdictionClause("EntityCode");
            var whereClause = $"WHERE [EntityCode] IN {clause}";
            await SyncHelper.SaveLastStateAsync<SyncCompany>(DbContext, TableName, whereClause);
        }
    }
}
