﻿// <copyright file="SyncExcludedLegalEntitiesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.DataMigrations;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Repository for LegalEntity.
    /// </summary>
    public class SyncExcludedLegalEntitiesRepository : RepositoryBase<TrustDbContext, SyncExcludedLegalEntity, Guid>, ISyncExcludedLegalEntitiesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="SyncExcludedLegalEntitiesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SyncExcludedLegalEntitiesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <inheritdoc />
        public async Task<IEnumerable<SyncExcludedLegalEntity>> GetLegalEntitiesExcludedFromSyncAsync(IEnumerable<string> legalEntityCodes)
        {
            return await FindByConditionAsync(le => legalEntityCodes.Contains(le.Code));
        }
    }
}
