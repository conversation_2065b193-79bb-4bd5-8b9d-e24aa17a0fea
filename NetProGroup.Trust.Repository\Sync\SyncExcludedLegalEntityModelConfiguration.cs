﻿// <copyright file="SyncExcludedLegalEntityModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.DataMigrations;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Model configuration for a SyncExcludedLegalEntity.
    /// </summary>
    public class SyncExcludedLegalEntityModelConfiguration : IEntityTypeConfiguration<SyncExcludedLegalEntity>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<SyncExcludedLegalEntity> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "SyncExcludedLegalEntities", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults(builder);

            builder.Property(x => x.Code).IsRequired().HasMaxLength(LegalEntityConsts.CodeMaxLength);
            builder.Property(x => x.LegacyCode).IsRequired(false).HasMaxLength(LegalEntityConsts.LegacyCodeMaxLength);

            builder.HasOne(x => x.DeletedByDataMigration)
                   .WithMany()
                   .HasForeignKey(x => x.DeletedByMigrationId)
                   .OnDelete(DeleteBehavior.Cascade)
                   .HasConstraintName("FK_SyncExcludedLegalEntity_DataMigration");

            builder.HasIndex(x => new { x.Code, x.DeletedByMigrationId }).IsUnique();
            builder.HasIndex(x => x.Code);
        }
    }
}
