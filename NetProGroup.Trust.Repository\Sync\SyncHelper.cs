﻿// <copyright file="SyncHelper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Domain.Shared.Jurisdictions;
using System.Text;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    ///     Helper methods for sync.
    /// </summary>
    public static class SyncHelper
    {
        private static readonly SemaphoreSlim _semaphore = new(1, 1);
        private static List<string> _jurisdictionCodes = new();

        /// <summary>
        ///     Gets or sets the list of jurisdictioncodes to use for the sync.
        /// </summary>
        public static List<string> JurisdictionCodes
        {
            get => _jurisdictionCodes;
            set
            {
                if (_semaphore.CurrentCount > 0)
                {
                    throw new InvalidOperationException("Must be locked to set jurisdiction codes.");
                }

                _jurisdictionCodes = value;
            }
        }

        /// <summary>
        ///     Gets the number of rows in the staging table.
        /// </summary>
        /// <param name="dbContext">The dbcontext to execute the SQL on.</param>
        /// <param name="tableName">The name of the staging table.</param>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        public static async Task<int> GetStagingCountAsync(TrustDbContext dbContext, string tableName)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));
            ArgumentException.ThrowIfNullOrWhiteSpace(tableName, nameof(tableName));

            var sql = $"SELECT COUNT(*) AS Value FROM [{tableName}]";
            var results = dbContext.Database.SqlQueryRaw<int>(sql);
            return await results.SingleAsync();
        }

        /// <summary>
        ///     Check if the staging table exists.
        /// </summary>
        /// <param name="dbContext">The dbcontext to execute the SQL on.</param>
        /// <param name="tableName">The name of the staging table.</param>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        public static async Task<bool> StagingTableExistsAsync(TrustDbContext dbContext, string tableName)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));
            ArgumentException.ThrowIfNullOrWhiteSpace(tableName, nameof(tableName));

            var sql = $"SELECT COUNT(*) AS Value FROM information_schema.tables WHERE table_name = '{tableName}'";
            var results = dbContext.Database.SqlQueryRaw<int>(sql);
            return await results.SingleAsync() > 0;
        }

        /// <summary>
        ///     Saving the last stage to history table.
        /// </summary>
        /// <param name="dbContext">The dbcontext to execute the SQL on.</param>
        /// <param name="tableName">The name of the staging table.</param>
        /// <param name="whereClause"></param>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        public static async Task SaveLastStateAsync(TrustDbContext dbContext, string tableName, string whereClause)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));
            ArgumentException.ThrowIfNullOrWhiteSpace(tableName, nameof(tableName));

            await dbContext.Database.ExecuteSqlRawAsync(SQLBuilder.GetDropHistoryTable(tableName, whereClause));
            await dbContext.Database.ExecuteSqlRawAsync(SQLBuilder.SelectIntoHistoryTable(tableName, whereClause));
        }

        /// <summary>
        ///     Saving the last state to history table.
        /// </summary>
        /// <param name="dbContext">The dbcontext to execute the SQL on.</param>
        /// <param name="tableName">The name of the staging table.</param>
        /// <param name="whereClause">Optional whereclause to limit the rows to copy.</param>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        public static async Task SaveLastStateAsync<TEntity>(TrustDbContext dbContext, string tableName, string whereClause)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));
            ArgumentException.ThrowIfNullOrWhiteSpace(tableName, nameof(tableName));

            await dbContext.Database.ExecuteSqlRawAsync(SQLBuilder.GetDropHistoryTable(tableName, whereClause));
            await dbContext.Database.ExecuteSqlRawAsync(SQLBuilder.InsertIntoHistoryTable<TEntity>(dbContext, tableName, whereClause));
        }

        /// <summary>
        ///     Adds a field comparison to the builder.
        /// </summary>
        /// <param name="bldr">The builder to add a lien to.</param>
        /// <param name="alias">Alias for the table.</param>
        /// <param name="field">Field to add.</param>
        /// <param name="lastComparison">Denotes whether this is the last comparison (for adding OR).</param>
        public static void AddFieldComparison(StringBuilder bldr, string alias, string field, bool lastComparison = false)
        {
            ArgumentNullException.ThrowIfNull(bldr, nameof(bldr));

            var line = $"ISNULL({alias}.{field}, '') <> ISNULL(hist.{field}, '') ";

            if (!lastComparison)
            {
                line += " OR ";
            }

            bldr.AppendLine(line);
        }

        public static void AddFieldComparison(StringBuilder bldr, string alias, List<string> fields)
        {
            ArgumentNullException.ThrowIfNull(bldr, nameof(bldr));
            ArgumentNullException.ThrowIfNull(fields, nameof(fields));

            for (var i = 0; i < fields.Count; i++)
            {
                AddFieldComparison(bldr, alias, fields[i], i == fields.Count - 1);
            }
        }

        /// <summary>
        ///     Adds a null field placeholder to the builder.
        /// </summary>
        /// <param name="bldr">The builder to add a lien to.</param>
        /// <param name="field">Field to add.</param>
        /// <param name="lastField">Denotes whether this is the last field (for adding ,).</param>
        public static void AddFieldAsNull(StringBuilder bldr, string field, bool lastField = false)
        {
            ArgumentNullException.ThrowIfNull(bldr, nameof(bldr));

            var line = $"NULL AS [{field}] ";

            if (!lastField)
            {
                line += ", ";
            }

            bldr.AppendLine(line);
        }

        public static void AddFieldsAsNull(StringBuilder bldr, List<string> fields)
        {
            ArgumentNullException.ThrowIfNull(bldr, nameof(bldr));
            ArgumentNullException.ThrowIfNull(fields, nameof(fields));

            for (var i = 0; i < fields.Count; i++)
            {
                AddFieldAsNull(bldr, fields[i], i == fields.Count - 1);
            }
        }

        /// <summary>
        ///     Maps the VP codes to jurisdiction codes.
        /// </summary>
        /// <param name="vpCodes">The list of VP codes to map.</param>
        /// <returns>The list of jurisdiction codes.</returns>
        /// <exception cref="APIException">Thrown when an unknown jurisdiction code is encountered.</exception>
        public static IEnumerable<string> ToJurisdictionCodes(this List<string> vpCodes)
        {
            foreach (var vpCode in vpCodes)
            {
                var jurisdictionCode = vpCode switch
                {
                    JurisdictionVPCodes.StKittsAndNevis => Trust.Shared.Jurisdictions.JurisdictionCodes.Nevis,
                    JurisdictionVPCodes.Bahamas => Trust.Shared.Jurisdictions.JurisdictionCodes.Bahamas,
                    JurisdictionVPCodes.CaymanIslands => Trust.Shared.Jurisdictions.JurisdictionCodes.CaymanIslands,
                    JurisdictionVPCodes.Cyprus => Trust.Shared.Jurisdictions.JurisdictionCodes.Cyprus,
                    JurisdictionVPCodes.BritishVirginIslands => Trust.Shared.Jurisdictions.JurisdictionCodes.BritishVirginIslands,
                    JurisdictionVPCodes.UnitedStates => Trust.Shared.Jurisdictions.JurisdictionCodes.UnitedStates,
                    JurisdictionVPCodes.Panama => Trust.Shared.Jurisdictions.JurisdictionCodes.Panama,
                    _ => throw new APIException($"Unknown jurisdiction code {vpCode}.")
                };

                yield return jurisdictionCode;
            }
        }

        /// <summary>
        ///     Gets the cluase for fields for a staging table.
        /// </summary>
        /// <typeparam name="TEntity">The type of the staging entity.</typeparam>
        /// <param name="dbContext">The DBContext to get the model from.</param>
        /// <param name="includeId">Denotes whether to include the Id field.</param>
        /// <returns>A collection of fields.</returns>
        public static string GetStagingTableFieldsClause<TEntity>(TrustDbContext dbContext, bool includeId = false)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));

            var fields = GetStagingTableFieldsAsList<TEntity>(dbContext, includeId);

            return string.Join(", ", fields);
        }

        /// <summary>
        ///     Gets the cluase for fields for a staging table.
        /// </summary>
        /// <typeparam name="TEntity">The type of the staging entity.</typeparam>
        /// <param name="dbContext">The DBContext to get the model from.</param>
        /// <param name="includeId">Denotes whether to include the Id field.</param>
        /// <returns>A collection of fields.</returns>
        public static List<string> GetStagingTableFieldsAsList<TEntity>(TrustDbContext dbContext, bool includeId = false)
        {
            ArgumentNullException.ThrowIfNull(dbContext, nameof(dbContext));

            // Get entity type from context
            var entityType = dbContext.Model.FindEntityType(typeof(TEntity));

            // Get the properties that are mapped to database columns
            var properties = entityType.GetProperties();

            var fields = new List<string>();
            foreach (var property in properties)
            {
                var fieldName = property.Name;
                if (includeId || !fieldName.Equals("id", StringComparison.OrdinalIgnoreCase))
                {
                    fields.Add(fieldName);
                }
            }

            return fields;
        }

        /// <summary>
        /// Lock the SyncHelper so that the JurisdictionCodes can safely be set.
        /// </summary>
        public static async Task LockAsync()
        {
            await _semaphore.WaitAsync();
        }

        /// <summary>
        /// Lock the SyncHelper so that the JurisdictionCodes can safely be set.
        /// </summary>
        public static void Lock()
        {
            _semaphore.Wait();
        }

        /// <summary>
        /// Unlock the SyncHelper so that another process can set and use the JurisdictionCodes.
        /// </summary>
        public static void Unlock()
        {
            if (_semaphore.CurrentCount < 1)
            {
                _semaphore.Release();
            }
        }
    }
}