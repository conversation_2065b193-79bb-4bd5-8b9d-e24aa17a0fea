﻿// <copyright file="SyncMasterClientModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Model configuration for SyncStoredProcedures.
    /// </summary>
    public class SyncMasterClientModelConfiguration : IEntityTypeConfiguration<SyncMasterClient>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<SyncMasterClient> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "SyncMasterClient", TrustDbContext.DbSchema);
            builder.HasKey(s => new { s.Id });
            builder.Property(s => s.Id).ValueGeneratedNever();
        }
    }
}
