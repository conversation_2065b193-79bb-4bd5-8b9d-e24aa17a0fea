﻿// <copyright file="SyncMasterClientRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Repository for SyncMasterClient.
    /// </summary>
    public class SyncMasterClientRepository : RepositoryBase<TrustDbContext, SyncMasterClient, string>, ISyncMasterClientRepository
    {
        private const string TableName = "Staging_PCP_MasterClients";

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncMasterClientRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SyncMasterClientRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISyncMasterClientRepository.DbContext => base.DbContext;

        /// <inheritdoc/>
        public async Task<ICollection<SyncMasterClient>> GetAllMasterClientsAsync()
        {
            var jurisdictionClause = SQLBuilder.GetEntityJurisdictionClause("ClientCode");

            var sqlBldr = new StringBuilder();
            sqlBldr.AppendLine("SELECT Convert(varchar(50), newid()) as Id ");
            sqlBldr.AppendLine(", SMC.ClientCode AS ClientCode, SMC.ClientName AS ClientName, SMC.ClientUniqueNr AS ClientUniqueNr ");
            sqlBldr.AppendLine(", SMC.UserEmailAddress AS UserEmailAddress, SMC.UserCode AS UserCode, SMC.UserName AS UserName ");
            sqlBldr.AppendLine(", SMC.UserPermission AS UserPermission ");
            sqlBldr.AppendLine(", 'update' AS [Action] ");
            sqlBldr.AppendLine($"FROM {TableName} SMC ");
            sqlBldr.AppendLine($"WHERE SMC.ClientCode IN {jurisdictionClause} ");

            sqlBldr.AppendLine("ORDER BY SMC.ClientCode, SMC.ClientUniqueNr");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncMasterClient>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<ICollection<SyncMasterClient>> GetChangedMasterClientsAsync()
        {
            var sqlBldr = new StringBuilder();
            sqlBldr.AppendLine("SELECT Convert(varchar(50), newid()) as Id ");
            sqlBldr.AppendLine(", SMC.ClientCode AS ClientCode, SMC.ClientName AS ClientName, SMC.ClientUniqueNr AS ClientUniqueNr ");
            sqlBldr.AppendLine(", SMC.UserEmailAddress AS UserEmailAddress, SMC.UserCode AS UserCode, SMC.UserName AS UserName ");
            sqlBldr.AppendLine(", SMC.UserPermission AS UserPermission ");
            sqlBldr.AppendLine(", 'update' AS [Action] ");
            sqlBldr.AppendLine($"FROM {TableName} SMC ");
            sqlBldr.AppendLine("LEFT JOIN ( ");
            sqlBldr.AppendLine("SELECT MC.Code, MC.Name, U.Email ");
            sqlBldr.AppendLine("FROM MasterClients MC ");
            sqlBldr.AppendLine("JOIN MasterClientUsers MCU ON MCU.MasterClientId = MC.Id ");
            sqlBldr.AppendLine("JOIN NetPro.Users U ON MCU.UserId = U.Id ");
            sqlBldr.AppendLine(") MC2 ");
            sqlBldr.AppendLine("ON MC2.Code = SMC.ClientCode AND MC2.Email = SMC.UserEmailAddress ");
            sqlBldr.AppendLine("WHERE MC2.Name <> SMC.ClientName");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncMasterClient>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<ICollection<SyncMasterClient>> GetAddedMasterClientsAsync()
        {
            var sqlBldr = new StringBuilder();
            sqlBldr.AppendLine("SELECT Convert(varchar(50), newid()) as Id ");
            sqlBldr.AppendLine(", SMC.ClientCode AS ClientCode, SMC.ClientName AS ClientName, SMC.ClientUniqueNr AS ClientUniqueNr ");
            sqlBldr.AppendLine(", SMC.UserEmailAddress AS UserEmailAddress, SMC.UserCode AS UserCode, SMC.UserName AS UserName ");
            sqlBldr.AppendLine(", SMC.UserPermission AS UserPermission ");
            sqlBldr.AppendLine(", 'insert' AS [Action] ");
            sqlBldr.AppendLine($"FROM {TableName} SMC ");
            sqlBldr.AppendLine("LEFT JOIN ( ");
            sqlBldr.AppendLine("SELECT MC.Code, U.Email ");
            sqlBldr.AppendLine("FROM MasterClients MC ");
            sqlBldr.AppendLine("JOIN MasterClientUsers MCU ON MCU.MasterClientId = MC.Id ");
            sqlBldr.AppendLine("JOIN NetPro.Users U ON MCU.UserId = U.Id ");
            sqlBldr.AppendLine(") MC2 ");
            sqlBldr.AppendLine("ON MC2.Code = SMC.ClientCode AND MC2.Email = SMC.UserEmailAddress ");
            sqlBldr.AppendLine("WHERE MC2.Code is null");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncMasterClient>().FromSqlRaw(sql).ToListAsync();
            return result;
        }

        /// <inheritdoc/>
        public async Task<ICollection<SyncMasterClient>> GetDeletedMasterClientsAsync()
        {
            var sqlBldr = new StringBuilder();
            sqlBldr.AppendLine("SELECT Convert(varchar(50), newid()) as Id ");
            sqlBldr.AppendLine(", MC.Code AS ClientCode, SMC.ClientName AS ClientName, SMC.ClientUniqueNr AS ClientUniqueNr ");
            sqlBldr.AppendLine(", SMC.UserEmailAddress AS UserEmailAddress, SMC.UserCode AS UserCode, SMC.UserName AS UserName ");
            sqlBldr.AppendLine(", SMC.UserPermission AS UserPermission ");
            sqlBldr.AppendLine(", 'delete' AS [Action] ");
            sqlBldr.AppendLine("FROM MasterClients MC ");
            sqlBldr.AppendLine("JOIN MasterClientUsers MCU ON MCU.MasterClientId = MC.Id AND MCU.IsManuallyAdded = 0");
            sqlBldr.AppendLine("JOIN NetPro.Users U ON MCU.UserId = U.Id ");
            sqlBldr.AppendLine($"LEFT JOIN {TableName} SMC ON SMC.ClientCode = MC.Code AND SMC.UserEmailAddress = U.Email ");
            sqlBldr.AppendLine("WHERE SMC.ClientCode is null");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncMasterClient>().FromSqlRaw(sql).ToListAsync();
            return result;
        }

        /// <inheritdoc/>
        public async Task<int> GetStagingCountAsync()
        {
            return await SyncHelper.GetStagingCountAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task<bool> StagingTableExistsAsync()
        {
            return await SyncHelper.StagingTableExistsAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task SaveLastStateAsync()
        {
            var clause = SQLBuilder.GetEntityJurisdictionClause("ClientCode");
            var whereClause = $"WHERE [ClientCode] IN {clause}";
            await SyncHelper.SaveLastStateAsync(DbContext, TableName, whereClause);
        }
    }
}
