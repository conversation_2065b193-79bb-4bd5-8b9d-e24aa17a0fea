﻿// <copyright file="ApplicationUsersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.Domain.Users;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.Domain.Repository.Users
{
    /// <summary>
    /// Repository for ApplicationUsers.
    /// </summary>
    public class ApplicationUsersRepository : UserRepository<TrustDbContext>, IApplicationUsersRepository
    {
        private readonly TrustDbContext _dbContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationUsersRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public ApplicationUsersRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        public DbContext DbContext => _dbContext;

        /// <summary>
        /// Finds entities using predicate and apply paging.
        /// </summary>
        /// <param name="expression">Expression for the search.</param>
        /// <param name="pageNumber">Page number for paging.</param>
        /// <param name="pageSize">Page size for paging.</param>
        /// <param name="options">Callback to modify the IQueryable like includes and ordering.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<IPagedList<ApplicationUser>> FindByConditionAsPagedListAsync(Expression<Func<ApplicationUser, bool>> expression, int pageNumber = 1, int pageSize = int.MaxValue, Func<IQueryable<ApplicationUser>, IQueryable<ApplicationUser>> options = null)
        {
            var query = Entities.Where(expression);
            if (options != null)
            {
                query = options(query);
            }

            return await query.ToPagedListAsync(pageNumber, pageSize);
        }
    }
}
