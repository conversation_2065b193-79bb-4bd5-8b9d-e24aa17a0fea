﻿// <copyright file="UserAttributeModelConfiguration.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NetProGroup.Framework.EF;
using NetProGroup.Trust.Domain.Users;

namespace NetProGroup.Trust.Domain.Repository.Users
{
    /// <summary>
    /// Model configuration for a UserAttribute.
    /// </summary>
    public class UserAttributeModelConfiguration : IEntityTypeConfiguration<UserAttribute>, IModelTypeConfiguration
    {
        /// <summary>
        /// Configures the model for the database.
        /// </summary>
        /// <param name="builder">The builder to use for the configuration.</param>
        public void Configure(EntityTypeBuilder<UserAttribute> builder)
        {
            ArgumentNullException.ThrowIfNull(builder);

            builder.ToTable(TrustDbContext.DbTablePrefix + "UserAttributes", TrustDbContext.DbSchema);
            builder.HasKey(x => new { x.Id });
            builder.Property(x => x.Id).ValueGeneratedOnAdd();

            Helpers.ModelBuilder.SetStampedEntityDefaults<UserAttribute>(builder);

            builder.Property(e => e.Key).IsRequired().HasMaxLength(255);

            builder.HasOne(ms => ms.User).WithMany()
                .HasForeignKey(mur => mur.UserId)
                .OnDelete(DeleteBehavior.NoAction)
                .HasConstraintName("FK_UserAttributes_Users");
        }
    }
}
