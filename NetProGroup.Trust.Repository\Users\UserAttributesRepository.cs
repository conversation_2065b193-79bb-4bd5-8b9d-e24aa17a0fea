﻿// <copyright file="UserAttributesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Users;

namespace NetProGroup.Trust.Domain.Repository.Users
{
    /// <summary>
    /// Repository for UserAttributes.
    /// </summary>
    public class UserAttributesRepository : RepositoryBase<TrustDbContext, UserAttribute, Guid>, IUserAttributesRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UserAttributesRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public UserAttributesRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext IUserAttributesRepository.DbContext => base.DbContext;
    }
}
