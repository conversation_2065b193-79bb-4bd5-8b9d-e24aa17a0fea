﻿// <copyright file="WellknownSubmissionAttributes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Attributes
{
    /// <summary>
    /// AttributeKeys for SubmissionAttributes.
    /// </summary>
    public static class WellknownSubmissionAttributes
    {
        /// <summary>
        /// Date/time of the last activity on a submssion (created / updated).
        /// </summary>
        public const string LastActivityAt = "submissions.last-activity-at";

        /// <summary>
        /// Id of the user of the last activity on a submssion (created / updated).
        /// </summary>
        public const string LastActivityBy = "submissions.last-activity-by";
    }
}
