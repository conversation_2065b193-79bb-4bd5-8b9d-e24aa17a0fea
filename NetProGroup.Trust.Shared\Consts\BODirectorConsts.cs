﻿// <copyright file="BODirectorConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants.
    /// </summary>
    public static class BODirectorConsts
    {
        /// <summary>
        /// The maximum length of the UniqueRelationId field.
        /// </summary>
        public const int UniqueRelationIdMaxLength = 25;

        /// <summary>
        /// The maximum length of the Name field.
        /// </summary>
        public const int NameMaxLength = 255;

        /// <summary>
        /// The maximum length of the Code field.
        /// </summary>
        public const int CodeMaxLength = 25;

        /// <summary>
        /// The maximum length of the RelationType field.
        /// </summary>
        public const int RelationTypeMaxLength = 25;

        /// <summary>
        /// The maximum length of the FormerName field.
        /// </summary>
        public const int FormerNameMaxLength = 100;

        /// <summary>
        /// The maximum length of the FileType field.
        /// </summary>
        public const int FileTypeMaxLength = 50;

        /// <summary>
        /// The maximum length of the OfficerType field.
        /// </summary>
        public const int OfficerTypeMaxLength = 25;

        public const int ServiceAddressMaxLength = 1000;

        public const int ResidentialOrRegisteredAddressMaxLength = 1000;

        public const int PlaceOfBirthOrIncorpMaxLength = 255;

        public const int NationalityMaxLength = 255;

        public const int CountryMaxLength = 50;

        public const int ProductionOfficeMaxLength = 10;

        public const int CorporateRegistrationNrMaxLength = 50;

        public const int TINMaxLength = 50;

        public const int NameOfRegulatorMaxLength = 255;

        public const int StockExchangeMaxLength = 100;

        public const int StockCodeMaxLength = 100;

        public const int JurisdictionOfRegulationOrSovereignStateMaxLength = 255;

        public const int BoDirIncorporationNumberMaxLength = 50;

        public const int DirectorIsAlternateToIdMaxLength = 25;

        public const int DirectorIsAlternateToNameMaxLength = 255;



        public const int StatusMaxLength = 50;

        public const int UserEmailMaxLength = 255;

        public const int TypeOfUpdateRequestMaxLength = 255;

        public const int UpdateRequestCommentsMaxLength = 255;

        public const int EntityCodeMaxLength = 50;

        public const int EntityNameMaxLength = 255;

        public const int MasterClientCodeMaxLength = 50;

        public const int CompanyNumberMaxLength = 100;
    }
}
