﻿// <copyright file="BODirectorConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants.
    /// </summary>
    public static class BODirectorConsts
    {
        /// <summary>
        /// The maximum length of the UniqueRelationId field.
        /// </summary>
        public const int UniqueRelationIdMaxLength = 25;

        /// <summary>
        /// The maximum length of the Name field.
        /// </summary>
        public const int NameMaxLength = 255;

        /// <summary>
        /// The maximum length of the Code field.
        /// </summary>
        public const int CodeMaxLength = 25;

        /// <summary>
        /// The maximum length of the RelationType field.
        /// </summary>
        public const int RelationTypeMaxLength = 25;

        /// <summary>
        /// The maximum length of the FormerName field.
        /// </summary>
        public const int FormerNameMaxLength = 100;

        /// <summary>
        /// The maximum length of the FileType field.
        /// </summary>
        public const int FileTypeMaxLength = 50;

        /// <summary>
        /// The maximum length of the OfficerType field.
        /// </summary>
        public const int OfficerTypeMaxLength = 25;

        /// <summary>
        /// The maximum lenght of the ServiceAddress field.
        /// </summary>
        public const int ServiceAddressMaxLength = 1000;

        /// <summary>
        /// The maximum lenght of the ResidentialOrRegisteredAddress field.
        /// </summary>
        public const int ResidentialOrRegisteredAddressMaxLength = 1000;

        /// <summary>
        /// The maximum lenght of the PlaceOfBirthOrIncorp field.
        /// </summary>
        public const int PlaceOfBirthOrIncorpMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the Nationality field.
        /// </summary>
        public const int NationalityMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the Country field.
        /// </summary>
        public const int CountryMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the ProductionOffice field.
        /// </summary>
        public const int ProductionOfficeMaxLength = 10;

        /// <summary>
        /// The maximum lenght of the CorporateRegistrationNr field.
        /// </summary>
        public const int CorporateRegistrationNrMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the TIN field.
        /// </summary>
        public const int TINMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the NameOfRegulator field.
        /// </summary>
        public const int NameOfRegulatorMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the StockExchange field.
        /// </summary>
        public const int StockExchangeMaxLength = 100;

        /// <summary>
        /// The maximum lenght of the StockCode field.
        /// </summary>
        public const int StockCodeMaxLength = 100;

        /// <summary>
        /// The maximum lenght of the JurisdictionOfRegulationOrSovereignState field.
        /// </summary>
        public const int JurisdictionOfRegulationOrSovereignStateMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the BoDirIncorporationNumber field.
        /// </summary>
        public const int BoDirIncorporationNumberMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the DirectorIsAlternateToId field.
        /// </summary>
        public const int DirectorIsAlternateToIdMaxLength = 25;

        /// <summary>
        /// The maximum lenght of the DirectorIsAlternateToName field.
        /// </summary>
        public const int DirectorIsAlternateToNameMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the Status field.
        /// </summary>
        public const int StatusMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the UserEmail field.
        /// </summary>
        public const int UserEmailMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the TypeOfUpdateRequest field.
        /// </summary>
        public const int TypeOfUpdateRequestMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the UpdateRequestComments field.
        /// </summary>
        public const int UpdateRequestCommentsMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the EntityCode field.
        /// </summary>
        public const int EntityCodeMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the EntityName field.
        /// </summary>
        public const int EntityNameMaxLength = 255;

        /// <summary>
        /// The maximum lenght of the MasterClientCode field.
        /// </summary>
        public const int MasterClientCodeMaxLength = 50;

        /// <summary>
        /// The maximum lenght of the CompanyNumber field.
        /// </summary>
        public const int CompanyNumberMaxLength = 100;
    }
}
