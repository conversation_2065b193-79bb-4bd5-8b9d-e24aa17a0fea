﻿// <copyright file="FormConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants.
    /// </summary>
    public static class FormConsts
    {
        /// <summary>
        /// The minimum length of the name field.
        /// </summary>
        public const int NameMinLength = 2;

        /// <summary>
        /// The maximum length of the name field.
        /// </summary>
        public const int NameMaxLength = 100;

        /// <summary>
        /// The maximum length of the key field.
        /// </summary>
        public const int KeyMaxLength = 100;

        /// <summary>
        /// The maximum length of the version field.
        /// </summary>
        public const int VersionMaxLength = 50;
    }
}
