// <copyright file="LegalEntityConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants.
    /// </summary>
    public static class LegalEntityConsts
    {
        /// <summary>
        /// The maximum length of the Name field.
        /// </summary>
        public const int NameMaxLength = 100;

        /// <summary>
        /// The maximum length of the Code field.
        /// </summary>
        public const int CodeMaxLength = 100;

        /// <summary>
        /// The maximum length of the LegacyCode field.
        /// </summary>
        public const int LegacyCodeMaxLength = 100;

        /// <summary>
        /// The maximum length of the IncorporationNr field.
        /// </summary>
        public const int IncorporationNrMaxLength = 100;

        /// <summary>
        /// The maximum length of the JurisdictionOfRegistration field.
        /// </summary>
        public const int JurisdictionOfRegistrationMaxLength = 100;

        /// <summary>
        /// The maximum length of the ReferralOffice field.
        /// </summary>
        public const int ReferralOfficeMaxLength = 100;

        /// <summary>
        /// The maximum length of the ProductionOffice field.
        /// </summary>
        public const int ProductionOfficeMaxLength = 100;

        /// <summary>
        /// The maximum length of the RiskGroup field.
        /// </summary>
        public const int RiskGroupMaxLength = 100;

        /// <summary>
        /// The maximum length of the EntityStatus field.
        /// </summary>
        public const int EntityStatusMaxLength = 100;

        /// <summary>
        /// The maximum length of the EntitySubStatus field.
        /// </summary>
        public const int EntitySubStatusMaxLength = 100;

        /// <summary>
        /// The maximum length of the CompanyType field.
        /// </summary>
        public const int CompanyTypeMaxLength = 100;

        /// <summary>
        /// The maximum length of the Administrator field.
        /// </summary>
        public const int AdministratorMaxLength = 100;

        /// <summary>
        /// The maximum length of the Manager field.
        /// </summary>
        public const int ManagerMaxLength = 100;
    }
}
