// <copyright file="LegalEntityOfficerTypes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Legal entity officer type keys used for mapping.
    /// </summary>
    public static class LegalEntityOfficerTypes
    {
        public const string Director = "Director";
        public const string AlternateDirector = "Alternate Director";
        public const string ReserveDirector = "Reserve Director";

        // BVI
        public const string VGTP01 = "VGTP01";
        public const string VGTP02 = "VGTP02";
        public const string VGTP03 = "VGTP03";
        public const string VGTP04 = "VGTP04";
        public const string VGTP05 = "VGTP05";
        public const string VGTP06 = "VGTP06";

        // Nevis
        public const string KNTP01 = "KNTP01";
        public const string KNTP02 = "KNTP02";
        public const string KNTP03 = "KNTP03";
        public const string KNTP04 = "KNTP04";
        public const string KNTP05 = "KNTP05";
        public const string KNTP06 = "KNTP06";
    }
}
