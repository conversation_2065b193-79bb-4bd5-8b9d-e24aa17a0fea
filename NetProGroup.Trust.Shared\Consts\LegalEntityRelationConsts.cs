// <copyright file="LegalEntityRelationConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants.
    /// </summary>
    public static class LegalEntityRelationConsts
    {
        /// <summary>
        /// The maximum length of the Name field.
        /// </summary>
        public const int NameMaxLength = 255;

        /// <summary>
        /// The maximum length of the Code field.
        /// </summary>
        public const int CodeMaxLength = 25;

        /// <summary>
        /// The maximum length of the OfficerTypeCode field for the officer.
        /// </summary>
        public const int OfficerTypeCodeMaxLength = 100;

        /// <summary>
        /// The maximum length of the OfficerTypeName field for the officer.
        /// </summary>
        public const int OfficerTypeNameMaxLength = 255;

        /// <summary>
        /// The maximum length of the FileType field.
        /// </summary>
        public const int FileTypeMaxLength = 50;

        /// <summary>
        /// The maximum length of the Former Name field.
        /// </summary>
        public const int FormerNameMaxLength = 255;

        /// <summary>
        /// The maximum length of the ExternalUniqueId field.
        /// </summary>
        public const int ExternalUniqueIdMaxLength = 50;

        /// <summary>
        /// The maximum length of an Address field.
        /// </summary>
        public const int AddressMaxLength = 1000;

        /// <summary>
        /// The maximum length of a field for place, country or nationality.
        /// </summary>
        public const int PlaceOrCountryMaxLength = 100;

        /// <summary>
        /// The maximum length of a field for a country code.
        /// </summary>
        public const int CountryCodeMaxLength = 10;

        /// <summary>
        /// The maximum length of the corporate registration number field.
        /// </summary>
        public const int CorporateRegistrationNumberMaxLength = 50;

        /// <summary>
        /// The maximum length of the TIN field.
        /// </summary>
        public const int TINMaxLength = 50;

        /// <summary>
        /// The maximum length of the NameOfRegulator field.
        /// </summary>
        public const int NameOfRegulatorMaxLength = 100;

        /// <summary>
        /// The maximum length of the JurisdictionOfRegulator field.
        /// </summary>
        public const int JurisdictionOfRegulatorMaxLength = 100;

        /// <summary>
        /// The maximum length of the StockCode field.
        /// </summary>
        public const int StockCodeMaxLength = 100;

        /// <summary>
        /// The maximum length of the StockExchange field.
        /// </summary>
        public const int StockExchangeMaxLength = 100;

        /// <summary>
        /// The maximum length of the UpdateRequestComments field.
        /// </summary>
        public const int UpdateRequestCommentsMaxLength = 2500;

        /// <summary>
        /// The maximum length of the CompanyNumber field.
        /// </summary>
        public const int CompanyNumberMaxLength = 100;

        /// <summary>
        /// The maximum length of the RelationType field.
        /// </summary>
        public const int RelationTypeMaxLength = 31;

        /// <summary>
        /// The maximum length of the DirectorCapacity field.
        /// </summary>
        public const int DirectorCapacityMaxLength = 255;

        /// <summary>
        /// The maximum length of the DirectorCapacity field.
        /// </summary>
        public const int DirectorIDMaxLength = 100;
    }
}
