// <copyright file="LegalEntityStatusCodes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Statuscodes for entities.
    /// </summary>
    public static class LegalEntityStatusCodes
    {
        /// <summary>
        /// Gets the VP code for Pre-Activation.
        /// </summary>
        public const string PreActivation = "P";

        /// <summary>
        /// Gets the VP code for Closing.
        /// </summary>
        public const string Closing = "G";

        /// <summary>
        /// Gets the VP code for Closed.
        /// </summary>
        public const string Closed = "C";

        /// <summary>
        /// Gets the VP code for Marked For Deletion.
        /// </summary>
        public const string MarkedForDeletion = "8";

        /// <summary>
        /// Gets the VP code for Active .
        /// </summary>
        public const string Active = "0";
    }
}
