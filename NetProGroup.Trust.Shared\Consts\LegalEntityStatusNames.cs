// <copyright file="LegalEntityStatusNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Status for entities.
    /// </summary>
    public static class LegalEntityStatusNames
    {
        /// <summary>
        /// Gets the VP code for Closing.
        /// </summary>
        public const string Closing = "Closing";

        /// <summary>
        /// Gets the VP code for Closed.
        /// </summary>
        public const string Closed = "Closed";

        /// <summary>
        /// Gets the VP code for Marked For Deletion.
        /// </summary>
        public const string MarkedForDeletion = "Marked for Deletion";

        /// <summary>
        /// Gets the VP code for Active .
        /// </summary>
        public const string Active = "Active";
    }
}
