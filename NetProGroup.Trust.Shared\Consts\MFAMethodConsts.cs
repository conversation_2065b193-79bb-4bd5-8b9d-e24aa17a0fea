﻿// <copyright file="MFAMethodConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants for MF methods.
    /// </summary>
    public static class MFAMethodConsts
    {
        /// <summary>
        /// Gets the MFAMethod using an authenticator (Google, Microsoft etc).
        /// </summary>
        public static string Authenticator => "authenticator";

        /// <summary>
        /// Gets the MFAMethod using an email code.
        /// </summary>
        public static string EmailCode => "email-code";
    }
}
