﻿// <copyright file="ScheduledJobConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants for ScheduledJob related data.
    /// </summary>
    public static class ScheduledJobConsts
    {
        /// <summary>
        /// The maximum length of the name field.
        /// </summary>
        public const int NameMaxLength = 100;

        /// <summary>
        /// The maximum length of the code field.
        /// </summary>
        public const int CodeMaxLength = 100;

        /// <summary>
        /// The maximum length of the CronExpression field.
        /// </summary>
        public const int CronExpressionMaxLength = 20;

        /// <summary>
        /// The JobId for the ViewPointSync job.
        /// </summary>
        public const string ViewPointSyncJobId = "{A3CEE95E-EB3A-415E-AE41-32B6DD569C10}";

        /// <summary>
        /// The JobId for the submissions job.
        /// </summary>
        public const string SubmissionsJobId = "{0FB46D6F-563B-4341-A41E-84F8DCB0F2BD}";
    }
}
