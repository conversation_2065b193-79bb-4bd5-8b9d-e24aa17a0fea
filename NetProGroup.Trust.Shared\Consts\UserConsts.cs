﻿// <copyright file="UserConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Constants for user related data.
    /// </summary>
    public static class UserConsts
    {
        /// <summary>
        /// Gets the Id for the fixed system user.
        /// </summary>
        public static Guid SystemUserId => Guid.Parse("6f886506-48cb-44b5-ad12-a2382e292795");

        /// <summary>
        /// Gets the Id for the fixed inbox user.
        /// </summary>
        public static Guid InboxUserId => Guid.Parse("d3e856e6-4bdc-4412-95af-22f4b188d1b2");

    }
}
