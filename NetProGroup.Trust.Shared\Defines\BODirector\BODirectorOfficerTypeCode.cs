﻿// <copyright file="BODirectorOfficerTypeCode.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.Domain.Shared.Defines.BODirector
{
    public static class BODirectorOfficerTypeCode
    {
        public const string VGTP01 = LegalEntityOfficerTypes.VGTP01;
        public const string VGTP02 = LegalEntityOfficerTypes.VGTP02;
        public const string VGTP03 = LegalEntityOfficerTypes.VGTP03;
        public const string VGTP04 = LegalEntityOfficerTypes.VGTP04;
        public const string VGTP05 = LegalEntityOfficerTypes.VGTP05;
        public const string VGTP06 = LegalEntityOfficerTypes.VGTP06;

        public const string KNTP01 = LegalEntityOfficerTypes.KNTP01;
        public const string KNTP02 = LegalEntityOfficerTypes.KNTP02;
        public const string KNTP03 = LegalEntityOfficerTypes.KNTP03;
        public const string KNTP04 = LegalEntityOfficerTypes.KNTP04;
        public const string KNTP05 = LegalEntityOfficerTypes.KNTP05;
        public const string KNTP06 = LegalEntityOfficerTypes.KNTP06;
    }
}
