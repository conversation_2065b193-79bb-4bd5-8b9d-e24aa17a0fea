// <copyright file="FormDocumentStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DomainShared.Enums
{
    /// <summary>
    /// Enumeration representing the status of a form document.
    /// </summary>
    public enum FormDocumentStatus
    {
        /// <summary>
        /// The status is 'draft'.
        /// </summary>
        /// <remarks>
        /// This is when the first document is being created (revision = 0).
        /// </remarks>
        Draft = 0,

        /// <summary>
        /// The status is 'revision'.
        /// </summary>
        /// <remarks>
        /// This is when the original (revision = 0) was finalized but modifications are required and a (new) revision is started.
        /// </remarks>
        Revision = 100,

        /// <summary>
        /// The status is 'finalized'.
        /// </summary>
        /// <remarks>
        /// This is when the latest revision (0 for first version) is finalized.
        ///  </remarks>
        Finalized = 200,
    }
}