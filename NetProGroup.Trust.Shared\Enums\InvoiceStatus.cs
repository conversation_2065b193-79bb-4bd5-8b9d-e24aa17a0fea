// <copyright file="InvoiceStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DomainShared.Enums
{
    /// <summary>
    /// Enumeration representing the status of an invoice.
    /// </summary>
    public enum InvoiceStatus
    {
        /// <summary>
        /// The status is 'Pending'.
        /// </summary>
        /// <remarks>
        /// The invoice is waiting to be collected/grouped for payment.
        /// </remarks>
        Pending = 0,

        /// <summary>
        /// The status is 'Paid'.
        /// </summary>
        /// <remarks>
        /// The invoice has been paid.
        /// </remarks>
        Paid = 1
    }
}