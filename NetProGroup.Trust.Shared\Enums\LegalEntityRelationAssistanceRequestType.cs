// <copyright file="LegalEntityRelationAssistanceRequestType.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DomainShared.Enums
{
    /// <summary>
    /// Enumeration representing the type of a assistance.
    /// </summary>
    public enum LegalEntityRelationAssistanceRequestType
    {
        /// <summary>
        /// No BeneficialOwner.
        /// </summary>        
        NoBeneficialOwner = 101,

        /// <summary>
        /// No Director.
        /// </summary>        
        NoDirector = 201,

        /// <summary>
        /// No Shareholder.
        /// </summary>        
        NoShareholder = 301,
    }
}