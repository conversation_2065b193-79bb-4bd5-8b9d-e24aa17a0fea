// <copyright file="LegalEntityRelationUpdateRequestType.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DomainShared.Enums
{
    /// <summary>
    /// Enumeration representing the type of a request for update.
    /// </summary>
    public enum LegalEntityRelationUpdateRequestType
    {
        /// <summary>
        /// None, not used.
        /// </summary>
        None = 0,

        /// <summary>
        /// MissingBeneficialOwners.
        /// </summary>
        MissingBeneficialOwners = 101,

        /// <summary>
        /// MissingDirectors.
        /// </summary>
        MissingDirectors = 102,

        /// <summary>
        /// MissingShareholders.
        /// </summary>
        MissingShareholders = 103,

        /// <summary>
        /// ChangeOfDirectors.
        /// </summary>
        ChangeOfBeneficialOwners = 201,

        /// <summary>
        /// ChangeOfBeneficialOwnersAddress.
        /// </summary>
        ChangeOfBeneficialOwnersAddress = 202,

        /// <summary>
        /// ChangeOfBeneficialOwnersParticulars.
        /// </summary>
        ChangeOfBeneficialOwnersParticulars = 203,

        /// <summary>
        /// ChangeOfDirectors.
        /// </summary>
        ChangeOfDirectors = 211,

        /// <summary>
        /// ChangeOfDirectorsAddress.
        /// </summary>
        ChangeOfDirectorsAddress = 212,

        /// <summary>
        /// ChangeOfDirectorsParticulars.
        /// </summary>
        ChangeOfDirectorsParticulars = 213,

        /// <summary>
        /// ChangeOfShareholders.
        /// </summary>
        ChangeOfShareholders = 221,

        /// <summary>
        /// ChangeOfShareholdersAddress.
        /// </summary>
        ChangeOfShareholdersAddress = 222,

        /// <summary>
        /// ChangeOfShareholdersParticulars.
        /// </summary>
        ChangeOfShareholdersParticulars = 223,

        /// <summary>
        /// OtherUpdateOfBeneficialOwners.
        /// </summary>
        OtherUpdateOfBeneficialOwners = 301,

        /// <summary>
        /// OtherUpdateOfDirectors.
        /// </summary>
        OtherUpdateOfDirectors = 302,

        /// <summary>
        /// OtherUpdateOfShareholders.
        /// </summary>
        OtherUpdateOfShareholders = 303,
    }
}