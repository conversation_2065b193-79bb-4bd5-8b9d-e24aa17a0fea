﻿// <copyright file="OnboardingStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Enums
{
    /// <summary>
    /// Represents the onboarding status of a Legal Entity.
    /// </summary>
    public enum OnboardingStatus
    {
        /// <summary>
        /// The entity is just created as part of the initial sync and must be updated by the migration.
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// The entity is in the process of being onboarded.
        /// </summary>
        Onboarding = 1,

        /// <summary>
        /// The entity has been successfully onboarded.
        /// </summary>
        Approved = 2,

        /// <summary>
        /// The entity has been declined during the onboarding process.
        /// </summary>
        Declined = 3,

        /// <summary>
        /// The entity was in the process of being onboarded and then closed from VP.
        /// </summary>
        ClosedWhileOnboarding = 4,
    }
}