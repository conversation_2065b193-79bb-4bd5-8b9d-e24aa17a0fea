// <copyright file="SubmissionStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DomainShared.Enums
{
    /// <summary>
    /// Enumeration representing the status of a submission.
    /// </summary>
    public enum SubmissionStatus
    {
        /// <summary>
        /// The status is 'draft'.
        /// </summary>
        /// <remarks>
        /// This is when the first document is being created (revision = 0).
        /// </remarks>
        Draft = 0,

        /// <summary>
        /// The status is 'revision'.
        /// </summary>
        /// <remarks>
        /// This is when the original (revision = 0) was finalized but modifications are required and a (new) revision is started.
        /// </remarks>
        Revision = 100,

        /// <summary>
        /// The status is 'scheduled'.
        /// </summary>
        /// <remarks>
        /// This is when the latest revision (0 for first version) is final but the end date of the financial period is still in
        /// the future and the submission must be submitted automatically when that date passes.
        ///  </remarks>
        Scheduled = 190,

        /// <summary>
        /// The status is 'finalized'.
        /// </summary>
        /// <remarks>
        /// This is when the latest revision (0 for first version) is finalized.
        ///  </remarks>
        Submitted = 200,

        /// <summary>
        /// The status is 'paid'.
        /// </summary>
        /// <remarks>
        /// This status is not used as actual status for the submission but used for exposing the status to the frontend.
        ///  </remarks>
        Paid = 300,

        /// <summary>
        /// The status is 'temporal'.
        /// </summary>
        /// <remarks>
        /// This status is for Panama submissions that are not fully started yet.
        ///  </remarks>
        Temporal = 400,

        /// <summary>
        /// The status is 'InformationRequested'.
        /// </summary>
        InformationRequested = 500

    }
}