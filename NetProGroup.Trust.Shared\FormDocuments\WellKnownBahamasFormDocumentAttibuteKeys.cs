// <copyright file="WellKnownBahamasFormDocumentAttibuteKeys.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Shared.FormDocuments
{
    /// <summary>
    /// A list of known attribute keys for FormDocuments.
    /// </summary>
    public static partial class WellKnownFormDocumentAttibuteKeys
    {
        #region Common names

        /// <summary>
        /// Start Date common name.
        /// </summary>
        public const string StartDate = ".startDate";

        /// <summary>
        /// End Date common name.
        /// </summary>
        public const string EndDate = ".endDate";

        /// <summary>
        /// Selected common name.
        /// </summary>
        public const string Selected = ".selected";

        #endregion

        #region FormDocument keys

        /// <summary>
        /// Relevant activity prefix.
        /// </summary>
        public const string RelevantActivities = "relevant-activity-declaration.relevantActivities.";

        /// <summary>
        /// Relevant activity 'None'.
        /// </summary>
        public const string None = "none";

        /// <summary>
        /// Relevant activity 'Holding Business'.
        /// </summary>
        public const string HoldingBusiness = "holding-business";

        /// <summary>
        /// Relevant activity 'Finance Leasing'.
        /// </summary>
        public const string FinanceLeasing = "finance-leasing";

        /// <summary>
        /// Relevant activity 'Banking Business'.
        /// </summary>
        public const string BankingBusiness = "banking-business";

        /// <summary>
        /// Relevant activity 'Insurance Business'.
        /// </summary>
        public const string InsuranceBusiness = "insurance-business";

        /// <summary>
        /// Relevant activity 'Fund Business'.
        /// </summary>
        public const string FundBusiness = "fund-business";

        /// <summary>
        /// Relevant activity 'Headquarters Business'.
        /// </summary>
        public const string HeadquartersBusiness = "headquarters-business";

        /// <summary>
        /// Relevant activity 'Shipping Business'.
        /// </summary>
        public const string ShippingBusiness = "shipping-business";

        /// <summary>
        /// Relevant activity 'Intellectual property Business'.
        /// </summary>
        public const string IntellectualPropertyBusiness = "intellectual-property-business";

        /// <summary>
        /// Relevant activity 'Distribution Business'.
        /// </summary>
        public const string DistributionBusiness = "distribution-business";

        /// <summary>
        /// Relevant activity partial financial period.
        /// </summary>
        public const string PartialFinancialPeriod = "partial-financial-period";

        #endregion
    }
}