// <copyright file="JurisdictionCodes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Shared.Jurisdictions
{
    /// <summary>
    /// Specify the configured jurisdiction codes.
    /// </summary>
    public static partial class JurisdictionCodes
    {
        /// <summary>
        /// Gets the code for Nevis.
        /// </summary>
        public const string Nevis = "Nevis";

        /// <summary>
        /// Gets the code for Cayman Islands.
        /// </summary>
        public const string CaymanIslands = "Cayman";

        /// <summary>
        /// Gets the code for Cyprus.
        /// </summary>
        public const string Cyprus = "Cyprus";

        /// <summary>
        /// Gets the code for British Virgin Islands.
        /// </summary>
        public const string BritishVirginIslands = "BVI";

        /// <summary>
        /// Gets the code for United States.
        /// </summary>
        public const string UnitedStates = "US";

        /// <summary>
        /// Gets the code for Panama.
        /// </summary>
        public const string Panama = "Panama";

        /// <summary>
        /// Gets the code for Bahamas.
        /// </summary>
        public const string Bahamas = "Bahamas";
    }
}