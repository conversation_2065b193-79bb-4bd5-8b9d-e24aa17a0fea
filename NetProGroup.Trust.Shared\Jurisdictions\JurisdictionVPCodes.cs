// <copyright file="JurisdictionVPCodes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Jurisdictions
{
    /// <summary>
    /// The used codes for the jurisdictions in ViewPoint.
    /// </summary>
    public static class JurisdictionVPCodes
    {
        /// <summary>
        /// Gets the VP code for Cayman Islands.
        /// </summary>
        public const string CaymanIslands = "KY";

        /// <summary>
        /// Gets the VP code for Cyprus.
        /// </summary>
        public const string Cyprus = "CY";

        /// <summary>
        /// Gets the VP code for British Virgin Islands.
        /// </summary>
        public const string BritishVirginIslands = "VG";

        /// <summary>
        /// Gets the VP code for United States.
        /// </summary>
        public const string UnitedStates = "US";

        /// <summary>
        /// Gets the VP code for Saint Kitts and Nevis.
        /// </summary>
        public const string StKittsAndNevis = "KN";

        /// <summary>
        /// Gets the VP code for Bahamas.
        /// </summary>
        public const string Bahamas = "BS";

        /// <summary>
        /// Gets the VP code for Panama.
        /// </summary>
        public const string Panama = "PA";
    }
}
