// <copyright file="SystemName.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.MessageTemplates
{
    /// <summary>
    /// Holds all the template name configured in the system.
    /// </summary>
    public static class SystemNames
    {
        /// <summary>
        /// Template with system name MFACodeMessage.
        /// </summary>
        public const string MFACodeMessage = nameof(MFACodeMessage);

        /// <summary>
        /// Template for a reset of a user MFA.
        /// </summary>
        public const string MFAResetRequestMessage = nameof(MFAResetRequestMessage);

        /// <summary>
        /// Template for notifying of an UpdateRequest.
        /// </summary>
        public const string RequestUpdateMessage = nameof(RequestUpdateMessage);

        /// <summary>
        /// Template for notifying of an AssistanceRequest.
        /// </summary>
        public const string RequestAssistanceMessage = nameof(RequestAssistanceMessage);

        /// <summary>
        /// Template for an invitation of a masterclient user.
        /// </summary>
        public const string UserInvitationMessage = nameof(UserInvitationMessage);

        /// <summary>
        /// Template for an invitation of an existing masterclient user for reregistration.
        /// </summary>
        public const string UserInvitationReregistrationMessage = nameof(UserInvitationReregistrationMessage);

        /// <summary>
        /// Template for new announcements.
        /// </summary>
        public const string NewAnnouncementMessage = nameof(NewAnnouncementMessage);
    }
}