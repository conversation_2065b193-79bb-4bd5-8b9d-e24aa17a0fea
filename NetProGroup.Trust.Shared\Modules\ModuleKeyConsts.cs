﻿// <copyright file="ModuleKeyConsts.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Modules
{
    /// <summary>
    /// Constants for module related data.
    /// </summary>
    public static class ModuleKeyConsts
    {
        /// <summary>
        /// The key for the SimplifiedTaxReturn module.
        /// </summary>
        public const string SimplifiedTaxReturn = nameof(SimplifiedTaxReturn);

        /// <summary>
        /// The key for the BODirectors module.
        /// </summary>
        public const string BODirectors = nameof(BODirectors);

        /// <summary>
        /// The key for the Basic Financial Report module for Panama.
        /// </summary>
        public const string BasicFinancialReportPanama = nameof(BasicFinancialReportPanama);

        /// <summary>
        /// The key for the EconomicSubstance module for Bahamas.
        /// </summary>
        public const string EconomicSubstanceBahamas = nameof(EconomicSubstanceBahamas);

        /// <summary>
        /// The key for the EconomicSubstance module for BVI.
        /// </summary>
        public const string EconomicSubstanceBVI = nameof(EconomicSubstanceBVI);

        /// <summary>
        /// The key for the RFI module for all jurisdictions.
        /// </summary>
        public const string RFI = nameof(RFI);

        /// <summary>
        /// The key for the Announcements module for all jurisdictions.
        /// </summary>
        public const string Announcements = nameof(Announcements);
    }
}
