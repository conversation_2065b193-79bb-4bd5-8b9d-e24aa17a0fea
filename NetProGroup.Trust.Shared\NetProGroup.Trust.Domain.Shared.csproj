﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
	</PropertyGroup>
	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1014;CA1716;SA1009</NoWarn>
		<!-- CA1014 == CLS Compliancy, not required -->
		<!-- CA1716 == Don't use Shared keyword in namespace. -->
		<!-- SA1009 =- Closing parenthesis should not be proceded by a blank line (readability is better in the current format)-->
	</PropertyGroup>

	<ItemGroup>
		<None Remove="stylecop.json" />
	</ItemGroup>

	<ItemGroup>
		<AdditionalFiles Include="stylecop.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.22" />
		<PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
		<PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

</Project>
