﻿// <copyright file="WellKnownAnnouncementPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the announcement module.
    /// </summary>
#pragma warning disable SA1202 // Elements should be ordered by access
#pragma warning disable SA1310 // Field names should not contain underscore
    public static partial class WellKnownPermissionNames
    {
        private const string AnnouncementModule = "announcements";

        /// <summary>
        /// Search announcement.
        /// </summary>
        public const string AnnouncementModule_Search = AnnouncementModule + ".search";

        /// <summary>
        /// View announcement module.
        /// </summary>
        public const string AnnouncementModule_View = AnnouncementModule + ".view";

        /// <summary>
        /// View announcement module.
        /// </summary>
        public const string AnnouncementModule_Delete = AnnouncementModule + ".delete";

        /// <summary>
        /// Create announcement with limitations.
        /// </summary>
        public const string AnnouncementModule_Create_Limited = AnnouncementModule + ".create-limited";

        /// <summary>
        /// Create announcement without limitations.
        /// </summary>
        public const string AnnouncementModule_Create = AnnouncementModule + ".create";
    }
#pragma warning restore SA1310 // Field names should not contain underscore
#pragma warning restore SA1202 // Elements should be ordered by access
}
