﻿// <copyright file="WellKnownSTRPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the BFR Panama module.
    /// </summary>
    public static partial class WellKnownPermissionNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore

        private const string BFRPanamaModule = "bfr.panama";
        private const string BFRPanamaModule_Submissions = BFRPanamaModule + ".submissions";

        /// <summary>
        /// View STR submissions.
        /// </summary>
        public const string BFRPanamaModule_Submissions_View = BFRPanamaModule_Submissions + ".view";

        /// <summary>
        /// Search STR submissions.
        /// </summary>
        public const string BFRPanamaModule_Submissions_Search = BFRPanamaModule_Submissions + ".search";

        /// <summary>
        /// Export STR submissions.
        /// </summary>
        public const string BFRPanamaModule_Submissions_Export = BFRPanamaModule_Submissions + ".export";

        /// <summary>
        /// Reset STR submissions to Saved/Re-open.
        /// </summary>
        public const string BFRPanamaModule_Submissions_Reset = BFRPanamaModule_Submissions + ".reset";

        /// <summary>
        /// View paid/unpaid.
        /// </summary>
        public const string BFRPanamaModule_Submissions_View_Paid = BFRPanamaModule_Submissions + ".view-paid";

        /// <summary>
        /// Mark as paid.
        /// </summary>
        public const string BFRPanamaModule_Submissions_Mark_Paid = BFRPanamaModule_Submissions + ".mark-paid";

        /// <summary>
        /// Export invoices.
        /// </summary>
        public const string BFRPanamaModule_Invoices_Export = BFRPanamaModule + ".invoices.export";

        /// <summary>
        /// Request for information view.
        /// </summary>
        public const string BFRPanamaModule_View_RFI = BFRPanamaModule + ".rfi-request.view";

        /// <summary>
        /// Request for information start.
        /// </summary>
        public const string BFRPanamaModule_Start_RFI_Request = BFRPanamaModule + ".rfi-request.start";

        /// <summary>
        /// Request for information cancel.
        /// </summary>
        public const string BFRPanamaModule_Cancel_RFI_Request = BFRPanamaModule + ".rfi-request.cancel";
#pragma warning restore SA1310 // Field names should not contain underscore

    }
}
