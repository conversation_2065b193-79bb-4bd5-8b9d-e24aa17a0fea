﻿// <copyright file="WellKnownBODIRPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the BO (Beneficial Owner) / DIR (Director) module.
    /// </summary>
#pragma warning disable SA1202 // Elements should be ordered by access
#pragma warning disable SA1310 // Field names should not contain underscore
    public static partial class WellKnownPermissionNames
    {
        private const string BODIRModule = "bo-dir";

        /// <summary>
        /// Search BO/DIR.
        /// </summary>
        public const string BODIRModule_Search = BODIRModule + ".search";

        /// <summary>
        /// View BO/DIR.
        /// </summary>
        public const string BODIRModule_View = BODIRModule + ".view";

        /// <summary>
        /// Export BO/DIR.
        /// </summary>
        public const string BODIRModule_Export = BODIRModule + ".export";
    }
#pragma warning restore SA1310 // Field names should not contain underscore
#pragma warning restore SA1202 // Elements should be ordered by access
}
