﻿// <copyright file="WellKnownBODIRPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the BO (Beneficial Owner) / DIR (Director) module.
    /// </summary>
    public static partial class WellKnownPermissionNames
    {
        private const string BODIRModule = "bo-dir";

        /// <summary>
        /// Search BO/DIR.
        /// </summary>
        public const string BODIRModule_Search = BODIRModule + ".search";

        /// <summary>
        /// View BO/DIR.
        /// </summary>
        public const string BODIRModule_View = BODIRModule + ".view";

        /// <summary>
        /// Export BO/DIR.
        /// </summary>
        public const string BODIRModule_Export = BODIRModule + ".export";
    }
}
