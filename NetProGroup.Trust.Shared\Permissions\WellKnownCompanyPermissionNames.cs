﻿// <copyright file="WellKnownCompanyPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for company actions.
    /// </summary>
#pragma warning disable SA1202 // Elements should be ordered by access
#pragma warning disable SA1310 // Field names should not contain underscore
    public static partial class WellKnownPermissionNames
    {
        private const string Companies = "companies";
        private const string Companies_Onboarding = Companies + ".onboarding";

        /// <summary>
        /// Companies Access Onboarding.
        /// </summary>
        public const string Companies_Access_Onboarding = Companies_Onboarding + ".access";

        /// <summary>
        /// Companies Approve Onboarding.
        /// </summary>
        public const string Companies_Approve_Onboarding = Companies_Onboarding + ".approve";

        /// <summary>
        /// Companies Reject Onboarding.
        /// </summary>
        public const string Companies_Reject_Onboarding = Companies_Onboarding + ".reject";

        /// <summary>
        /// Search Companies.
        /// </summary>
        public const string Companies_Search = Companies + ".search";

        /// <summary>
        /// View Companies.
        /// </summary>
        public const string Companies_View = Companies + ".view";

        /// <summary>
        /// View Available Modules.
        /// </summary>
        public const string Companies_View_Available_Modules = Companies + ".modules.available.view";

        /// <summary>
        /// Set Available Modules.
        /// </summary>
        public const string Companies_Set_Available_Modules = Companies + ".modules.available.set";

        /// <summary>
        /// View custom STR fee.
        /// </summary>
        public const string Companies_View_Custom_STR_Fee = Companies + ".custom-str-fee.view";

        /// <summary>
        /// Set custom STR fee.
        /// </summary>
        public const string Companies_Set_Custom_STR_Fee = Companies + ".custom-str-fee.set";

        /// <summary>
        /// Set Late Payments Exemption STR.
        /// </summary>
        public const string Companies_Set_Late_Payment_Exemption_STR = Companies + ".late-payment-exemption-str.set";

        /// <summary>
        /// View custom BFR fee.
        /// </summary>
        public const string Companies_View_Custom_BFR_Fee = Companies + ".custom-bfr-fee.view";

        /// <summary>
        /// Set custom BFR fee.
        /// </summary>
        public const string Companies_Set_Custom_BFR_Fee = Companies + ".custom-bfr-fee.set";

        /// <summary>
        /// View annual fee.
        /// </summary>
        public const string Companies_View_Annual_Fee = Companies + ".annual-fee.view";

        /// <summary>
        /// Set annual fee.
        /// </summary>
        public const string Companies_Set_Annual_Fee = Companies + ".annual-fee.set";

        /// <summary>
        /// View company log.
        /// </summary>
        public const string Companies_View_Log = Companies + ".log.view";

        /// <summary>
        /// View First Submission Year for the company.
        /// </summary>
        public const string Companies_View_First_Submission_Year = Companies + ".first-submission-year.view";

        /// <summary>
        /// Set First Submission Year for the company.
        /// </summary>
        public const string Companies_Set_First_Submission_Year = Companies + ".first-submission-year.set";

        /// <summary>
        /// Move/Delete submissions on company view .
        /// </summary>
        public const string Companies_Move_Delete_Submissions = Companies + ".submissions.move-delete";

        /// <summary>
        /// Set company back to onboarding when no submissions.
        /// </summary>
        public const string Companies_Set_Back_To_Onboarding_No_Submissions = Companies + ".onboarding-status.reset";

        /// <summary>
        /// View STR submission log.
        /// </summary>
        public const string Companies_View_STR_Submission_Log = Companies + ".str-submission.log.view";
    }
#pragma warning restore SA1310 // Field names should not contain underscore
#pragma warning restore SA1202 // Elements should be ordered by access
}
