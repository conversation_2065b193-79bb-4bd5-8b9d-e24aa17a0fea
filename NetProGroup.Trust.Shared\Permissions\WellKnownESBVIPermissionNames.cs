﻿// <copyright file="WellKnownESBVIPerrmissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the Economic Substance BVI module.
    /// </summary>
    public static partial class WellKnownPermissionNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore

        private const string ESBVIModule = "es.bvi";
        private const string ESBVIModule_Submissions = ESBVIModule + ".submissions";

        /// <summary>
        /// View STR submissions.
        /// </summary>
        public const string ESBVIModule_Submissions_View = ESBVIModule_Submissions + ".view";

        /// <summary>
        /// Search STR submissions.
        /// </summary>
        public const string ESBVIModule_Submissions_Search = ESBVIModule_Submissions + ".search";

        /// <summary>
        /// Export STR submissions.
        /// </summary>
        public const string ESBVIModule_Submissions_Export = ESBVIModule_Submissions + ".export";

        /// <summary>
        /// Reset STR submissions to Saved/Re-open.
        /// </summary>
        public const string ESBVIModule_Submissions_Reset = ESBVIModule_Submissions + ".reset";

        /// <summary>
        /// Delete completed STR submissions.
        /// </summary>
        public const string ESBVIModule_Submissions_Delete_Completed = ESBVIModule_Submissions + ".delete-completed";

        /// <summary>
        /// Delete saved STR submissions.
        /// </summary>
        public const string ESBVIModule_Submissions_Delete_Saved = ESBVIModule_Submissions + ".delete-saved";

        /// <summary>
        /// View paid/unpaid.
        /// </summary>
        public const string ESBVIModule_Submissions_View_Paid = ESBVIModule_Submissions + ".view-paid";

        /// <summary>
        /// Mark as paid.
        /// </summary>
        public const string ESBVIModule_Submissions_Mark_Paid = ESBVIModule_Submissions + ".mark-paid";

        /// <summary>
        /// Import payments.
        /// </summary>
        public const string ESBVIModule_Payments_Import = ESBVIModule + ".payments.import";

        /// <summary>
        /// Export submissions to ITA.
        /// </summary>
        public const string ESBVIModule_Submissions_Export_ITA = ESBVIModule_Submissions + ".export.ita";

        /// <summary>
        /// Export invoices.
        /// </summary>
        public const string ESBVIModule_Invoices_Export = ESBVIModule + ".invoices.export";

        /// <summary>
        /// Management information.
        /// </summary>
        public const string ESBVIModule_Start_RFI_Request = ESBVIModule + ".rfi-request.start";

        /// <summary>
        /// View custom ES fee.
        /// </summary>
        public const string ESBVIModule_Companies_View_Custom_ES_Fee = ESBVIModule + "." + Companies + ".custom-es-fee.view";

        /// <summary>
        /// Set custom ES fee.
        /// </summary>
        public const string ESBVIModule_Companies_Set_Custom_ES_Fee = ESBVIModule + "." + Companies + ".custom-es-fee.set";

#pragma warning restore SA1310 // Field names should not contain underscore

    }
}
