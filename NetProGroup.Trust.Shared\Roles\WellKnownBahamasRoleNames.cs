// <copyright file="WellKnownBahamasRoleNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Roles
{
    /// <summary>
    /// Specify the configured application roles for Bahamas.
    /// </summary>
    public static partial class WellKnownRoleNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore
        /// <summary>
        /// Role is for Bahamas.
        /// </summary>
        public const string Bahamas = "Bahamas";

        /// <summary>
        /// Role for the Bahamas owner.
        /// </summary>
        public const string Bahamas_Owner = Bahamas + ".Owner";

        /// <summary>
        /// Role for the Bahamas Basic User.
        /// </summary>
        public const string Bahamas_Basic_User = Bahamas + ".BasicUser";

        /// <summary>
        /// Role for the Bahamas CMU SuperUser.
        /// </summary>
        public const string Bahamas_CMU_SuperUser = Bahamas + ".CMU.SuperUser";

        /// <summary>
        /// Role for the Bahamas Officers SuperUser.
        /// </summary>
        public const string Bahamas_Officers_SuperUser = Bahamas + ".Officers.SuperUser";

        /// <summary>
        /// Role for the Bahamas ES SuperUser.
        /// </summary>
        public const string Bahamas_ES_SuperUser = Bahamas + ".ES.SuperUser";

        /// <summary>
        /// Role for the Bahamas COM SuperUser.
        /// </summary>
        public const string Bahamas_COM_SuperUser = Bahamas + ".COM.SuperUser";

#pragma warning restore SA1310 // Field names should not contain underscore

    }
}