// <copyright file="WellKnownNevisRoleNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Roles
{
    /// <summary>
    /// Specify the configured application roles for Nevis.
    /// </summary>
    public static partial class WellKnownRoleNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore
        /// <summary>
        /// Role is for Nevis. This MUST match the code of the jurisdiction.
        /// </summary>
        public const string Nevis = "Nevis";

        /// <summary>
        /// Role for the Nevis owner.
        /// </summary>
        public const string Nevis_Owner = Nevis + ".Owner";

        /// <summary>
        /// Role for the Nevis Basic User.
        /// </summary>
        public const string Nevis_Basic_User = Nevis + ".BasicUser";

        /// <summary>
        /// Role for the Nevis CMU SuperUser.
        /// </summary>
        public const string Nevis_CMU_SuperUser = Nevis + ".CMU.SuperUser";

        /// <summary>
        /// Role for the Nevis STR SuperUser.
        /// </summary>
        public const string Nevis_STR_SuperUser = Nevis + ".STR.SuperUser";

        /// <summary>
        /// Role for the Nevis Officers SuperUser.
        /// </summary>
        public const string Nevis_Officers_SuperUser = Nevis + ".Officers.SuperUser";

        /// <summary>
        /// Role for the Nevis COM SuperUser.
        /// </summary>
        public const string Nevis_COM_SuperUser = Nevis + ".COM.SuperUser";

#pragma warning restore SA1310 // Field names should not contain underscore

    }
}