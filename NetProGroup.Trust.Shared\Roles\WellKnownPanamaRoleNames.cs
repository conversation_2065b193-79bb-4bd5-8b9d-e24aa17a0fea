// <copyright file="WellKnownPanamaRoleNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Roles
{
    /// <summary>
    /// Specify the configured application roles for Panama.
    /// </summary>
    public static partial class WellKnownRoleNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore
        /// <summary>
        /// Role is for Panama.
        /// </summary>
        public const string Panama = "Panama";

        /// <summary>
        /// Role for the Panama owner.
        /// </summary>
        public const string Panama_Owner = Panama + ".Owner";

        /// <summary>
        /// Role for the Panama Basic User.
        /// </summary>
        public const string Panama_Basic_User = Panama + ".BasicUser";

        /// <summary>
        /// Role for the Panama CMU SuperUser.
        /// </summary>
        public const string Panama_CMU_SuperUser = Panama + ".CMU.SuperUser";

        /// <summary>
        /// Role for the Panama Officers SuperUser.
        /// </summary>
        public const string Panama_Officers_SuperUser = Panama + ".Officers.SuperUser";

        /// <summary>
        /// Role for the Panama ACC Records SuperUser.
        /// </summary>
        public const string Panama_ACC_Records_SuperUser = Panama + ".ACC.Records.SuperUser";

        /// <summary>
        /// Role for the Panama COM SuperUser.
        /// </summary>
        public const string Panama_COM_SuperUser = Panama + ".COM.SuperUser";

#pragma warning restore SA1310 // Field names should not contain underscore

    }
}