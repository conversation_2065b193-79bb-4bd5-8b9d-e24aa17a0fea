// <copyright file="WellKnownRoleIds.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Roles
{
    /// <summary>
    /// Specify the configured application roles.
    /// </summary>
    public static partial class WellKnownRoleIds
    {
#pragma warning disable SA1310 // Field names should not contain underscore

        /// <summary>
        /// Gets the id of the System role.
        /// </summary>
        public static Guid System => Guid.Parse("62390aa0-4979-41f5-a5b9-e52c2790d05d");

        /// <summary>
        /// Gets the id of the Client role.
        /// </summary>
        public static Guid Client => Guid.Parse("412CAC53-4979-4C63-9DEC-E8A38DE9BB62");

#pragma warning restore SA1310 // Field names should not contain underscore
    }
}