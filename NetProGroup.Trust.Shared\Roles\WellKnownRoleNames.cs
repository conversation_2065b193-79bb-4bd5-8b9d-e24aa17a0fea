// <copyright file="WellKnownRoleNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Roles
{
    /// <summary>
    /// Specify the configured application roles.
    /// </summary>
    public static partial class WellKnownRoleNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore

        /// <summary>
        /// User is 'System'.
        /// </summary>
        public const string System = "System";

        /// <summary>
        /// User is 'Client'.
        /// </summary>
        public const string Client = "Client";

        /// <summary>
        /// User is 'SuperAdmin'.
        /// </summary>
        public const string Common_SuperAdmin = "SuperAdmin";

        /// <summary>
        /// COM Owner for all jurisdictions.
        /// </summary>
        public const string Common_COM_Owner = "All.COM.Owner";

        /// <summary>
        /// SupportUser for all jurisdictions.
        /// </summary>
        public const string Common_SupportUser = "All.SupportUser";

        /// <summary>
        /// SupportUser for all jurisdictions.
        /// </summary>
        public const string Common_CMU_SuperUser = "All.CMU.SuperUser";

        /// <summary>
        /// BasicUser for all jurisdictions.
        /// </summary>
        public const string Common_BasicUser = "All.BasicUser";

#pragma warning restore SA1310 // Field names should not contain underscore
    }
}