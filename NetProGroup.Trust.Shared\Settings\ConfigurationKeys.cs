﻿// <copyright file="ConfigurationKeys.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Settings
{
    /// <summary>
    /// Represents the various keys for configuration settings.
    /// </summary>
    public static class ConfigurationKeys
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented

        /// <summary>
        /// The configuration for 'late payment fees' for Simple Tax Return.
        /// </summary>
        public const string STRLatePaymentFeeConfiguration = "str-late-payment-fee-configuration";

        /// <summary>
        /// The configuration for invoice numbering.
        /// </summary>
        public const string InvoiceNumberingConfiguration = "invoice-numbering-configuration";

        /// <summary>
        /// The setting with the last generated invoicenumber.
        /// </summary>
        /// <remarks>
        /// If a prefix is used, it is after the ':'.
        /// For example 47854:24/05 for prefix 24/05.
        /// </remarks>
        public const string LastInvoiceNumber = "last-invoice-number";

        /// <summary>
        /// The setting for an exempt on late payment fee.
        /// </summary>
        public const string STRSubmissionLatePaymentFeeExempt = "str-submission-late-payment-fee-exempt";

        /// <summary>
        /// The setting for submission fee.
        /// </summary>
        public const string STRSubmissionFee = "str-submission-fee";

        /// <summary>
        /// The setting for BFR submission fee.
        /// </summary>
        public const string BFRSubmissionFee = "bfr-submission-fee";

        /// <summary>
        /// The setting for the ITA approved start date.
        /// </summary>
        public const string ITAApprovedStartDate = "ita-approved-start-date";

        /// <summary>
        /// The setting for the ITA approved end date.
        /// </summary>
        public const string ITAApprovedEndDate = "ita-approved-End-date";

#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member

    }
}
