﻿// <copyright file="Documents.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Settings
{
    /// <summary>
    /// Represents the various keys for document settings.
    /// </summary>
    public static class DocumentKeys
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented

        public const string Header = "documents.header";
        public const string Footer = "documents.footer";
        public const string CompanyInfo = "documents.company-info";

        /// <summary>
        /// Attribute with date/time of first time of report.
        /// </summary>
        public const string FirstReport = "documents.first-report";

        /// <summary>
        /// Attribute with date/time of last time of report.
        /// </summary>
        public const string LastReport = "documents.last-report";

#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member

    }
}
