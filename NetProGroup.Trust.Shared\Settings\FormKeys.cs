﻿namespace NetProGroup.Trust.Domain.Shared.Settings
{
    /// <summary>
    /// Represents the various keys for forms.
    /// </summary>
    public static class FormKeys
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented

        // Prior submission
        private const string PriorSubmissionPrefix = "prior-submission";
        public const string PriorSubmissionExists = $"{PriorSubmissionPrefix}.exists";
        public const string PriorSubmissionFinancialPeriodEndDate = $"{PriorSubmissionPrefix}.financial-period.enddate";

        // ITA keys
        private const string ITAPrefix = "ita";
        public const string ITAHasDates = $"{ITAPrefix}.hasdate";
        public const string ITABefore2019 = $"{ITAPrefix}.before-2019";
        public const string ITAApprovedStartDate = $"{ITAPrefix}.approved-start-date";
        public const string ITAApprovedEndDate = $"{ITAPrefix}.approved-end-date";

        // Legal Entity Keys
        private const string LegalEntityPrefix = "legal-entity-data";
        public const string CompanyName = $"{LegalEntityPrefix}.name";
        public const string CompanyCode = $"{LegalEntityPrefix}.code";
        public const string CompanyIncorporationNumber = $"{LegalEntityPrefix}.incorporationNr";
        public const string CompanyMasterClientCode = $"{LegalEntityPrefix}.masterClientCode";
        public const string CompanyReferralOffice = $"{LegalEntityPrefix}.referralOffice";
        public const string CompanyStrSubmissionFee = $"{LegalEntityPrefix}.strSubmissionFee";
        public const string CompanyStrSubmissionLatePaymentFeeExempt = $"{LegalEntityPrefix}.strSubmissionLatePaymentFeeExempt";
        public const string CompanyIsActive = $"{LegalEntityPrefix}.isActive";

        // Address of Head Office Keys
        private const string HeadOfficePrefix = "address-of-head-office";
        public const string HeadOfficeAddress1 = $"{HeadOfficePrefix}.address1";
        public const string HeadOfficeAddress2 = $"{HeadOfficePrefix}.address2";
        public const string HeadOfficeCity = $"{HeadOfficePrefix}.city";
        public const string HeadOfficeZipCode = $"{HeadOfficePrefix}.zipCode";
        public const string HeadOfficeCountry = $"{HeadOfficePrefix}.country";
        public const string HeadOfficeIsAddressInNevisDifferent = $"{HeadOfficePrefix}.isAddressInNevisDifferent";
        public const string HeadOfficeCompanyClassification = $"{HeadOfficePrefix}.companyClassification";
        public const string HeadOfficeNevisAddress1 = $"{HeadOfficePrefix}.nevisAddress1";
        public const string HeadOfficeNevisAddress2 = $"{HeadOfficePrefix}.nevisAddress2";
        public const string HeadOfficeNevisCity = $"{HeadOfficePrefix}.nevisCity";
        public const string HeadOfficeNevisZipCode = $"{HeadOfficePrefix}.nevisZipCode";
        public const string HeadOfficeNevisCountry = $"{HeadOfficePrefix}.nevisCountry";

        // Contact Information Keys
        private const string ContactInformationPrefix = "contact-information";
        public const string ContactName = $"{ContactInformationPrefix}.name";
        public const string ContactPosition = $"{ContactInformationPrefix}.position";
        public const string ContactAddress1 = $"{ContactInformationPrefix}.address1";
        public const string ContactAddress2 = $"{ContactInformationPrefix}.address2";
        public const string ContactZipCode = $"{ContactInformationPrefix}.zipCode";
        public const string ContactCountry = $"{ContactInformationPrefix}.country";
        public const string ContactCity = $"{ContactInformationPrefix}.city";
        public const string ContactTelephoneNumber = $"{ContactInformationPrefix}.telephone.number";
        public const string ContactTelephoneCountryCode = $"{ContactInformationPrefix}.telephone.countryCode";
        public const string ContactTelephonePrefix = $"{ContactInformationPrefix}.telephone.prefix";
        public const string ContactFaxNumber = $"{ContactInformationPrefix}.fax.number";
        public const string ContactFaxCountryCode = $"{ContactInformationPrefix}.fax.countryCode";
        public const string ContactFaxPrefix = $"{ContactInformationPrefix}.fax.prefix";
        public const string ContactEmail = $"{ContactInformationPrefix}.email";
        public const string CompanyRepresentativeName = $"{ContactInformationPrefix}.companyRepresentativeName";
        public const string CompanyRepresentativeTelephoneNumber = $"{ContactInformationPrefix}.companyRepresentativeTelephone.number";
        public const string CompanyRepresentativeTelephoneCountryCode = $"{ContactInformationPrefix}.companyRepresentativeTelephone.countryCode";
        public const string CompanyRepresentativeTelephonePrefix = $"{ContactInformationPrefix}.companyRepresentativeTelephone.prefix";
        public const string CompanyRepresentativeFaxNumber = $"{ContactInformationPrefix}.companyRepresentativeFax.number";
        public const string CompanyRepresentativeFaxCountryCode = $"{ContactInformationPrefix}.companyRepresentativeFax.countryCode";
        public const string CompanyRepresentativeFaxPrefix = $"{ContactInformationPrefix}.companyRepresentativeFax.prefix";
        public const string CompanyRepresentativeEmail = $"{ContactInformationPrefix}.companyRepresentativeEmail";

        // Tax Resident Keys
        private const string TaxResidentPrefix = "tax-resident";
        public const string TaxResidentIncorporatedBefore2019 = $"{TaxResidentPrefix}.incorporatedBefore2019";
        public const string TaxResidentNonTaxResident = $"{TaxResidentPrefix}.nonTaxResident";
        public const string TaxResidentResidentCountry = $"{TaxResidentPrefix}.residentCountry";

        // Intellectual Properties Keys
        private const string IntellectualPropertiesPrefix = "intellectual-properties";
        public const string IntellectualPropertiesAcquired = $"{IntellectualPropertiesPrefix}.intellectualPropertyAcquired";
        private const string IntellectualPropertiesAssetsAcquiredPrefix = $"{IntellectualPropertiesPrefix}.assetsAcquired";

        private const string FinancialPeriodPrefix = "financial-period";
        public const string FinancialPeriodStartDate = $"{FinancialPeriodPrefix}.startDate";
        public const string FinancialPeriodEndDate = $"{FinancialPeriodPrefix}.endDate";
       
        public static string IntellectualPropertiesAssetsAcquiredDate(int index) => $"{IntellectualPropertiesAssetsAcquiredPrefix}.{index}.acquisitionDate";

        public static string IntellectualPropertiesAssetsAcquiredDescription(int index) => $"{IntellectualPropertiesAssetsAcquiredPrefix}.{index}.description";

        public static string IntellectualPropertiesAssetsAcquiredIncome(int index) => $"{IntellectualPropertiesAssetsAcquiredPrefix}.{index}.income";

        // Corporate Accounting Records Keys
        private const string CorporateAccountingPrefix = "corporate-accounting-records";
        public const string CorporateAccountingAssessableIncomeGenerated = $"{CorporateAccountingPrefix}.assessableIncomeGenerated";
        public const string CorporateAccountingActivitiesCondition = $"{CorporateAccountingPrefix}.activitiesCondition";

        public static string CorporateAccountingActivitiesDescription(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.description";

        public static string CorporateAccountingActivitiesIncome(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.income";
        
        public static string CorporateAccountingActivitiesYearIncome(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.incomeYear";

        public static string CorporateAccountingActivitiesRelatedPartyIntellectualProperty(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.relatedPartyIntellectualProperty";

        public static string CorporateAccountingActivitiesNonRelatedIntellectualProperty(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.nonRelatedIntellectualProperty";

        public static string CorporateAccountingActivitiesNonIntellectualProperty(int index) => $"{CorporateAccountingPrefix}.accountingActivities.{index}.nonIntellectualProperty";


        // Corporate Multinational Enterprise Keys
        private const string CorporateMNEPrefix = "corporate-multinational-enterprise";
        public const string CorporateMNEIsPartOfMNEGroup = $"{CorporateMNEPrefix}.isPartOfMNEGroup";
        public const string CorporateMNERequiresCbCReport = $"{CorporateMNEPrefix}.requiresCbCReport";

        // Finalize Keys
        private const string FinalizePrefix = "finalize";
        public const string FinalizeConfirmationTrueInformation = $"{FinalizePrefix}.confirmationTrueInformation";
        public const string FinalizeConfirmationUnderstand = $"{FinalizePrefix}.confirmationUnderstand";
        public const string FinalizeConfirmationAwarePerjury = $"{FinalizePrefix}.confirmationAwarePerjury";
        public const string FinalizeDateOfSignature = $"{FinalizePrefix}.dateOfSignature";
        public const string FinalizeAddressOfPersonDeclaring = $"{FinalizePrefix}.addressOfPersonDeclaring";
        public const string FinalizeAddressOfPersonDeclaring2 = $"{FinalizePrefix}.addressOfPersonDeclaring2";
        public const string FinalizeCity = $"{FinalizePrefix}.city";
        public const string FinalizeZipCode = $"{FinalizePrefix}.zipCode";
        public const string FinalizeCountry = $"{FinalizePrefix}.country";
        public const string FinalizeNameOfPersonDeclaring = $"{FinalizePrefix}.nameOfPersonDeclaring";
        public const string FinalizeOnMyOwnBehalf = $"{FinalizePrefix}.onMyOwnBehalf";
        public const string FinalizeAsOfficer = $"{FinalizePrefix}.asOfficer";
        public const string FinalizeAsAttorney = $"{FinalizePrefix}.asAttorney";
        public const string FinalizeAsTrustee = $"{FinalizePrefix}.asTrustee";

        // Corporate Address Keys
        private const string CorporateAddressPrefix = "corporate-address";
        public const string CorporateAddressRecordsKeptAtRegisteredOffice = $"{CorporateAddressPrefix}.recordsKeptAtRegisteredOffice";

        // Business Activities Keys
        private const string BusinessActivitiesPrefix = "business-activities";
        public const string BusinessActivitiesFirstActivityFrom = $"{BusinessActivitiesPrefix}.activities.0.from";
        public const string BusinessActivitiesFirstActivityTo = $"{BusinessActivitiesPrefix}.activities.0.to";
        public const string BusinessActivitiesFirstActivityType = $"{BusinessActivitiesPrefix}.activities.0.type";
        public const string BusinessActivitiesFirstActivity = $"{BusinessActivitiesPrefix}.activities.0.activity";

        public const string RelevantActivitiesPrefix = "relevant-activity-declaration.relevantActivities";
        public const string RelevantActivitiesRegexPrefix = @"relevant-activity-declaration\.relevantActivities";

        public static string RelevantActivitiesIsSelected(int index) => $"{RelevantActivitiesPrefix}.{index}.selected";

        public static string RelevantActivitiesIsPartialFinancialPeriod(int index) => $"{RelevantActivitiesPrefix}.{index}.carriedOnForOnlyPartOfFinancialPeriod";

        public static string RelevantActivitiesStartDate(int index) => $"{RelevantActivitiesPrefix}.{index}.startDate";

        public static string RelevantActivitiesEndDate(int index) => $"{RelevantActivitiesPrefix}.{index}.endDate";

        public static string BusinessActivitiesType(int index) => $"{BusinessActivitiesPrefix}.activities.{index}.type";

        public static string BusinessActivitiesActivity(int index) => $"{BusinessActivitiesPrefix}.activities.{index}.activity";

        public static string BusinessActivitiesOtherActivity(int index) => $"{BusinessActivitiesPrefix}.activities.{index}.otherActivity";
#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}