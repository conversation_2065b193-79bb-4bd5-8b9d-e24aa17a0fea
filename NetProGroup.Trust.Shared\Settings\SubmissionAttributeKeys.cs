﻿namespace NetProGroup.Trust.Domain.Shared.Settings
{
    /// <summary>
    /// Represents the various keys for document settings.
    /// </summary>
    public static class SubmissionAttributeKeys
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented

        public const string CreatedByEmail = "submissions.created-by-email";
        public const string SubmittedByEmail = "submissions.submitted-by-email";
        public const string ExportedByEmail = "submissions.exported-by-email";
        public const string ReopenRequestComments = "submissions.reopen-request.comments";
        public const string InitialSubmittedAt = "submissions.initial-submitted-date";

#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member

    }
}
