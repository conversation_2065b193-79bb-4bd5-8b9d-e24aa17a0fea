using System.Globalization;

namespace NetProGroup.Trust.Domain.Shared.Utilities;

public abstract class Formatter
{
    /// <summary>
    /// Formats an amount to 2 decimals with the correct decimal point (a dot)
    /// </summary>
    /// <param name="amount"></param>
    /// <returns></returns>
    public static string FormatAmount(decimal amount)
    {
        var savedCulture = Thread.CurrentThread.CurrentCulture;
        Thread.CurrentThread.CurrentCulture = CultureInfo.GetCultureInfo("en-US");

        try
        {
            return amount.ToString("0.00");
        }
        finally
        {
            Thread.CurrentThread.CurrentCulture = savedCulture;
        }

    }
}