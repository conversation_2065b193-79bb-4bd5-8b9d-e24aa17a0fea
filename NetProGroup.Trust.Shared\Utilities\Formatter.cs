// <copyright file="Formatter.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Globalization;

namespace NetProGroup.Trust.Domain.Shared.Utilities;

/// <summary>
/// String format helper methods.
/// </summary>
public abstract class Formatter
{
    /// <summary>
    /// Formats an amount to 2 decimals with the correct decimal point (a dot).
    /// </summary>
    /// <param name="amount">Amount to format.</param>
    /// <returns>A decimal number string with two decimal points.</returns>
    public static string FormatAmount(decimal amount)
    {
        var savedCulture = Thread.CurrentThread.CurrentCulture;
        Thread.CurrentThread.CurrentCulture = CultureInfo.GetCultureInfo("en-US");

        try
        {
            return amount.ToString("0.00");
        }
        finally
        {
            Thread.CurrentThread.CurrentCulture = savedCulture;
        }
    }
}