using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Documents;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Documents
{
    public class DocumentsAppServiceTests : TestBase
    {

        private IDocumentsAppService _documentsAppService;
        private Guid _documentId;
        private string baseFilePath;

        [SetUp]
        public async Task Setup()
        {
            _documentsAppService = _server.Services.GetRequiredService<IDocumentsAppService>();

            baseFilePath = $"App_Data/TestFiles/Documents/Test file.pdf";

            SetWorkContextUser(ManagementUser);

            // Create a test document
            await CreateTestDocumentAsync();
        }

        [Test]
        public async Task CreateDocumentAsync_Success()
        {
            // Arrange
            using (var stream = System.IO.File.OpenRead(baseFilePath))
            {

                // Generate image file
                IFormFile testDocument = new FormFile(stream, 0, stream.Length, null, Path.GetFileName(stream.Name))
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "application/pdf"
                };

                // Upload applicant photo
                var data = new CreateDocumentDTO()
                {
                    File = testDocument,
                    Type = Trust.Shared.Enums.DocumentType.Pdf,
                    Description = "Test file"
                };

                _documentId = await _documentsAppService.CreateDocumentAsync(data);

                // Act/Assert

                Assert.That(_documentId, Is.Not.EqualTo(Guid.Empty));
            }
        }

        [Test]
        public async Task CreateDocumentAsync_Error_NullData()
        {
            // Try to create a document with null data.
            Func<Task> action = () => _documentsAppService.CreateDocumentAsync(null);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentNullException>();
        }

        [Test]
        public async Task CreateDocumentAsync_Error_NullFile()
        {
            // Arrange

            // Upload document with null file
            var data = new CreateDocumentDTO()
            {
                File = null,
                Type = Trust.Shared.Enums.DocumentType.Pdf,
                Description = "Test file"
            };

            Func<Task> action = () => _documentsAppService.CreateDocumentAsync(data);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentNullException>();
        }

        [Test]
        public async Task GetDocumentByIdAsync_Success()
        {
            // Arrange
            var document = await _documentsAppService.GetDocumentByIdAsync(_documentId, false);

            // Act/Assert

            Assert.That(document.Description, Is.EqualTo("Test file"));
        }

        [Test]
        public async Task GetDocumentByIdAsync_Error_Empty_DocumentId()
        {
            // Arrange
            try
            {
                // Try to retrieve a document with an empty id.
                await _documentsAppService.GetDocumentByIdAsync(Guid.Empty, false);

            }
            catch (NotFoundException)
            {
                // Act/Assert
                Assert.Pass();
            }
        }

        [Test]
        public async Task GetDocumentByIdAsync_Error_Invalid_DocumentId()
        {
            // Arrange
            try
            {
                // Try to retrieve a document with an invalid id.
                await _documentsAppService.GetDocumentByIdAsync(Guid.NewGuid(), false);

            }
            catch (NotFoundException)
            {
                // Act/Assert
                Assert.Pass();
            }
        }

        [Test]
        public async Task GetDocumentsByIdAsync_Success()
        {
            // Arrange
            // Create an additional document
            var documentId2 = Guid.Empty;
            using (var stream = System.IO.File.OpenRead(baseFilePath))
            {

                // Generate image file
                IFormFile testDocument = new FormFile(stream, 0, stream.Length, null, Path.GetFileName(stream.Name))
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "application/pdf"
                };

                // Upload applicant photo
                var data = new CreateDocumentDTO()
                {
                    File = testDocument,
                    Type = Trust.Shared.Enums.DocumentType.Pdf,
                    Description = "Test file 2"
                };

                documentId2 = await _documentsAppService.CreateDocumentAsync(data);
            }

            // Setup the list of document ids.
            var documentIds = new List<Guid> { _documentId, documentId2 };

            var documents = await _documentsAppService.GetDocumentsByIdAsync(documentIds, false);

            // Act/Assert

            Assert.That(documents.Count, Is.EqualTo(2));
        }

        [Test]
        public async Task GetDocumentsByIdAsync_Error_Null_List()
        {
            // Arrange
            Func<Task> action = () => _documentsAppService.GetDocumentsByIdAsync(null, false);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentNullException>();
        }

        #region Setup

        /// <summary>
        /// Create a test submission
        /// </summary>
        private async Task CreateTestDocumentAsync()
        {
            // Create an test document
            using (var stream = System.IO.File.OpenRead(baseFilePath))
            {

                // Generate pdf file
                IFormFile testDocument = new FormFile(stream, 0, stream.Length, null, Path.GetFileName(stream.Name))
                {
                    Headers = new HeaderDictionary(),
                    ContentType = "application/pdf"
                };

                // Upload test document
                var data = new CreateDocumentDTO()
                {
                    File = testDocument,
                    Type = Trust.Shared.Enums.DocumentType.Pdf,
                    Description = "Test file"
                };

                _documentId = await _documentsAppService.CreateDocumentAsync(data);
            }
        }

        #endregion

    }
}