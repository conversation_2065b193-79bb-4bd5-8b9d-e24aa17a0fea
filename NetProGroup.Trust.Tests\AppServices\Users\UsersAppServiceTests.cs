﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Users
{
    public class UsersAppServiceTests : TestBase
    {
        private IUsersAppService _usersAppService;
        private IActivityLogRepository _activityLogRepository;

        [SetUp]
        public void Setup()
        {
            _usersAppService = _server.Services.GetRequiredService<IUsersAppService>();
            _activityLogRepository = _server.Services.GetRequiredService<IActivityLogRepository>();
        }

        [Test]
        public async Task AcceptTermsConditionsAsync_ShouldSetCorrectTimeInActivityLog()
        {
            // Arrange
            SetWorkContextUser(ClientUser);
            var expectedDateTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm");

            // Act
            await _usersAppService.AcceptTermsConditionsAsync(ClientUser.Id, new AcceptTermsConditionsDTO
            {
                Version = "1.0"
            });

            // Assert
            var result = await _activityLogRepository.FindByConditionAsync(
                log => log.ActivityType == ActivityLogActivityTypes.UserTermsConditionsAccepted &&
                       log.CreatedByIdentityUserId == ClientUser.Id &&
                       log.Text.Contains(expectedDateTime));

            result.Should().NotBeNull();
            var acceptedTermsLog = result.FirstOrDefault();
            acceptedTermsLog.Should().NotBeNull();
            acceptedTermsLog.Text.Should().Contain($"{expectedDateTime} (UTC)");
        }
    }
}
