﻿using FluentAssertions;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.Domain.Shared.Defines;
using System.Reflection;

namespace NetProGroup.Trust.Tests.Auditing
{
    [TestFixture()]
    public class ActivityTypeDescriptionMapperTests
    {
        [Test()]
        public void GetActionDescriptionFromActivityTypeTest()
        {
            var type = typeof(ActivityLogActivityTypes);
            
            // get all of the const string members of the ActivityLogActivityTypes class and check if they are in the dictionary
            var members = type.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);
            foreach (var member in members)
            {
                var value = member.GetValue(null);
                var description = ActivityTypeDescriptionMapper.GetActionDescriptionFromActivityType(value.ToString());
                description.Should().NotContain("Unsupported", "Every type should have a matching description");
            }
        }
    }
}