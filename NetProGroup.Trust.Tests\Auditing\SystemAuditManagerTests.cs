﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.ActivityLogs.EFRepository;
using NetProGroup.Framework.Services.EFAuditing;
using NetProGroup.Trust.Application.Contracts.Audits;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Auditing
{
    [TestFixture]
    public class SystemAuditManagerTests : TestBase
    {
        private ISystemAuditManager _systemAuditManager;
        private IActivityLogRepository _activityLogRepository;
        private IActivityLogsAppService _activityLogsAppService;
        private IUsersAppService _usersAppService;
        private Period _defaultPeriod = new Period { StartDate = DateTime.UtcNow.AddDays(-7), EndDate = DateTime.UtcNow };

        [SetUp]
        public void Setup()
        {
            _systemAuditManager = _server.Services.GetRequiredService<ISystemAuditManager>();
            _activityLogRepository = _server.Services.GetRequiredService<IActivityLogRepository>();
            _server.Services.GetRequiredService<IAuditService>();
            _server.Services.GetRequiredService<ISystemAuditItemBuilder>();
            _activityLogsAppService = _server.Services.GetService<IActivityLogsAppService>();
            _usersAppService = _server.Services.GetService<IUsersAppService>();
        }

        /// <summary>
        ///     Tests that ListActivityLogsAsync returns expected results when given valid request parameters.
        /// </summary>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        [Test]
        public async Task ListActivityLogsAsync_WithValidRequest_ReturnsExpectedResults()
        {
            // Arrange
            List<ActivityLog> activityLogs = [CreateTestActivityLog("Test activity", "Test activity details")];

            await _activityLogRepository.InsertAsync(activityLogs);
            await _activityLogRepository.SaveChangesAsync();

            ListActivityLogRequest request = new()
            {
                Period = _defaultPeriod
            };

            // Act
            var result = await _systemAuditManager.ListActivityLogsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.ActivityLogItems.Should().NotBeNull();
            result.ActivityLogItems.Should().HaveCount(2);

            var firstItem = result.ActivityLogItems[0];
            firstItem.ActivityType.Should().Be(ActivityLogActivityTypes.BeneficialOwnerAssistanceRequested);
            firstItem.ShortDescription.Should().Be("Test activity");
            firstItem.Text.Should().Be("Test activity details");
        }

        /// <summary>
        ///     Tests that ListActivityLogsAsync correctly filters results by ActivityLogId when provided.
        /// </summary>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        [Test]
        public async Task ListActivityLogsAsync_WithActivityLogId_ReturnsFilteredResults()
        {
            // Arrange
            var activityLogs = new[]
            {
                CreateTestActivityLog("Test activity 1", "Test activity details 1"),
                CreateTestActivityLog("Test activity 2", "Test activity details 2")
            };

            await _activityLogRepository.InsertAsync(activityLogs);
            await _activityLogRepository.SaveChangesAsync();
            var activityLogId = activityLogs[0].Id;

            ListActivityLogRequest request = new()
            {
                Period = _defaultPeriod,
                ActivityLogId = activityLogId
            };

            // Act
            var result = await _systemAuditManager.ListActivityLogsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.ActivityLogItems.Should().NotBeNull();
            result.ActivityLogItems.Should().HaveCount(1);
            result.ActivityLogItems[0].Id.Should().Be(activityLogId);
            result.ActivityLogItems[0].ShortDescription.Should().Be("Test activity 1");
        }

        /// <summary>
        ///     Tests that ListActivityLogsAsync correctly filters results by EntityId when provided.
        /// </summary>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        [Test]
        public async Task ListActivityLogsAsync_WithEntityId_ReturnsFilteredResults()
        {
            // Arrange
            var entityId = Guid.NewGuid();
            var activityLog1 = CreateTestActivityLog("Entity activity 1", "Entity activity details 1");
            activityLog1.EntityId = entityId;

            var activityLog2 = CreateTestActivityLog("Entity activity 2", "Entity activity details 2");

            await _activityLogRepository.InsertAsync(new[] { activityLog1, activityLog2 });
            await _activityLogRepository.SaveChangesAsync();

            ListActivityLogRequest request = new()
            {
                Period = _defaultPeriod,
                EntityId = entityId
            };

            // Act
            var result = await _systemAuditManager.ListActivityLogsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.ActivityLogItems.Should().NotBeNull();
            result.ActivityLogItems.Should().HaveCount(1);
            result.ActivityLogItems[0].ShortDescription.Should().Be("Entity activity 1");
        }

        /// <summary>
        ///     Tests that ListActivityLogsAsync correctly filters results by date range.
        /// </summary>
        /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
        [Test]
        public async Task ListActivityLogsAsync_WithDateRange_ReturnsFilteredResults()
        {
            // Arrange
            var now = DateTime.UtcNow;
            var recentLog = CreateTestActivityLog("Recent activity", "Recent activity details");
            recentLog.CreatedAt = now.AddDays(-5);

            var oldLog = CreateTestActivityLog("Old activity", "Old activity details");
            oldLog.CreatedAt = now.AddDays(-15);

            await _activityLogRepository.InsertAsync(new[] { recentLog, oldLog });
            await _activityLogRepository.SaveChangesAsync();

            ListActivityLogRequest request = new()
            {
                Period = new Period { StartDate = now.AddDays(-7), EndDate = now },
            };

            // Act
            var result = await _systemAuditManager.ListActivityLogsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.ActivityLogItems.Should().NotBeNull();
            result.ActivityLogItems.Should().HaveCount(2);
            result.ActivityLogItems[1].ShortDescription.Should().Be("Recent activity");
        }

        private static ActivityLog CreateTestActivityLog(string shortDescription, string testActivityDetails)
        {
            return new ActivityLog
            {
                CreatedAt = DateTime.UtcNow,
                ActivityType = ActivityLogActivityTypes.BeneficialOwnerAssistanceRequested,
                ShortDescription = shortDescription,
                Text = testActivityDetails,
                ContextId = Guid.NewGuid(),
                CreatedByIdentityUserId = Guid.NewGuid(),
                EntityName = "Test entity",
                EntityId = Guid.NewGuid()
            };
        }
    }
}