﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Announcements : TestBase
    {

        private IInboxDataManager _inboxDataManager;
        private IAnnouncementDataManager _announcementDataManager;

        [SetUp]
        public void Setup()
        {
            _announcementDataManager = _server.Services.GetRequiredService<IAnnouncementDataManager>();
            _inboxDataManager = _server.Services.GetRequiredService<IInboxDataManager>();
        }

        [Test]
        public async Task CreateUpdateAnnouncementAsync_InboxMessage_MarkAsRead()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = true,
                SendAt = DateTime.UtcNow.AddMinutes(1),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            //Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Act
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow);

            //Get the inbox messages status 
            var readMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            // Assert
            readMessage.IsRead.Should().BeTrue("The inbox message should be marked as read after creating the read status.");
        }

        [Test]
        public async Task UpdateAnnouncement_ShouldNotChangeStatusFromScheduledToDraft()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Scheduled Announcement",
                EmailSubject = "Scheduled Email Subject",
                Body = "This is a scheduled announcement body.",
                IncludeAttachments = false,
                SendNow = false,
                SendAt = DateTime.UtcNow.AddMinutes(10),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            var announcementId = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);
            var announcement = (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x => x.Id == announcementId);
            var originalStatus = announcement.Status;
            originalStatus.Should().Be(AnnouncementStatus.Scheduled);

            // Update the announcement with changes (should not revert to Draft)
            createDto.Id = announcementId;
            createDto.Subject = "Updated Scheduled Announcement";
            createDto.Body = "This is an updated scheduled announcement body.";

            // Act
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Assert
            var updatedAnnouncement = (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x => x.Id == announcementId);
            updatedAnnouncement.Status.Should().Be(originalStatus, "Status should not change from Scheduled to Draft when updating announcement.");
        }

    }
}
