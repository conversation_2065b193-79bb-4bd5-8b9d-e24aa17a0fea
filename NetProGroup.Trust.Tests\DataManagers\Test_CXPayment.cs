using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Services;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    [TestFixture]
    public class Test_CXPayment : TestBase
    {
        private ICxPaymentService _paymentService;

        [SetUp]
        public void Setup()
        {
            _paymentService = _server.Services.GetRequiredService<ICxPaymentService>();
        }

        [Test]
        public void ProcessRequest_ShouldReturn_ValidResponse_OnValidRequest()
        {
            // Arrange

            var paymentRequest = new PaymentRequest
            {
                RedirectUrl = "https://valid-url.com",
                Amount = 100.50M,
                OrderId = "78fcb820-4ba6-4383-a07e-a798658ad020",
                Email = "<EMAIL>",
                Company = "Test Company",
                PaymentGatewaytUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                ApiKey = "2F822Rw39fx762MaV7Yy86jXGTC7sCDy",
            };

            // Act
            var response = _paymentService.ProcessRequest(paymentRequest);

            // Assert
            Assert.NotNull(response);
            Assert.NotNull(response.Transaction);
            Assert.AreEqual("Step 1 completed", response.ResultMessage);  // Assuming success based on response text
        }

        [Test]
        public void ProcessRequest_ReturnsErrorResponse_OnInvalidAmount()
        {
            // Arrange
            var paymentRequest = new PaymentRequest
            {
                RedirectUrl = "https://valid-url.com",
                Amount = -5.00M,  // Invalid amount
                OrderId = "78fcb820-4ba6-4383-a07e-a798658ad020",
                Email = "<EMAIL>",
                Company = "Test Company",
                PaymentGatewaytUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
            };

            // Act
            var response = _paymentService.ProcessRequest(paymentRequest);

            // Assert
            Assert.NotNull(response);
            Assert.AreEqual(3, response.ResultCode);
        }

        [Test]
        public void CompleteRequest_ThrowsException_OnInvalidToken()
        {
            // Arrange
            var paymentCompleteRequest = new PaymentComplete
            {
                ApiGatewayUrl = "https://cxpay.transactiongateway.com/api/v2/three-step",
                IdTransaction = "78fcb820-4ba6-4383-a07e-a798658ad020",
                Token = "invalid-token"  // Invalid token
            };

            // Act
            var response = _paymentService.CompleteRequest(paymentCompleteRequest);

            // Assert
            Assert.NotNull(response);
            Assert.AreEqual(3, response.ResultCode);
        }

        [Test]
        public void CompleteRequest_ThrowsApplicationException_OnNonExistentTransaction()
        {
            // Arrange
            var paymentCompleteRequest = new PaymentComplete
            {
                IdTransaction = Guid.NewGuid().ToString(),  // Non-existent transaction
                Token = "valid-token"
            };

            // Act & Assert
            var exception = Assert.Throws<ApplicationException>(() =>
            {
                _paymentService.CompleteRequest(paymentCompleteRequest);
            });

            // Assert
            Assert.NotNull(exception);
            Assert.AreEqual("No transaction found with transaction id: " + paymentCompleteRequest.IdTransaction, exception.Message);
        }


        [Test]
        public void ProcessRequest_ThrowsException_OnNullPaymentRequest()
        {
            // Arrange, Act & Assert
            Assert.Throws<ArgumentNullException>(() => _paymentService.ProcessRequest(null));
        }

        [Test]
        public void CompleteRequest_ThrowsException_OnNullCompleteRequest()
        {
            // Arrange,Act & Assert
            Assert.Throws<ArgumentNullException>(() => _paymentService.CompleteRequest(null));
        }
    }

}