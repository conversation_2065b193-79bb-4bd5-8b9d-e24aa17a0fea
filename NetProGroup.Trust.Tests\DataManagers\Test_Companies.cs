﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Companies : TestBase
    {

        private ILegalEntitiesDataManager _legalEntitiesDataManager;
        private IJurisdictionsRepository _jurisdictionsRepository;

        private readonly Guid _jurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9");

        [SetUp]
        public async Task Setup()
        {
            _legalEntitiesDataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();

            var companyA = await _legalEntitiesDataManager.CreateCompanyAsync(new CreateCompanyDTO { MasterClientId = _masterClient.Id, Name = "Company A", Code = "A", JurisdictionId = _jurisdictionId }, saveChanges: true);
            var legalEntityA = await legalEntitiesRepository.GetByIdAsync(companyA.Id);
            legalEntityA.EntityTypeName = LegalEntityTypes.IBC;
            await legalEntitiesRepository.UpdateAsync(legalEntityA, saveChanges: true);

            var companyB = await _legalEntitiesDataManager.CreateCompanyAsync(new CreateCompanyDTO { MasterClientId = _masterClient.Id, Name = "Company B", Code = "B", JurisdictionId = _jurisdictionId }, saveChanges: true);
            var legalEntityB = await legalEntitiesRepository.GetByIdAsync(companyB.Id);
            legalEntityB.EntityTypeName = LegalEntityTypes.LLC;
            await legalEntitiesRepository.UpdateAsync(legalEntityB, saveChanges: true);

            var companyC = await _legalEntitiesDataManager.CreateCompanyAsync(new CreateCompanyDTO { MasterClientId = _masterClient.Id, Name = "Company C", Code = "C", JurisdictionId = _jurisdictionId }, saveChanges: true);
            var legalEntityC = await legalEntitiesRepository.GetByIdAsync(companyC.Id);
            legalEntityC.EntityTypeName = "Trust";
            await legalEntitiesRepository.UpdateAsync(legalEntityC, saveChanges: true);
        }

        /// <summary>
        /// Get company using an invalid masterclient.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_By_Invalid_MasterClient()
        {
            // Arrange
            var request = new ListCompaniesRequest() { MasterClientId = Guid.Empty, AuthorizedJurisdictionIDs = _jurisdictionsRepository.FindAll().Select(j => j.Id).ToList() };

            // Act
            var response = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.CompanyItems, Is.Not.Null);
            Assert.That(response.CompanyItems, Has.Count.EqualTo(0));
        }

        /// <summary>
        /// Get company using a valid masterclient.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_By_Valid_MasterClient()
        {
            // Arrange
            var request = new ListCompaniesRequest()
            {
                MasterClientId = _masterClient.Id,
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };

            // Act
            var response = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.CompanyItems, Is.Not.Null);
            Assert.That(response.CompanyItems, Has.Count.EqualTo(2));
        }

        /// <summary>
        /// Get all companies.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_All()
        {
            // Arrange
            var request = new ListCompaniesRequest()
            {
                AuthorizedJurisdictionIDs = _jurisdictionsRepository.GetQueryable().Select(j => j.Id).ToList()
            };

            // Act
            var response = await _legalEntitiesDataManager.ListCompaniesAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.CompanyItems, Is.Not.Null);
            Assert.That(response.CompanyItems, Has.Count.EqualTo(2));
        }
    }
}
