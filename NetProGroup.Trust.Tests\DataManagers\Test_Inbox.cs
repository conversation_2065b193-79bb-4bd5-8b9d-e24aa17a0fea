﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Inbox : TestBase
    {
        private IInboxDataManager _inboxDataManager;
        private IInboxReadStatusesRepository _inboxReadStatusRepository;
        private IAnnouncementDataManager _announcementDataManager;
        private IWorkContext _workContext;

        [SetUp]
        public void Setup()
        {
            _announcementDataManager = _server.Services.GetRequiredService<IAnnouncementDataManager>();
            _inboxDataManager = _server.Services.GetRequiredService<IInboxDataManager>();
            _inboxReadStatusRepository = _server.Services.GetRequiredService<IInboxReadStatusesRepository>();
            _workContext = _server.Services.GetRequiredService<IWorkContext>();
        }

        [Test]
        public async Task CreateInboxReadStatusIfNotExistsAsync_DoesNotDuplicateReadStatus()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement Duplicate ReadStatus",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = true,
                SendAt = DateTime.UtcNow.AddMinutes(1),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            // Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Act - first call
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow);

            // Get all read messages for the user
            var readMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            var readStatuses = await _inboxReadStatusRepository.FindByConditionAsync(status => status.UserId == ClientUser.Id);
            var readStatusBefore1 = readStatuses.Should().ContainSingle().Which;

            // There should be only one read status for this message and user
            // (Assuming the system only allows one read status per message/user, so IsRead is true and ReadAt is not overwritten)
            var readAt1 = readMessage.ReadAt;

            // Call again and check that ReadAt is not changed.
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow.AddMinutes(2));

            var readMessageAfter = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            var readStatusesAfter = await _inboxReadStatusRepository.FindByConditionAsync(status => status.UserId == ClientUser.Id);

            // Assert
            readMessage.IsRead.Should().BeTrue("The message should be marked as read.");
            readMessageAfter.ReadAt.Should().Be(readAt1, "ReadAt should not be updated on duplicate call.");
            readStatusesAfter.Should().ContainSingle("No additional read status should be created if one already exists.")
                .Which.Id.Should().Be(readStatusBefore1.Id, "The read status should not be replaced.");
        }

        [Test]
        public async Task GetInboxMessagesAsync_ShouldReturnCorrectIsReadAndReadAt()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement Get All",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = true,
                SendAt = DateTime.UtcNow.AddMinutes(1),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            // Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Mark as read
            var readAt = DateTime.UtcNow;
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, readAt);

            // Act
            var readMessages = await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true);
            var readMessage = readMessages.Single();

            // Assert
            readMessage.IsRead.Should().BeTrue("The message should be marked as read.");
            readMessage.ReadAt.Should().Be(readAt.ToTCILocalTime(), "ReadAt should match the time it was marked as read.");
        }

        [Test]
        public async Task GetInboxMessageAsync_ShouldReturnCorrectIsReadAndReadAt()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement Single Message Read Status",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = true,
                SendAt = DateTime.UtcNow.AddMinutes(1),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };


            // Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Mark as read
            var readAt = DateTime.UtcNow;
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, readAt);

            // Set the work context for the current user
            _workContext.IdentityUserId = ClientUser.Id;

            // Act
            var inboxMessage = await _inboxDataManager.GetInboxMessageAsync(unreadInboxMessage.Id);

            // Assert
            inboxMessage.Should().NotBeNull("The inbox message should be returned.");
            inboxMessage.IsRead.Should().BeTrue("The message should be marked as read.");
            inboxMessage.ReadAt.Should().Be(readAt.ToTCILocalTime(), "ReadAt should match the time it was marked as read.");
        }
    }
}
