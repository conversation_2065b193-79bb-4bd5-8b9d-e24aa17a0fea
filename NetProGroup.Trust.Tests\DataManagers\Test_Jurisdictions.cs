﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.DataManager.Jurisdictions;
using NetProGroup.Trust.DataManager.Jurisdictions.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Jurisdictions : TestBase
    {
        private IJurisdictionsRepository _jurisdictionRepository;
        private IJurisdictionsDataManager _jurisdictionsDataManager;

        [SetUp]
        public void Setup()
        {
            _jurisdictionRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            _jurisdictionsDataManager = _server.Services.GetRequiredService<IJurisdictionsDataManager>();
        }

        [Test]
        public async Task List_Returns_Paged_Items()
        {
            // Arrange
            var request = new ListJurisdictionsRequest()
            {
                AuthorizedJurisdictionIDs = _jurisdictionRepository.FindAll().Select(j => j.Id).ToList()
            };

            // Act
            var response = await _jurisdictionsDataManager.ListJurisdictionsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.JurisdictionItems, Is.Not.Null);
            Assert.That(response.JurisdictionItems.Count, Is.EqualTo(4));
        }
    }
}
