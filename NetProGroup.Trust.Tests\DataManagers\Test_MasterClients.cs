﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.DataManager.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_MasterClients : TestBase
    {
        private IMasterClientsRepository _masterClientRepository;
        private IMasterClientsDataManager _masterClientsDataManager;
        private ILegalEntitiesDataManager _legalEntitiesDataManager;

        private Guid _jurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9");
        private IUserManager _userManager;
        private IUsersDataManager _usersDataManager;

        [SetUp]
        public async Task Setup()
        {
            _masterClientRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _masterClientsDataManager = _server.Services.GetRequiredService<IMasterClientsDataManager>();
            _legalEntitiesDataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            _usersDataManager = _server.Services.GetRequiredService<IUsersDataManager>();

            await CreateCompany("Company A", "A", _masterClient.Id);

        }

        /// <summary>
        /// Get master client using an invalid jurisdiction.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_By_Invalid_Jurisdiction()
        {
            // Arrange
            var request = new ListMasterClientsRequest() { JurisdictionId = Guid.Empty };

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MasterClientItems, Is.Not.Null);
            Assert.That(response.MasterClientItems.Count, Is.EqualTo(0));
        }

        /// <summary>
        /// Get master client using an invalid search term.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_By_Invalid_SearchTerm()
        {
            // Arrange
            var request = new ListMasterClientsRequest()
            {
                SearchTerm = "invalid"
            };

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MasterClientItems, Is.Not.Null);
            Assert.That(response.MasterClientItems.Count, Is.EqualTo(0));
        }

        /// <summary>
        /// Get master client using a valid jurisdiction.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_By_Valid_Jurisdiction()
        {
            // Arrange
            var request = new ListMasterClientsRequest() { JurisdictionId = _jurisdictionId };

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MasterClientItems, Is.Not.Null);
            Assert.That(response.MasterClientItems.Count, Is.EqualTo(1));
        }

        /// <summary>
        /// Get master client using a valid search term.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_By_Valid_SearchTerm()
        {
            // Arrange
            var request = new ListMasterClientsRequest()
            {
                SearchTerm = "TEST_123"
            };

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MasterClientItems, Is.Not.Null);
            Assert.That(response.MasterClientItems.Count, Is.EqualTo(1));
        }

        /// <summary>
        /// Get all master clients.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Returns_Paged_Items_All()
        {
            // Arrange
            var request = new ListMasterClientsRequest();

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MasterClientItems, Is.Not.Null);
            Assert.That(response.MasterClientItems.Count, Is.EqualTo(1));
        }

        /// <summary>
        /// Get all master clients and check user invitation details are mapped.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Includes_MasterClientUser_InvitationDetails()
        {
            // Arrange
            var request = new ListMasterClientsRequest()
            {
                //AuthorizedJurisdictionIDs =new { _jurisdictionId}
            };
            
            // Save the date that we sent the invitation
            var invitationDateTime = new DateTime(2023, 05, 22, 21, 0, 55, 0);
            await _usersDataManager.SetAttributeValueAsync(ClientUser.Id, UserAttributeKeys.Invitation_Last_Sent,
                invitationDateTime, true);

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            response.Should().NotBeNull();
            response.MasterClientItems.Should().NotBeNull();
            response.MasterClientItems.Count.Should().Be(1);

            var userDto = response.MasterClientItems.Single().MasterClientUsers.Should().HaveCount(1).And.Subject.Single();
            userDto.DisplayName.Should().NotBeNullOrEmpty();
            userDto.InvitationDetails.LastInvitationAt.Should().Be(invitationDateTime);
        }

        /// <summary>
        /// Get all master clients and check user invitation details are mapped.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_Filters_By_MasterClientUser_Email()
        {
            // Arrange

            _masterClient.MasterClientUsers.Add(new MasterClientUser() { MasterClient = _masterClient, UserId = ClientUser.Id });
            await _masterClientRepository.UpdateAsync(_masterClient, true);

            var user2 = await CreateUser("<EMAIL>");

            var masterClient2Dto = await _masterClientsDataManager.CreateMasterClientAsync(
                new CreateMasterClientDTO { Code = "TESTCODE2" }, saveChanges: true);
            var masterClient2 = await _masterClientRepository.GetByIdAsync(masterClient2Dto.Id);

            masterClient2.MasterClientUsers.Add(new MasterClientUser() { MasterClientId = masterClient2.Id, UserId = user2.Id });
            await _masterClientRepository.UpdateAsync(masterClient2, true);

            await CreateCompany("Company B", "B", masterClient2.Id);

            var request = new ListMasterClientsRequest()
            {
                SearchTerm = "testuser2"
            };

            // Act
            var response = await _masterClientsDataManager.ListMasterClientsAsync(request);

            // Assert
            response.Should().NotBeNull();
            response.MasterClientItems.Should().NotBeNull();
            response.MasterClientItems.Count.Should().Be(1);
            response.MasterClientItems.Single().Id.Should().Be(masterClient2.Id);
        }

        private async Task<ApplicationUserDTO> CreateUser(string email)
        {
            var user2 = await _userManager.CreateUserWithRolesAsync(
                new RegistrationDTO
                {
                    LastName = "MC User 2",
                    FirstName = "Test",
                    UserName = email,
                    DisplayName = "Test User 2",
                    Email = email,
                    ObjectId = Guid.NewGuid()
                });
            return user2;
        }

        private async Task CreateCompany(string name, string code, Guid masterClientId)
        {
            await _legalEntitiesDataManager.CreateCompanyAsync(
                new CreateCompanyDTO
                {
                    MasterClientId = masterClientId,
                    Name = name,
                    Code = code,
                    JurisdictionId = _jurisdictionId
                }, saveChanges: true);
        }
    }
}
