﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Modules : TestBase
    {
        private ILegalEntitiesDataManager _legalEntitiesDataManager;
        private IMasterClientsDataManager _masterClientsDataManager;
        private IJurisdictionsRepository _jurisdictionsRepository;
        private IModulesDataManager _modulesDataManager;

        private readonly Guid _jurisdiction1Id = new Guid("{BDEF352D-DEDC-4271-888D-EFA168404CE9}");

        private Guid _masterClientId;

        private CompanyDTO _companyA;
        private CompanyDTO _companyB;

        private Module _module1;
        private Module _module2;
        private Module _module3;
        private Module _module4;

        [SetUp]
        public async Task Setup()
        {
            var context = _server.Services.GetRequiredService<TrustDbContext>();
            context.Set<JurisdictionModule>().RemoveRange(context.Set<JurisdictionModule>().ToList());
            context.Set<Module>().RemoveRange(context.Set<Module>().ToList());
            await context.SaveChangesAsync();

            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            _legalEntitiesDataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();
            _masterClientsDataManager = _server.Services.GetRequiredService<IMasterClientsDataManager>();


            var masterClient = await _masterClientsDataManager.CreateMasterClientAsync(new Application.Contracts.MasterClients.CreateMasterClientDTO { Code = "TEST_123" }, saveChanges: true);
            _masterClientId = masterClient.Id;

            _companyA = await _legalEntitiesDataManager.CreateCompanyAsync(new CreateCompanyDTO { MasterClientId = _masterClientId, Name = "Company A", Code = "A", JurisdictionId = _jurisdiction1Id }, saveChanges: true);
            _companyB = await _legalEntitiesDataManager.CreateCompanyAsync(new CreateCompanyDTO { MasterClientId = _masterClientId, Name = "Company B", Code = "B", JurisdictionId = _jurisdiction1Id }, saveChanges: true);

            // Modules
            var modulesRespository = _server.Services.GetRequiredService<IModulesRepository>();

            _module1 = new Module(new Guid("{33CA7BE6-5705-4E9A-8AC9-99CD86130822}"), "simple-tax-return", "Simple Tax Return");
            _module2 = new Module(new Guid("{A6B1047A-B762-4A3D-A7ED-1261ECF63068}"), "complex-tax-return", "Complex Tax Return");
            _module3 = new Module(new Guid("{D6DE2345-F951-40E3-BA71-4808339B8A1C}"), "bo-directors", "BO/Directors");
            _module4 = new Module(new Guid("{D6DE2345-F951-40E3-BA71-4808339B8A1C}"), "module-4", "Module 4");

            // Assign module to jurisdiction Nevis
            _module1.JurisdictionModules.Add(new JurisdictionModule(_jurisdiction1Id, _module1.Id));
            _module3.JurisdictionModules.Add(new JurisdictionModule(_jurisdiction1Id, _module3.Id));
            _module4.JurisdictionModules.Add(new JurisdictionModule(_jurisdiction1Id, _module3.Id));
            await modulesRespository.InsertAsync(_module1, saveChanges: true);
            await modulesRespository.InsertAsync(_module3, saveChanges: true);

            // Assign module1 explicitly to CompanyA with IsEnabled set to true
            var companyAModule1 = new LegalEntityModule(_companyA.Id, _module1.Id) { IsEnabled = true };
            _module1.LegalEntityModules.Add(companyAModule1);

            // Assign module2 to CompanyA (but module is not assigned to the jurisdiction of the company)
            _module2.LegalEntityModules.Add(new LegalEntityModule(_companyA.Id, _module2.Id));

            // Assign module2 to CompanyA with IsEnabled set to false
            var companyAModule2 = new LegalEntityModule(_companyA.Id, _module2.Id) { IsEnabled = false };
            _module2.LegalEntityModules.Add(companyAModule2);
            await modulesRespository.InsertAsync(_module2, saveChanges: true);
        }

        [Test]
        public async Task Test_Get_All_Modules()
        {
            // Arrange
            _modulesDataManager = _server.Services.GetRequiredService<IModulesDataManager>();
            var request = new DataManager.Modules.RequestResponses.ListModulesRequest();

            // Act
            var response = await _modulesDataManager.GetModulesAsync(request);

            // Assert
            Assert.That(response.ModuleItems.Count, Is.EqualTo(3));
        }

        [Test]
        public async Task Test_Get_Modules_For_Jurisdiction()
        {
            // Arrange
            _modulesDataManager = _server.Services.GetRequiredService<IModulesDataManager>();
            var request = new DataManager.Modules.RequestResponses.ListModulesRequest { JurisdictionId = _jurisdiction1Id };

            // Act
            var response = await _modulesDataManager.GetModulesAsync(request);

            // Assert
            Assert.That(response.ModuleItems.Count, Is.EqualTo(3));
        }

        [Test]
        public async Task Test_Get_Modules_For_Company()
        {
            // Arrange
            _modulesDataManager = _server.Services.GetRequiredService<IModulesDataManager>();
            var request = new DataManager.Modules.RequestResponses.ListModulesRequest { CompanyId = _companyA.Id };

            // Act
            var response = await _modulesDataManager.GetModulesAsync(request);

            // Assert
            Assert.That(response.CompanyModuleItems.Count, Is.EqualTo(2));
        }

        [Test]
        public async Task Test_Get_Modules_For_Company_Unassigned_Module_Is_Disabled()
        {
            // Arrange
            _modulesDataManager = _server.Services.GetRequiredService<IModulesDataManager>();
            var request = new DataManager.Modules.RequestResponses.ListModulesRequest { CompanyId = _companyB.Id };

            // Act
            var response = await _modulesDataManager.GetModulesAsync(request);

            // Assert
            // Module3 is available for the jurisdiction but not explicitly assigned to CompanyB
            var module3Item = response.CompanyModuleItems.FirstOrDefault(m => m.Id == _module3.Id);
            Assert.That(module3Item, Is.Not.Null, "Module3 should be in the response as it's available for the jurisdiction");
            Assert.That(module3Item.IsEnabled, Is.False, "Unassigned module should have IsEnabled set to false");
            Assert.That(module3Item.IsApproved, Is.False, "Unassigned module should have IsApproved set to false");
        }

        [Test]
        public async Task Test_Get_Modules_For_Company_Assigned_Modules_Have_Correct_Enabled_State()
        {
            // Arrange
            _modulesDataManager = _server.Services.GetRequiredService<IModulesDataManager>();
            var request = new DataManager.Modules.RequestResponses.ListModulesRequest { CompanyId = _companyA.Id };

            // Act
            var response = await _modulesDataManager.GetModulesAsync(request);

            // Assert
            var module1Item = response.CompanyModuleItems.FirstOrDefault(m => m.Id == _module1.Id);
            Assert.That(module1Item, Is.Not.Null, "Module1 should be in the response");
            Assert.That(module1Item.IsEnabled, Is.True, "Module1 should be enabled as set in the assignment");
            Assert.That(module1Item.IsApproved, Is.False, "Newly assigned module should have IsApproved set to false by default");

            var module4Item = response.CompanyModuleItems.FirstOrDefault(m => m.Id == _module4.Id);
            Assert.That(module4Item, Is.Not.Null, "Module2 should be in the response");
            Assert.That(module4Item.IsEnabled, Is.False, "Module2 should be disabled as set in the assignment");
            Assert.That(module4Item.IsApproved, Is.False, "Newly assigned module should have IsApproved set to false by default");
        }

        [Test]
        public async Task Test_Set_Modules_For_Jurisdiction()
        {
            _modulesDataManager = _server.Services.GetRequiredService<IModulesDataManager>();

            Guid testJurisdictionId1 = new Guid("{109D9DF3-8E42-4C0D-B7DA-757D4392FDCB}");
            //Guid testJurisdictionId2 = new Guid("{CA75BBAB-1EB5-4B1F-AEFB-400A1EA9DCDA}");

            // Arrange 1
            await _jurisdictionsRepository.InsertAsync(new Jurisdiction(testJurisdictionId1) { Code = "TEST1", Name = "Test 1" }, true);
            //await _jurisdictionsDataManager.CreateJurisdictionAsync(new Application.Contracts.Jurisdictions.JurisdictionDTO { Id = testJurisdictionId2, Code = "TEST2", Name = "Test 2" }, saveChanges: true);

            var request = new DataManager.Modules.RequestResponses.SetModulesRequest { JurisdictionId = testJurisdictionId1 };
            request.ModuleItems = new List<SetModuleDTO>
            {
                new() { Id = _module1.Id, IsEnabled = true },
                new() { Id = _module2.Id, IsEnabled = true }
            };

            // Act 1
            var response = await _modulesDataManager.SetModulesAsync(request);

            // Assert 1
            Assert.That(response.ModuleItems.Count, Is.EqualTo(3));
            Assert.That(response.ModuleItems.Where(x => x.IsEnabled.GetValueOrDefault()).Count, Is.EqualTo(2));

            // Arrange 2
            request = new DataManager.Modules.RequestResponses.SetModulesRequest { JurisdictionId = testJurisdictionId1 };
            request.ModuleItems = new List<SetModuleDTO>
            {
                new() { Id = _module1.Id, IsEnabled = false }
            };

            // Act 2
            var response2 = await _modulesDataManager.SetModulesAsync(request);

            // Assert 2
            Assert.That(response2.ModuleItems.Count, Is.EqualTo(3));
            Assert.That(response2.ModuleItems.Where(x => x.IsEnabled.GetValueOrDefault()).Count, Is.EqualTo(1));
        }
    }
}
