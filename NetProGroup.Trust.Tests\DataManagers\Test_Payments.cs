using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.DataManager.Payments;
using NetProGroup.Trust.DataManager.Payments.RequestResponses;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    [TestFixture]
    public class Test_Payments : TestBase
    {
        private IPaymentRepository _paymentRepository;
        private IPaymentDataManager _paymentDataManager;

        [SetUp]
        public void Setup()
        {
            _paymentRepository = _server.Services.GetRequiredService<IPaymentRepository>();
            _paymentDataManager = _server.Services.GetRequiredService<IPaymentDataManager>();
        }

        private Domain.Payments.Payment CreateTestPayment(Guid id, decimal amount, PaymentStatus status, string invoiceNr, bool isActive = true)
        {
            return new Domain.Payments.Payment(id)
            {
                Amount = amount,
                Status = status,
                Currency = new Currency(
                    new Guid("7890ab12-3456-def7-8901-234567890abc"), 
                    "United States Dollar",
                    "USD", 
                    "$"),
                LegalEntity = new LegalEntity(isActive: isActive)
                {
                    Name = $"Entity {status}",
                    Code = $"E-{status}",
                    EntityType = LegalEntityType.Company,
                    IncorporationDate = DateTime.UtcNow.AddYears(-2),
                    MasterClientId = _masterClient.Id
                },
                PaymentInvoices = new List<PaymentInvoice>
                {
                    new PaymentInvoice
                    {
                        Invoice = new Invoice(Guid.NewGuid())
                        {
                            InvoiceNr = invoiceNr,
                            Date = DateTime.UtcNow.AddMonths(-1),
                            FinancialYear = 2023,
                            Layout = LayoutConsts.TridentTrust
                        }
                    }
                }
            };
        }

        private async Task SeedPaymentsAsync(List<Domain.Payments.Payment> payments)
        {
            await _paymentRepository.InsertAsync(payments);
            await _paymentRepository.SaveChangesAsync();
        }

        private PaymentsRequest CreatePaymentRequest(PaymentStatus status)
        {
            return new PaymentsRequest
            {
                PageNumber = 1,
                PageSize = 10,
                Status = status,
                UserId = ClientUser.Id
            };
        }

        private void AssertPaymentsResult(IEnumerable<PaymentDTO> result, PaymentStatus expectedStatus, int expectedCount)
        {
            Assert.NotNull(result);
            Assert.That(result.Count(), Is.EqualTo(expectedCount));
            Assert.IsTrue(result.All(p => p.Status == expectedStatus));
        }

        [Test]
        public async Task ListPaymentsAsync_FilterByPendingStatus_ReturnsCorrectResults()
        {
            // Arrange
            var payments = new List<Domain.Payments.Payment>
            {
                CreateTestPayment(Guid.NewGuid(), 1000, PaymentStatus.Pending, "INV-PENDING")
            };
            await SeedPaymentsAsync(payments);

            var request = CreatePaymentRequest(PaymentStatus.Pending);

            // Act
            var result = await _paymentDataManager.ListPaymentsAsync(request);

            // Assert
            AssertPaymentsResult(result, PaymentStatus.Pending, 1);
        }

        [Test]
        public async Task ListPaymentsAsync_FilterByCompletedStatus_ReturnsCorrectResults()
        {
            // Arrange
            var payments = new List<Domain.Payments.Payment>
            {
                CreateTestPayment(Guid.NewGuid(), 2000, PaymentStatus.Completed, "INV-COMPLETED")
            };
            await SeedPaymentsAsync(payments);

            var request = CreatePaymentRequest(PaymentStatus.Completed);

            // Act
            var result = await _paymentDataManager.ListPaymentsAsync(request);

            // Assert
            AssertPaymentsResult(result, PaymentStatus.Completed, 1);
        }

        [Test]
        public async Task ListPaymentsAsync_FilterByFailedStatus_ReturnsCorrectResults()
        {
            // Arrange
            var payments = new List<Domain.Payments.Payment>
            {
                CreateTestPayment(Guid.NewGuid(), 3000, PaymentStatus.Failed, "INV-FAILED")
            };
            await SeedPaymentsAsync(payments);

            var request = CreatePaymentRequest(PaymentStatus.Failed);

            // Act
            var result = await _paymentDataManager.ListPaymentsAsync(request);

            // Assert
            AssertPaymentsResult(result, PaymentStatus.Failed, 1);
        }

        [Test]
        public async Task ListPaymentsAsync_FilterByRefundedStatus_ReturnsCorrectResults()
        {
            // Arrange
            var payments = new List<Domain.Payments.Payment>
            {
                CreateTestPayment(Guid.NewGuid(), 4000, PaymentStatus.Refunded, "INV-REFUNDED")
            };
            await SeedPaymentsAsync(payments);

            var request = CreatePaymentRequest(PaymentStatus.Refunded);

            // Act
            var result = await _paymentDataManager.ListPaymentsAsync(request);

            // Assert
            AssertPaymentsResult(result, PaymentStatus.Refunded, 1);
        }
    }
}
