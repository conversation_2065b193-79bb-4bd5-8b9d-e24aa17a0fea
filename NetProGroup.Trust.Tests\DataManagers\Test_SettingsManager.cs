﻿using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_SettingsManager : TestBase
    {
        private ISettingsAppService _settingsAppService;
        private ISettingsManager _settingsManager;
        private IJurisdictionsRepository _jurisdictionsRepository;

        private Jurisdiction _jurisdiction;

        private Guid _jurisdictionId;
        private Guid _masterClientId;
        private Guid _companyId;

        /// <summary>
        /// Setup the test.
        /// </summary>
        /// <returns></returns>
        [SetUp]
        public void Setup()
        {
            _settingsAppService = _server.Services.GetRequiredService<ISettingsAppService>();
            _settingsManager = _server.Services.GetRequiredService<ISettingsManager>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();


            _jurisdiction = _jurisdictionsRepository.FindFirstOrDefaultByCondition(x => x.Code == "Nevis", q => q.Include(x => x.MasterClients).ThenInclude(x => x.LegalEntities));
            _jurisdictionId = _jurisdiction.Id;

            var masterClient = new MasterClient() { Code = "Unit Test 1" };

            var company = new LegalEntity()
            {
                EntityType = DomainShared.Enums.LegalEntityType.Company,
                Name = "Unit Test Company 1",
                Code = "UT 1"
            };
            masterClient.LegalEntities.Add(company);

            _jurisdiction.MasterClients.Add(masterClient);

            _jurisdictionsRepository.SaveChanges();

            _masterClientId = masterClient.Id;
            _companyId = company.Id;

            SetWorkContextUser(ManagementUser);
        }

        [Test]
        public async Task Test_SetAllSettings()
        {
            var allSettings = new Application.Contracts.Settings.FeeSettingsDTO
            {
                STRSubmissionFee = (decimal?)25.25
            };

            await _settingsAppService.SaveSettingsForJurisdictionAsync(allSettings, _jurisdictionId);
        }

        [Test]
        public async Task Test_STR_LatePaymentFee()
        {
            var latePaymentFeesDTO = await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(_jurisdictionId, 2024);
            Assert.That(latePaymentFeesDTO, Is.Not.Null);
            Assert.That(latePaymentFeesDTO.Count, Is.EqualTo(0));

            var latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = 2024,
                Amount = 100,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = DateTime.Today,
                EndAt = DateTime.Today,
            };
            await _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            latePaymentFeesDTO = await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(_jurisdictionId, 2024);
            Assert.That(latePaymentFeesDTO, Is.Not.Null);
            Assert.That(latePaymentFeesDTO.Count, Is.EqualTo(1));

            var latePaymentFee = latePaymentFeesDTO.First();
            latePaymentFee.FinancialYear = null; // Delete it
            await _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFee, _jurisdictionId);

            latePaymentFeesDTO = await _settingsManager.GetSTRLatePaymentFeesForJurisdictionAsync(_jurisdictionId, 2024);
            Assert.That(latePaymentFeesDTO, Is.Not.Null);
            Assert.That(latePaymentFeesDTO.Count, Is.EqualTo(0));
        }

        [Test]
        public async Task Test_STR_LatePaymentFee_Throw_On_Negative_Amount()
        {
            var latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = 2024,
                Amount = -1,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = DateTime.Today,
                EndAt = DateTime.Today,
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentException>();
        }

        [Test]
        public async Task Test_STR_LatePaymentFee_Throw_On_Invalid_Year()
        {
            var latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = 2050,
                Amount = -1,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = DateTime.Today,
                EndAt = DateTime.Today,
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentException>();
        }

        [Test]
        public async Task Test_STR_LatePaymentFee_Throw_On_Overlapping_Period()
        {
            var latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = DateTime.Today.Year,
                Amount = 100,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = DateTime.Today,
                EndAt = DateTime.Today.AddMonths(1),
            };

            await _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = DateTime.Today.Year,
                Amount = 100,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = DateTime.Today.AddDays(5),
                EndAt = DateTime.Today.AddDays(5).AddMonths(1),
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            // Act/Assert
            await action.Should().ThrowAsync<ConstraintException>();
        }

        [Test]
        public async Task Test_STR_LatePaymentFee_Sequential_Period_Success()
        {
            var latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = DateTime.Today.Year,
                Amount = 100,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = DateTime.Today,
                EndAt = DateTime.Today.AddMonths(1),
            };

            await _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            latePaymentFeeDTO = new STRLatePaymentFeeDTO
            {
                FinancialYear = DateTime.Today.Year,
                Amount = 100,
                CurrencyCode = "USD",
                Charge = true,
                Description = "Test",
                InvoiceText = "Test",
                StartAt = latePaymentFeeDTO.EndAt.AddDays(1),
                EndAt = latePaymentFeeDTO.EndAt.AddDays(1).AddMonths(1),
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForJurisdictionAsync(latePaymentFeeDTO, _jurisdictionId);

            // Act/Assert
            await action.Should().NotThrowAsync<ConstraintException>();
        }

        [Test]
        public async Task Test_STRSubmissionFee_Jurisdiction_Throw_On_Negative_Amount()
        {
            var feeSettingsDTO = new FeeSettingsDTO
            {
                STRSubmissionFee = -1
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForJurisdictionAsync(feeSettingsDTO, _jurisdictionId);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentException>();
        }

        [Test]
        public async Task Test_STRSubmissionFee_Company_Throw_On_Negative_Amount()
        {
            var feeSettingsDTO = new FeeSettingsDTO
            {
                STRSubmissionFee = -1
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForCompanyAsync(feeSettingsDTO, _companyId);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentException>();
        }

        [Test]
        public async Task Test_BFRSubmissionFee_Jurisdiction_Throw_On_Negative_Amount()
        {
            var feeSettingsDTO = new FeeSettingsDTO
            {
                BFRSubmissionFee = -1
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForJurisdictionAsync(feeSettingsDTO, _jurisdictionId);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentException>();
        }

        [Test]
        public async Task Test_BFRSubmissionFee_Company_Throw_On_Negative_Amount()
        {
            var feeSettingsDTO = new FeeSettingsDTO
            {
                BFRSubmissionFee = -1
            };

            Func<Task> action = () => _settingsManager.SaveSettingsForCompanyAsync(feeSettingsDTO, _companyId);

            // Act/Assert
            await action.Should().ThrowAsync<ArgumentException>();
        }

        [Test]
        public async Task Test_InvoiceNumbering()
        {
            _settingsManager = _server.Services.GetRequiredService<ISettingsManager>();

            var settings = await _settingsManager.GetInvoiceNumberingSettingsAsync(_jurisdictionId, null);

            Assert.That(settings, Is.Not.Null);
            Assert.That(settings.InitialNumber, Is.EqualTo(-1));

            settings.PrefixFormat = "{yy}/{mm}";
            settings.InitialNumber = 4000;

            await _settingsManager.SaveInvoiceNumberingSettingsAsync(_jurisdictionId, null, settings);


            settings = await _settingsManager.GetInvoiceNumberingSettingsAsync(_jurisdictionId, null);
            Assert.That(settings, Is.Not.Null);
            Assert.That(settings.InitialNumber, Is.EqualTo(4000));
        }
    }
}
