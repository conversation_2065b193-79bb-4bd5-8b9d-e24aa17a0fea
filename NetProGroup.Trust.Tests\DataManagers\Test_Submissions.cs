﻿using FluentAssertions;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Submissions : TestBase
    {

        private ISubmissionsManager _submissionsManager;
        private IModulesRepository _modulesRepository;
        private Guid _jurisdictionId;
        private Guid _masterClientId;
        private Guid _legalEntityId;
        private Guid _submissionId;
        private static readonly DateTime _startDate = new(2024, 1, 1);
        private static readonly DateTime _endDate = new(2024, 12, 31);

        private Module _strModule;

        [SetUp]
        public async Task Setup()
        {
            _submissionsManager = _server.Services.GetRequiredService<ISubmissionsManager>();
            _modulesRepository = _server.Services.GetRequiredService<IModulesRepository>();
            
            _modulesRepository.Insert(new Module(Guid.NewGuid(), ModuleKeyConsts.SimplifiedTaxReturn, ModuleKeyConsts.SimplifiedTaxReturn), saveChanges: true);

            _strModule = _modulesRepository.FindFirstOrDefaultByCondition(x => x.Key == ModuleKeyConsts.SimplifiedTaxReturn);

            // Setup a jurisdiction, company and module
            await SetupTestData();

            // Create a test submission
            await CreateTestSubmissionAsync();
        }

        [Test]
        public async Task ListSubmissionsAsync_Success()
        {
            // Arrange
            var pagedRequest = new ListSubmissionsRequest
            {
                LegalEntityId = _legalEntityId,
                ModuleId = ModuleBfrId,
                PagingInfo = new PagingInfo(1, 10),
                SortingInfo = new SortingInfo()
            };

            // Act/Assert
            var submissions = await _submissionsManager.ListSubmissionsAsync(pagedRequest);

            Assert.That(submissions.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task ListSubmissionsAsync_NullListSubmissionsRequest_ThrowsArgumentNullException()
        {
            // Arrange
            Func<Task> action = () => _submissionsManager.ListSubmissionsAsync((ListSubmissionsRequest)null);

            // Act & Assert
            await action.Should().ThrowAsync<ArgumentNullException>();
        }

        [Test]
        public async Task ListSubmissionsAsync_NullListSubmissionsByMasterClientRequest_ThrowsArgumentNullException()
        {
            // Arrange
            Func<Task> action = () => _submissionsManager.ListSubmissionsAsync((ListSubmissionsByMasterClientRequest)null);

            // Act & Assert
            await action.Should().ThrowAsync<ArgumentNullException>();
        }

        [Test]
        public async Task DeleteSubmissionAsync_SetsSubmissionToDeleted_PreservesRelatedEntities()
        {
            // Act
            await _submissionsManager.DeleteSubmissionAsync(_submissionId);

            // Assert
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsIncludingDeletedRepository>();

            // Retrieve the deleted submission
            var submission = await submissionsRepository.GetByIdAsync(_submissionId,
                q => q.Include(s => s.FormDocument.Attributes)
            );

            submission.Should().NotBeNull();
            submission.IsDeleted.Should().BeTrue();
            submission.DeletedAt.Should().NotBeNull();
            submission.FormDocument.Should().NotBeNull();
            submission.FormDocument.Attributes.Should().HaveCount(10);
        }

        [Test]
        public async Task DeleteSubmissionAsync_Error_SubmissionSubmitted()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Retrieve the created submission and mark it as submitted
            var submission = await submissionsRepository.GetByIdAsync(_submissionId);

            submission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(submission, true);

            // Try to delete a submitted submission
            Func<Task> action = () => _submissionsManager.DeleteSubmissionAsync(_submissionId);

            // Act/Assert
            await action.Should().ThrowAsync<PreconditionFailedException>();
        }

        [Test]
        public async Task DeleteSubmissionAsync_Error_SubmissionAlreadyDeleted()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Retrieve the created submission and mark it as deleted
            var submission = await submissionsRepository.GetByIdAsync(_submissionId);

            await _submissionsManager.DeleteSubmissionAsync(_submissionId);

            // Try to delete a deleted submission
            Func<Task> action = () => _submissionsManager.DeleteSubmissionAsync(_submissionId);

            // Act/Assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Test]
        public async Task DeleteSubmissionAsync_Error_InvalidSubmissionId()
        {
            // Try to delete a submission with a invalid submission Id
            Func<Task> action = () => _submissionsManager.DeleteSubmissionAsync(Guid.NewGuid());

            // Act/Assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Test]
        public async Task StartSubmissionAsync_Success()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Mark the existing submission as Submitted for testing purposes only
            var createdSubmission = await submissionsRepository.GetByIdAsync(_submissionId);
            createdSubmission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(createdSubmission, true);

            // Start a submission for Panama
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = _legalEntityId
            };

            var submission = await _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Act/Assert
            Assert.That(submission.Status, Is.EqualTo(SubmissionStatus.Temporal));
        }

        [Test]
        public async Task StartSubmissionAsync_Success_RetrieveExistingTemporalSubmission()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Mark the existing submission as Submitted for testing purposes only
            var createdSubmission = await submissionsRepository.GetByIdAsync(_submissionId);
            createdSubmission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(createdSubmission, true);

            // Start a submission for Panama
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = _legalEntityId
            };

            var submission = await _submissionsManager.StartSubmissionAsync(startSubmissionData);

            var newSubmission = await _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Act/Assert
            Assert.That(submission.Id, Is.EqualTo(newSubmission.Id));
        }

        [Test]
        public async Task StartSubmissionAsync_Error_InvalidModule()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Mark the existing submission as Submitted for testing purposes only
            var createdSubmission = await submissionsRepository.GetByIdAsync(_submissionId);
            createdSubmission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(createdSubmission, true);

            // Start a submission for Panama
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = Guid.NewGuid(),
                LegalEntityId = _legalEntityId
            };

            // Try to start a submission for Panama with an invalid module.
            Func<Task> action = () => _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Act/Assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Test]
        public async Task StartSubmissionAsync_Error_InvalidLegalEntity()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Mark the existing submission as Submitted for testing purposes only
            var createdSubmission = await submissionsRepository.GetByIdAsync(_submissionId);
            createdSubmission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(createdSubmission, true);

            // Start a submission for Panama
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = Guid.NewGuid()
            };

            // Try start a submission for Panama with an invalid legal entity id.
            Func<Task> action = () => _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Act/Assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Test]
        public async Task StartSubmissionAsync_Error_ExistingSubmission()
        {
            // Arrange
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = _legalEntityId
            };

            // Try to start a submission for Panama with an existing active submission.
            Func<Task> action = () => _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Act/Assert
            await action.Should().ThrowAsync<ConstraintException>();
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationAsync_Success()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Mark the existing submission as Submitted for testing purposes only
            var existingSubmission = await submissionsRepository.GetByIdAsync(_submissionId);
            existingSubmission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(existingSubmission, true);

            // Start a submission for Panama
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = _legalEntityId
            };

            var submission = await _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Set the data to update the submission general information.
            var updateSubmissionData = new UpdateSubmissionInformationDTO()
            {
                StartAt = new DateTime(2024, 1, 1),
                EndAt = new DateTime(2024, 12, 1),
            };

            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(submission.Id, updateSubmissionData, true);

            // Retrieve the created submission
            var createdSubmission = await submissionsRepository.GetByIdAsync(submission.Id);

            // Act/Assert
            // Check if the submission has the status Drat instead of Temporal
            Assert.That(createdSubmission.Status, Is.EqualTo(SubmissionStatus.Draft));
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationAsync_Error_InvalidDates_StartDateGreaterThanEndDate()
        {
            // Arrange

            // Set the data to update the created submission general information.
            var updateExistingSubmissionData = new UpdateSubmissionInformationDTO()
            {
                StartAt = new DateTime(2025, 1, 1),
                EndAt = new DateTime(2024, 12, 1),
            };

            // Try to update a submission with a start date greater than the end date.
            Func<Task> action = () => _submissionsManager.UpdateSubmissionGeneralInformationAsync(_submissionId, updateExistingSubmissionData, true);

            // Act/Assert
            await action.Should().ThrowAsync<BadRequestException>();
        }

        [Test]
        [Ignore("This test is failing because the dates are not being validated.")]
        public async Task UpdateSubmissionGeneralInformationAsync_Error_InvalidDates_MoreThanAYear()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Set the data to update the created submission general information.
            var updateExistingSubmissionData = new UpdateSubmissionInformationDTO()
            {
                StartAt = new DateTime(2022, 1, 1),
                EndAt = new DateTime(2024, 12, 1),
            };

            Func<Task> action = () => _submissionsManager.UpdateSubmissionGeneralInformationAsync(_submissionId, updateExistingSubmissionData, true);

            // Act/Assert
            await action.Should().ThrowAsync<BadRequestException>();
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationAsync_Error_InvalidSubmission()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Set the data to update the created submission general information.
            var updateExistingSubmissionData = new UpdateSubmissionInformationDTO()
            {
                StartAt = new DateTime(2025, 1, 1),
                EndAt = new DateTime(2024, 12, 1),
            };

            // Try to update a submission with an invalid submission id.
            Func<Task> action = () => _submissionsManager.UpdateSubmissionGeneralInformationAsync(Guid.NewGuid(), updateExistingSubmissionData, true);

            // Act/Assert
            await action.Should().ThrowAsync<NotFoundException>();
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationAsync_Error_OverlappingPeriods()
        {
            // Arrange
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Set the data to update the created submission general information.
            var updateExistingSubmissionData = new UpdateSubmissionInformationDTO()
            {
                StartAt = new DateTime(2024, 1, 1),
                EndAt = new DateTime(2024, 12, 1),
            };

            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(_submissionId, updateExistingSubmissionData, true);

            // Mark the existing submission as Submitted for testing purposes only
            var existingSubmission = await submissionsRepository.GetByIdAsync(_submissionId);
            existingSubmission.Status = SubmissionStatus.Submitted;

            await submissionsRepository.UpdateAsync(existingSubmission, true);

            // Start a submission for Panama
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = _legalEntityId
            };

            var submission = await _submissionsManager.StartSubmissionAsync(startSubmissionData);

            // Set the data to update the submission general information.
            var updateSubmissionData = new UpdateSubmissionInformationDTO()
            {
                StartAt = new DateTime(2024, 4, 1),
                EndAt = new DateTime(2025, 2, 1),
            };

            // Try to update a submission with overlapping periods.
            Func<Task> action = () => _submissionsManager.UpdateSubmissionGeneralInformationAsync(submission.Id, updateSubmissionData, true);

            // Act/Assert
            await action.Should().ThrowAsync<BadRequestException>();
        }

        [Test]
        public async Task Search_STR_Submissions()
        {
            // Arrange
            var request = new SearchSubmissionsRequest()
            {
                ModuleId = _strModule.Id,
                FinancialYear = 2020,
                SubmittedAfterDate = new DateTime(2024, 9, 30)
            };

            // Act
            var response = await _submissionsManager.SearchSubmissionsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
        }

        [Test]
        public async Task UpdateSubmissionBahamasFinancialPeriod_ShouldThrowPermissionException()
        {
            // Arrange
            var submission = await CreateBahamasSubmissionAsync();

            var dto = new UpdateSubmissionInformationDTO { StartAt = _startDate, EndAt = _endDate };

            // Act & Assert
            Func<Task> action = () => _submissionsManager.UpdateSubmissionGeneralInformationManagementAsync(submission.Id, dto);
            await action.Should().ThrowAsync<ForbiddenException>();
        }

        /// <summary>
        /// Creates a submission for the Bahamas jurisdiction.
        /// </summary>
        /// <returns></returns>
        private async Task<Submission> CreateBahamasSubmissionAsync()
        {
            var legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            var legalEntity = new LegalEntity
            {
                Code = "TEST_LEGAL_ENTITY",
                Name = "Test Legal Entity",
                JurisdictionId = JurisdictionBahamasId,
                MasterClientId = _masterClientId
            };

            await legalEntitiesRepository.InsertAsync(legalEntity, true);

            var legalEntityModule = new LegalEntityModule(legalEntity.Id, ModuleEsId)
            {
                IsApproved = true,
                IsEnabled = true
            };

            legalEntity.LegalEntityModules.Add(legalEntityModule);
            await legalEntitiesRepository.UpdateAsync(legalEntity, true);

            var dataSet = new Dictionary<string, string> { { "test", "test" } };

            return await CreateAndInitializeSubmissionAsync(ModuleEsId, legalEntity.Id, dataSet);
        }
        #region Setup

        /// <summary>
        /// Setup testing data needed to execute the tests.
        /// </summary>
        private async Task SetupTestData()
        {
            // Retrieve required services
            var moduleRepository = _server.Services.GetRequiredService<IModulesRepository>();
            var legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            var legalEntityModulesRepository = _server.Services.GetRequiredService<ILegalEntityModulesRepository>();
            var jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var formTemplatesRepository = _server.Services.GetRequiredService<IFormTemplatesRepository>();
            var modulesRepository = _server.Services.GetRequiredService<IModulesRepository>();
            var masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            var masterClientUsersRepository = _server.Services.GetRequiredService<IMasterClientUsersRepository>();
            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            var userRepository = _server.Services.GetRequiredService<IUserRepository>();
            var userManager = _server.Services.GetRequiredService<IUserManager>();

            // Create a test jurisdiction
            var existingJurisdiction = jurisdictionsRepository.FindFirstOrDefaultByCondition(x => x.Code == JurisdictionCodes.Panama);
            _jurisdictionId = existingJurisdiction.Id;

            // Create a master client code
            var existingMasterClient = masterClientsRepository.FindFirstOrDefaultByCondition(x => x.Code == "TEST1");
            if (existingMasterClient == null)
            {
                var toDb = new MasterClient(Guid.NewGuid(), "TEST1")
                {
                    IsActive = true
                };
                masterClientsRepository.Insert(toDb, saveChanges: true);
                _masterClientId = toDb.Id;
            }

            // Create as test company
            var code = $"Code test 1";
            var existingCompany = legalEntitiesRepository.FindFirstOrDefaultByCondition(x => x.Code == code);

            if (existingCompany == null)
            {
                var dataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();

                var createCompanyDto = new CreateCompanyDTO
                {
                    Code = code,
                    Name = "Test company 1",
                    MasterClientId = _masterClientId,
                    JurisdictionId = _jurisdictionId,
                    IncorporationNr = "ABCD",
                    IncorporationDate = DateTime.UtcNow,
                    IsActive = true
                };

                var companyDTO = dataManager.CreateCompanyAsync(createCompanyDto, true).Result;

                _legalEntityId = companyDTO.Id;
            }

            // Assign a module to the created company

            var existing = legalEntityModulesRepository.FindFirstOrDefaultByCondition(
                x => x.LegalEntityId == _legalEntityId &&
                x.ModuleId == ModuleBfrId);

            if (existing == null)
            {
                existing = new LegalEntityModule(_legalEntityId, ModuleBfrId);
                existing.IsApproved = true;
                existing.IsEnabled = true;

                legalEntityModulesRepository.Insert(existing, saveChanges: true);
            }

            // Setup the authenticated user
            var user = ClientUser;
            workContext.IdentityUserId = user.Id;
            workContext.User = await userManager.GetUserByIdAsync(workContext.IdentityUserId.Value);

            // Set a test email to pass the security validations.
            workContext.User.Email = "<EMAIL>";

            // Setup the master client user
            var masterClientUser = new MasterClientUser()
            {
                MasterClientId = _masterClientId,
                UserId = user.Id
            };

            await masterClientUsersRepository.InsertAsync(masterClientUser, true);

            // Setup the form template
            // Setup variables
            var moduleKey = ModuleKeyConsts.BasicFinancialReportPanama;

            // Retrieve the services needed
            var module = moduleRepository.FindFirstOrDefaultByCondition(x => x.Key == moduleKey);


            // Check if the template already exists.
            var formTemplate = formTemplatesRepository.FindFirstOrDefaultByCondition(
                ft => ft.JurisdictionId == _jurisdictionId &&
                    ft.ModuleId == module.Id,
                q => q.Include(ft => ft.FormTemplateVersions));

            if (formTemplate == null)
            {
                formTemplate = new FormTemplate(Guid.NewGuid())
                {
                    JurisdictionId = _jurisdictionId,
                    ModuleId = module.Id,
                    Key = moduleKey,
                    Name = module.Name + " template",
                };
                formTemplatesRepository.Insert(formTemplate, true);
            }

            var year = 2024;

            var formTemplateVersion = formTemplate.FormTemplateVersions.FirstOrDefault(ftv => ftv.Year == year);
            string version = "1.0";

            if (formTemplateVersion == null)
            {
                formTemplateVersion = new FormTemplateVersion { Name = year.ToString(), Version = version, Year = year, StartAt = null };
                formTemplate.FormTemplateVersions.Add(formTemplateVersion);
            }
            else
            {
                formTemplateVersion.Name = $"{formTemplate.Name} {year.ToString()}";
                formTemplateVersion.Version = version;
                formTemplateVersion.Year = year;
                formTemplateVersion.StartAt = null;
                formTemplateVersion.Year = year;
            }

            // Setup the form as KeyValueForm
            var sampleKeyValueForm = new NetProGroup.Trust.Forms.Forms.KeyValueForm();
            sampleKeyValueForm.Id = $"{moduleKey.ToLower()}.{year}";
            sampleKeyValueForm.Name = $"{moduleKey}.{year}";
            sampleKeyValueForm.Version = "1";
            sampleKeyValueForm.CreatedAt = DateTime.UtcNow;
            sampleKeyValueForm.CreatedBy = "TestController";
            sampleKeyValueForm.Description = $"Sample template for module {moduleKey}, year {year} (jurisdiction Test Jurisdiction)";

            // Create some fields for the main section
            sampleKeyValueForm.DataSet.Add("is-firstFinancial-report", "");
            sampleKeyValueForm.DataSet.Add("financial-period-from", "");
            sampleKeyValueForm.DataSet.Add("financial-period-to", "");
            sampleKeyValueForm.DataSet.Add("main-activity", "");
            sampleKeyValueForm.DataSet.Add("other-activity", "");
            sampleKeyValueForm.DataSet.Add("use-trident-tool", "");

            if (string.IsNullOrEmpty(formTemplateVersion.DataAsJson))
            {
                var bldr = new Trust.Forms.FormBuilder();
                bldr.Form = sampleKeyValueForm;
                formTemplateVersion.DataAsJson = bldr.ToJson();
            }

            formTemplatesRepository.Update(formTemplate, true);

        }

        private async Task<Submission> CreateAndInitializeSubmissionAsync(Guid moduleId, Guid legalEntityId, Dictionary<string, string> dataSet)
        {
            var submissionsAppService = _server.Services.GetRequiredService<ISubmissionsAppService>();
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            var startSubmissionData = new StartSubmissionDTO
            {
                ModuleId = moduleId,
                LegalEntityId = legalEntityId,
                FinancialYear = 2024
            };

            var submission = await submissionsAppService.StartSubmissionAsync(startSubmissionData);

            await submissionsAppService.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO
            {
                Id = submission.Id,
                DataSet = dataSet
            });

            var createdSubmission = await submissionsRepository.GetByIdAsync(submission.Id);
            createdSubmission.Status = SubmissionStatus.Draft;
            await submissionsRepository.UpdateAsync(createdSubmission, true);

            return createdSubmission;
        }

        private async Task CreateTestSubmissionAsync()
        {
            var dataSet = Enumerable.Range(1, 10)
                .ToDictionary(i => $"test{i}", i => $"test{i}");

            var submission = await CreateAndInitializeSubmissionAsync(ModuleBfrId, _legalEntityId, dataSet);
            _submissionId = submission.Id;
        }
        #endregion

    }
}
