﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Submissions_BVI_ES : TestBase
    {

        private ICommonSubmissionsDataManager _submissionsManager;
        private IModulesRepository _modulesRepository;
        private Guid _jurisdictionId;
        private Guid _masterClientId;
        private Guid _legalEntityId;
        private Guid _submissionId;

        [SetUp]
        public async Task Setup()
        {
            var factory = _server.Services.GetRequiredService<ISubmissionsDataManagerFactory>();
            _submissionsManager = factory.CreateSubmissionsDataManager(JurisdictionCodes.BritishVirginIslands, ModuleKeyConsts.EconomicSubstanceBVI);
            _modulesRepository = _server.Services.GetRequiredService<IModulesRepository>();

            // Setup a jurisdiction, company and module
            await SetupTestData();

            // Create a test submission
            await CreateTestSubmissionAsync();
        }

        [Test]
        public async Task ListSubmissionsAsync_Success()
        {
            // Arrange
            var pagedRequest = new ListSubmissionsRequest
            {
                LegalEntityId = _legalEntityId,
                ModuleId = ModuleEsBviId,
                PagingInfo = new PagingInfo(1, 10),
                SortingInfo = new SortingInfo()
            };

            // Act/Assert
            var submissions = await _submissionsManager.ListSubmissionsAsync(pagedRequest);

            Assert.That(submissions.Count, Is.EqualTo(1));

        }

        [Test]
        public async Task Search_ES_Submissions()
        {
            // Arrange
            var request = new SearchSubmissionsRequest()
            {
                ModuleId = ModuleEsBviId,
                FinancialYear = 2024,
                MasterClientSearchTerm = "AB"
            };

            request.AuthorizedJurisdictionIDs.Add(_jurisdictionId);

            // Act
            var response = await _submissionsManager.SearchSubmissionsAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
        }

        [Test]
        public async Task Schedule_For_Submit()
        {
            // Arrange            
            var request = new SubmitSubmissionDTO()
            {
                SubmissionId = _submissionId,
                ScheduleSubmit = true
            };

            // Act
            var response = await _submissionsManager.SubmitSubmissionAsync(request);

            await _submissionsManager.SubmitScheduledSubmissionAsync(_submissionId);

            // Assert
            Assert.That(response, Is.Not.Null);
        }


        #region Setup

        /// <summary>
        /// Setup testing data needed to execute the tests.
        /// </summary>
        private async Task SetupTestData()
        {
            // Retrieve required services
            var moduleRepository = _server.Services.GetRequiredService<IModulesRepository>();
            var legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            var legalEntityModulesRepository = _server.Services.GetRequiredService<ILegalEntityModulesRepository>();
            var jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var formTemplatesRepository = _server.Services.GetRequiredService<IFormTemplatesRepository>();
            var modulesRepository = _server.Services.GetRequiredService<IModulesRepository>();
            var masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            var masterClientUsersRepository = _server.Services.GetRequiredService<IMasterClientUsersRepository>();
            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            var userRepository = _server.Services.GetRequiredService<IUserRepository>();
            var userManager = _server.Services.GetRequiredService<IUserManager>();

            // Create a test jurisdiction
            var existingJurisdiction = jurisdictionsRepository.FindFirstOrDefaultByCondition(x => x.Code == JurisdictionCodes.BritishVirginIslands);
            _jurisdictionId = existingJurisdiction.Id;

            // Create a master client code
            var existingMasterClient = masterClientsRepository.FindFirstOrDefaultByCondition(x => x.Code == "TEST1");
            if (existingMasterClient == null)
            {
                var jurisdiction = jurisdictionsRepository.FindFirstOrDefaultByCondition(x => x.Code == "TestJurisdiction");

                var toDb = new MasterClient(Guid.NewGuid(), "TEST1")
                {
                    IsActive = true
                };
                masterClientsRepository.Insert(toDb, saveChanges: true);
                _masterClientId = toDb.Id;
            }

            // Create as test company
            var code = $"Code test 1";
            var existingCompany = legalEntitiesRepository.FindFirstOrDefaultByCondition(x => x.Code == code);

            if (existingCompany == null)
            {
                var dataManager = _server.Services.GetRequiredService<ILegalEntitiesDataManager>();

                var createCompanyDto = new CreateCompanyDTO
                {
                    Code = code,
                    Name = "Test company 1",
                    MasterClientId = _masterClientId,
                    JurisdictionId = _jurisdictionId,
                    IncorporationNr = "ABCD",
                    IncorporationDate = DateTime.UtcNow,
                    IsActive = true
                };

                var companyDTO = dataManager.CreateCompanyAsync(createCompanyDto, true).Result;

                _legalEntityId = companyDTO.Id;
            }

            // Assign a module to the created company

            var existing = legalEntityModulesRepository.FindFirstOrDefaultByCondition(
                x => x.LegalEntityId == _legalEntityId &&
                x.ModuleId == ModuleEsBviId);

            if (existing == null)
            {
                existing = new LegalEntityModule(_legalEntityId, ModuleEsBviId);
                existing.IsApproved = true;
                existing.IsEnabled = true;

                legalEntityModulesRepository.Insert(existing, saveChanges: true);
            }

            // Setup the authenticated user
            var user = ClientUser;
            workContext.IdentityUserId = user.Id;
            workContext.User = await userManager.GetUserByIdAsync(workContext.IdentityUserId.Value);

            // Set a test email to pass the security validations.
            workContext.User.Email = "<EMAIL>";

            // Setup the master client user
            var masterClientUser = new MasterClientUser()
            {
                MasterClientId = _masterClientId,
                UserId = user.Id
            };

            await masterClientUsersRepository.InsertAsync(masterClientUser, true);

            // Setup the form template
            // Setup variables
            var moduleKey = ModuleKeyConsts.EconomicSubstanceBVI;

            // Retrieve the services needed
            var module = moduleRepository.FindFirstOrDefaultByCondition(x => x.Key == moduleKey);


            // Check if the template already exists.
            var formTemplate = formTemplatesRepository.FindFirstOrDefaultByCondition(
                ft => ft.JurisdictionId == _jurisdictionId &&
                    ft.ModuleId == module.Id,
                q => q.Include(ft => ft.FormTemplateVersions));

            if (formTemplate == null)
            {
                formTemplate = new FormTemplate(Guid.NewGuid())
                {
                    JurisdictionId = _jurisdictionId,
                    ModuleId = module.Id,
                    Key = moduleKey,
                    Name = module.Name + " template",
                };
                formTemplatesRepository.Insert(formTemplate, true);
            }

            var year = 2024;

            var formTemplateVersion = formTemplate.FormTemplateVersions.FirstOrDefault(ftv => ftv.Year == year);
            string version = "1.0";

            if (formTemplateVersion == null)
            {
                formTemplateVersion = new FormTemplateVersion { Name = year.ToString(), Version = version, Year = year, StartAt = null };
                formTemplate.FormTemplateVersions.Add(formTemplateVersion);
            }
            else
            {
                formTemplateVersion.Name = $"{formTemplate.Name} {year.ToString()}";
                formTemplateVersion.Version = version;
                formTemplateVersion.Year = year;
                formTemplateVersion.StartAt = null;
                formTemplateVersion.Year = year;
            }

            // Setup the form as KeyValueForm
            var sampleKeyValueForm = new NetProGroup.Trust.Forms.Forms.KeyValueForm();
            sampleKeyValueForm.Id = $"{moduleKey.ToLower()}.{year}";
            sampleKeyValueForm.Name = $"{moduleKey}.{year}";
            sampleKeyValueForm.Version = "1";
            sampleKeyValueForm.CreatedAt = DateTime.UtcNow;
            sampleKeyValueForm.CreatedBy = "TestController";
            sampleKeyValueForm.Description = $"Sample template for module {moduleKey}, year {year} (jurisdiction Test Jurisdiction)";

            // Create some fields for the main section
            sampleKeyValueForm.DataSet.Add("is-firstFinancial-report", "");
            sampleKeyValueForm.DataSet.Add("financial-period-from", "");
            sampleKeyValueForm.DataSet.Add("financial-period-to", "");
            sampleKeyValueForm.DataSet.Add("main-activity", "");
            sampleKeyValueForm.DataSet.Add("other-activity", "");
            sampleKeyValueForm.DataSet.Add("use-trident-tool", "");

            if (string.IsNullOrEmpty(formTemplateVersion.DataAsJson))
            {
                var bldr = new Trust.Forms.FormBuilder();
                bldr.Form = sampleKeyValueForm;
                formTemplateVersion.DataAsJson = bldr.ToJson();
            }

            formTemplatesRepository.Update(formTemplate, true);

        }

        /// <summary>
        /// Create a test submission
        /// </summary>
        private async Task CreateTestSubmissionAsync()
        {
            // Retreive required services
            var submissionsAppService = _server.Services.GetRequiredService<ISubmissionsAppService>();
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Create a test submission for the company and module
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleEsBviId,
                LegalEntityId = _legalEntityId,
            };

            var submission = await submissionsAppService.StartSubmissionAsync(startSubmissionData);

            // Set the Draft status to the created submission.
            var createdSubmission = await submissionsRepository.GetByIdAsync(submission.Id);
            createdSubmission.Status = SubmissionStatus.Draft;

            await submissionsRepository.UpdateAsync(createdSubmission, true);
            _submissionId = submission.Id;
        }

        /// <summary>
        /// Create a test submission
        /// </summary>
        private async Task CreateSubmissionForSchedulingAsync()
        {
            // Retreive required services
            var submissionsAppService = _server.Services.GetRequiredService<ISubmissionsAppService>();
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Create a test submission for the company and module
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleEsBviId,
                LegalEntityId = _legalEntityId,
                FinancialYear = null
            };

            var submission = await submissionsAppService.StartSubmissionAsync(startSubmissionData);

            // Set the Draft status to the created submission.
            var createdSubmission = await submissionsRepository.GetByIdAsync(submission.Id);
            createdSubmission.Status = SubmissionStatus.Draft;

            await submissionsRepository.UpdateAsync(createdSubmission, true);
            _submissionId = submission.Id;
        }

        #endregion

    }
}
