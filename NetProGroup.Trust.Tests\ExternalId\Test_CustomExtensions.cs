﻿using NetProGroup.Trust.API.Areas.ExternalId.Controllers;

namespace NetProGroup.Trust.Tests.ExternalId
{
    public class Test_CustomExtensions
    {

        [Test]
        public void Test_GetMasterClientCode_From_Body_Succeeds()
        {
            var body = NetProGroup.Trust.Tests.Properties.Resources.CustomExtensionBody;

            var masterClientCode = OnAttributeCollectionSubmitController.GetClientMasterCode(body);

            Assert.That(masterClientCode, Is.EqualTo("123456"));
        }
    }
}
