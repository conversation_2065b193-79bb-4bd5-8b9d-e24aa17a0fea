﻿using NetProGroup.Trust.Forms.Elements;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Forms.MetaData;
using NetProGroup.Trust.Forms.Types;

namespace NetProGroup.Trust.Tests.FormBuilder
{
    public class Tests_FormBuilder
    {
        [Test]
        public void TestFormBuilder_Serializing_Roundtrip()
        {
            // STANDARD
            var document = new Forms.FormBuilder();
            var standardForm = new StandardForm("test_form");
            document.Form = standardForm;

            var field1 = standardForm.AddField("field_1", FieldType.Text, "Field 1");

            field1.AddCondition(new Forms.MetaData.Condition
            {
                ConditionType = ConditionType.Required,
                MatchType = Forms.Types.MatchType.MatchAny
            }).AddComparison(new Forms.MetaData.Comparison { SubjectId = "field_2", ComparisonValue = "20", ComparisonType = ComparisonType.LessThan });

            field1.AddSelectOption("1", "One");
            field1.AddSelectOption("2", "Two", true);
            field1.AddSelectOption("3", "Three");

            standardForm.AddArea("area_1");

            // Add a dataset with countries
            var datasetCountries = standardForm.CreateDataSet("countries");
            datasetCountries.CreateDataRow("1").Add("Name", "Bonaire").Add("Code", "BQ");
            datasetCountries.CreateDataRow("2").Add("Name", "Curacao").Add("Code", "CW");
            datasetCountries.CreateDataRow("3").Add("Name", "Netherlands").Add("Code", "NL");

            // Create a selection field for the countries
            var fieldSelectCountry = standardForm.AddField("country", FieldType.SelectList, "Country of residence");
            fieldSelectCountry.SelectDataSet = "countries";
            fieldSelectCountry.SelectValueColumnName = "Code";
            fieldSelectCountry.SelectDisplayColumnName = "Name";

            // Add a dataset with directors
            var datasetDirectors = standardForm.CreateDataSet("directors");
            datasetDirectors.CreateDataRow("1").Add("Name", "Director 1").Add("Type", "Individual");
            datasetDirectors.CreateDataRow("2").Add("Name", "Director 2").Add("Type", "Company");
            datasetDirectors.CreateDataRow("3").Add("Name", "Director 3").Add("Type", "Company");

            // Create a table to display row values
            var table = standardForm.AddTable("table");
            table.DataSet = "directors";

            TableCellText cellText;
            var cell = table.AddCell(new Forms.Elements.TableCell(""));
            cell.Class = "cell-bg-color";
            cellText = cell.AddText(new Forms.Elements.TableCellText(""));
            cellText.ColumnName = "Name";
            cellText.Class = "text-large";
            cellText = cell.AddText(new Forms.Elements.TableCellText(""));
            cellText.ColumnName = "Type";
            cellText.Class = "text-medium";

            // Create variables so a dialog can fill these
            standardForm.CreateVariable("var-company");
            standardForm.CreateVariable("var-company2");

            var json = document.ToJson();

            var document2 = Forms.FormBuilder.FromJson(json);
            var json2 = document2.ToJson();

            Assert.That(json, Is.EqualTo(json2));

            Assert.That(document2.Form.GetType(), Is.EqualTo(typeof(StandardForm)));

            standardForm = document.Form as StandardForm;
            Assert.That(standardForm.Pages[0].Elements.Count(), Is.EqualTo(4));

            // EXTENDED

            var extendedForm = new ExtendedForm("test_form");
            document.Form = extendedForm;
            extendedForm.AddField("field_1", FieldType.Text, "Field 1");

            var extendedJson = document.ToJson();

            document2 = Forms.FormBuilder.FromJson(extendedJson);

            Assert.That(document2.Form.GetType(), Is.EqualTo(typeof(ExtendedForm)));

            extendedForm = document.Form as ExtendedForm;
            Assert.That(extendedForm.Pages[0].Elements.Count(), Is.EqualTo(1));


            var values = standardForm.GetFormValues();
        }

        [Test]
        public void TestFormBuilder_Validation_All_OK()
        {
            // STANDARD
            var document = new Forms.FormBuilder();
            var standardForm = new StandardForm("test_form");
            document.Form = standardForm;

            standardForm.AddField(Forms.Elements.Field.CreateText("field_1", "Field 1", "field.1.text")).Value = "Dummy";
            standardForm.AddField(Forms.Elements.Field.CreateBoolean("field_2", "Field 2", "field.2.bool")).Value = "1";

            standardForm.AddField(Forms.Elements.Field.CreateDate("field_3", "Field 3", "field.2.date")).Value = "2024-07-25";
            standardForm.AddField(Forms.Elements.Field.CreateTime("field_4", "Field 4", "field.2.time")).Value = "23:05";
            standardForm.AddField(Forms.Elements.Field.CreateDateTime("field_5", "Field 5", "field.2.time")).Value = "2024-07-25 23:05";

            standardForm.AddField(Forms.Elements.Field.CreateSelectList("field_5", "Field 5", "field.2.time", new List<SelectOption>
            {
                new SelectOption { Value = "1", Label = "One" },
                new SelectOption { Value = "2", Label = "Two" },
                new SelectOption { Value = "3", Label = "Three" }
            }));

            standardForm.AddField(Forms.Elements.Field.CreateSelectList("field_6", "Field 6", "field.6.select", new List<SelectOption>
            {
                new SelectOption { Value = "1", Label = "One" },
                new SelectOption { Value = "2", Label = "Two", Selected = true },
                new SelectOption { Value = "3", Label = "Three" }
            }));

            standardForm.AddField(Forms.Elements.Field.CreateMultiSelectList("field_7", "Field 7", "field.7.select", new List<SelectOption>
            {
                new SelectOption { Value = "1", Label = "One" },
                new SelectOption { Value = "2", Label = "Two", Selected = true },
                new SelectOption { Value = "3", Label = "Three", Selected = true }
            }));

            var result = standardForm.ValidateFields();
        }

        [Test]
        public void TestFormBuilder_Get_Values()
        {
            // STANDARD
            var document = new Forms.FormBuilder();
            var standardForm = new StandardForm("test_form");
            document.Form = standardForm;

            standardForm.AddField(Forms.Elements.Field.CreateText("field_1", "Field 1", "field.1.text")).Value = "Dummy";
            standardForm.AddField(Forms.Elements.Field.CreateBoolean("field_2", "Field 2", "field.2.bool")).Value = "1";

            standardForm.AddField(Forms.Elements.Field.CreateDate("field_3", "Field 3", "field.3.date")).Value = "2024-07-25";
            standardForm.AddField(Forms.Elements.Field.CreateTime("field_4", "Field 4", "field.4.time")).Value = "23:05";
            standardForm.AddField(Forms.Elements.Field.CreateDateTime("field_5", "Field 5", "field.5.time")).Value = "2024-07-25 23:05";

            standardForm.AddField(Forms.Elements.Field.CreateSelectList("field_6", "Field 6", "field.6.select", new List<SelectOption>
            {
                new SelectOption { Value = "1", Label = "One" },
                new SelectOption { Value = "2", Label = "Two", Selected = true },
                new SelectOption { Value = "3", Label = "Three" }
            }));

            standardForm.AddField(Forms.Elements.Field.CreateMultiSelectList("field_7", "Field 7", "field.7.select", new List<SelectOption>
            {
                new SelectOption { Value = "1", Label = "One" },
                new SelectOption { Value = "2", Label = "Two", Selected = true },
                new SelectOption { Value = "3", Label = "Three", Selected = true }
            }));

            var result = standardForm.GetFormValues();
        }

        [Test]
        public void TestFormBuilder_DataSets()
        {
            // STANDARD
            var document = new Forms.FormBuilder();
            var standardForm = new StandardForm("test_form");
            document.Form = standardForm;

            var dataSet = new Forms.Data.DataSet("dataset-1");
            standardForm.DataSets.Add(dataSet);

            var dataRow = new Forms.Data.DataRow("dataset-1_datarow-1");
            dataRow.Values.Add("col1", "value1");
            dataSet.DataRows.Add(dataRow);

            dataRow = new Forms.Data.DataRow("dataset-1_datarow-1");
            dataRow.Values.Add("col1", "value2-1");
            dataRow.Values.Add("col2", "value2-2");
            dataSet.DataRows.Add(dataRow);

            var json = document.ToJson();

            var document2 = Forms.FormBuilder.FromJson(json);

            Assert.That(document2.ToJson(), Is.EqualTo(json));
        }

        [Test]
        public void TestFormBuilder_KeyValue()
        {
            // STANDARD
            var document = new Forms.FormBuilder();
            var keyValueForm = new KeyValueForm("test_form");
            document.Form = keyValueForm;

            keyValueForm.DataSet.Add("Key 1", "Value 1");
            keyValueForm.DataSet.Add("Key 2", "Value 2");
            keyValueForm.DataSet.Add("Key 3", "Value 3");
            keyValueForm.DataSet.Add("Key 4", "Value 4");

            var json = document.ToJson();

            var document2 = Forms.FormBuilder.FromJson(json);

            Assert.That(document2.ToJson(), Is.EqualTo(json));
        }

    }
}
