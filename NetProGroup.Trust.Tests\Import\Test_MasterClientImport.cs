﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Import.Interfaces;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Import
{
    public class Test_MasterClientImport : TestBase
    {
        private IMasterClientImport _masterClientImport;
        private IJurisdictionsRepository _jurisdictionRepository;

        [SetUp]
        public void Setup()
        {
            _masterClientImport = _server.Services.GetRequiredService<IMasterClientImport>();
            _jurisdictionRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
        }

        [Test]
        public async Task Import_MasterClient_Nevis()
        {
            var jurisdiction = await _jurisdictionRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == "Nevis");

            var stream = new MemoryStream(NetProGroup.Trust.Tests.Properties.Resources.NEVIS___Import_mcc);
            await _masterClientImport.ImportFileAsync(jurisdiction.Id, stream);
        }
    }
}
