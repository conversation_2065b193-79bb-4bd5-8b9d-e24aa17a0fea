﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Sync;
using NetProGroup.Trust.Tests.Shared;
using System.Text;

namespace NetProGroup.Trust.Tests.Import
{
    public class Test_SyncHelper : TestBase
    {
        private TrustDbContext _dbContext;

        [SetUp]
        public void Setup()
        {
            _dbContext = _server.Services.GetRequiredService<TrustDbContext>();
        }

        [Test]
        public async Task Test_Comparison()
        {
            var sqlBldr = new StringBuilder();
            var fields = new List<string> { "ClientCode", "EntityName", "EntityCode" };

            sqlBldr.AppendLine("IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = 'Staging_CompaniesHistory')) ");
            sqlBldr.AppendLine("BEGIN ");
            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, * FROM [Staging_Companies] ");
            sqlBldr.AppendLine("return ");
            sqlBldr.AppendLine("END");

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, C.* ");
            sqlBldr.AppendLine("FROM dbo.Staging_Companies AS C ");
            sqlBldr.AppendLine("LEFT JOIN dbo.Staging_CompaniesHistory hist ON C.EntityUniqueNr = hist.EntityUniqueNr");
            sqlBldr.AppendLine("WHERE ( ");

            SyncHelper.AddFieldComparison(sqlBldr, "c", fields);

            sqlBldr.AppendLine(")");

            var sql = sqlBldr.ToString();
        }

        [Test]
        public async Task Test_Nullables()
        {
            var sqlBldr = new StringBuilder();
            var fields = new List<string> { "ClientCode", "EntityName", "EntityCode" };

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, ");
            sqlBldr.AppendLine("D.ExternalUniqueId AS [DirUniqueNr], ");

            sqlBldr.AppendLine("D.Name AS [DirName], ");
            sqlBldr.AppendLine("D.Code AS [DirCode], ");

            SyncHelper.AddFieldsAsNull(sqlBldr, fields);

            sqlBldr.AppendLine("FROM dbo.Directors AS D ");
            sqlBldr.AppendLine("LEFT JOIN dbo.Staging_Directors SD ON D.ExternalUniqueId = SD.DirUniqueNr ");
            sqlBldr.AppendLine("WHERE SD.DirUniqueNr is null");

            var sql = sqlBldr.ToString();
        }

        [Test]
        public async Task Test_CopyStagingHistory()
        {
            var sql = NetProGroup.Trust.Domain.Repository.Sync.SQLBuilder.InsertIntoHistoryTable<SyncDirector>(_dbContext, "Staging_PCP_Directors", null);
        }
    }
}
