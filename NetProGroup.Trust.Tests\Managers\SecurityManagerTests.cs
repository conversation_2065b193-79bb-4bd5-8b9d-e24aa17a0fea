﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Managers
{
    public class SecurityManagerTests : TestBase
    {
        private ISecurityManager _securityManager;
        private IUserRepository _userRepository;
        private IJurisdictionsRepository _jurisdictionsRepository;

        [SetUp]
        public void Setup()
        {
            _securityManager = _server.Services.GetRequiredService<ISecurityManager>();
            _userRepository = _server.Services.GetRequiredService<IUserRepository>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
        }

        [Test]
        public async Task Check_Permission()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var user1 = ManagementUser;
            var jurisdiction1 = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == "Nevis");

            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.User = ManagementUser;
            workContext.SetProperty("jurisdictionid", jurisdiction1.Id);

            // Act / Assert
            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.Companies_Access_Onboarding);
        }

        [Test]
        public async Task IsClient_ForClientUser_IsTrue()
        {
            // Arrange
            var workContext1 = _server.Services.GetRequiredService<IWorkContext>();

            workContext1.IdentityUserId = ClientUser.Id;

            var user = ClientUser;

            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.User = user;

            // Act / Assert
            (await _securityManager.UserIsClient()).Should().BeTrue();
        }

        [Test]
        public async Task IsClient_ForManagementUser_IsFalse()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            var user = ManagementUser;

            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.User = user;

            // Act / Assert
            (await _securityManager.UserIsClient()).Should().BeFalse();
        }

        [Test]
        public async Task Check_Jurisdictions_Per_Permission()
        {
            #region Arrange

            var jurisdiction1 = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == "Nevis");

            SetWorkContextUser(ManagementUser);

            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.SetProperty("jurisdictionid", jurisdiction1.Id);

            #endregion

            #region Act & assert

            var jurisdictions = await _securityManager.GetJurisdictionsForManagementPermissionAsync(WellKnownPermissionNames.Companies_Access_Onboarding);

            Assert.That(jurisdictions.Count, Is.EqualTo(1));

            #endregion
        }
    }
}
