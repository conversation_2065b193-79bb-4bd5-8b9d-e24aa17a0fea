﻿using NetProGroup.Trust.DataManager.Payments.Invoices;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Managers
{
    public class Test_InvoiceNumberGenerator : TestBase
    {
        [SetUp]
        public void Setup()
        {

        }

        [Test]
        public async Task Generate_New_Prefix()
        {
            // Arrange
            var numberingSettings = new Application.Contracts.Settings.InvoiceNumberingSettingsDTO
            {
                InitialNumber = 4000,
                PrefixFormat = "{yy}{mm}"
            };

            // Act
            var prefix = InvoiceNumberGenerator.CreatePrefix(numberingSettings, new DateTime(2024, 5, 1));

            // Assert
            Assert.That(prefix, Is.EqualTo("2405"));
        }

        [Test]
        public async Task Generate_New_Initial_InvoiceNumber_Monthly()
        {
            // Arrange
            var numberingSettings = new Application.Contracts.Settings.InvoiceNumberingSettingsDTO
            {
                InitialNumber = 4000,
                PrefixFormat = "{yy}{mm}",
                RangeFormat = "{yy}{mm}",
                FullInvoiceNumberFormat = "{prefix}/{number}"
            };

            var lastInvoiceNumberData = new Domain.SettingsModels.InvoiceNumberData
            {
                // keep empty
            };

            // Act
            var newInvoiceNumberData = InvoiceNumberGenerator.GenerateNewInvoiceNumber(numberingSettings, lastInvoiceNumberData);

            // Assert
            Assert.That(newInvoiceNumberData.InvoiceNumber, Is.EqualTo(numberingSettings.InitialNumber));
        }

        [Test]
        public async Task Generate_New_Next_InvoiceNumber_Monthly()
        {
            // Arrange
            var numberingSettings = new Application.Contracts.Settings.InvoiceNumberingSettingsDTO
            {
                InitialNumber = 4000,
                PrefixFormat = "{yy}{mm}",
                RangeFormat = "{yy}{mm}",
                FullInvoiceNumberFormat = "{prefix}/{number}"
            };

            var prefix = InvoiceNumberGenerator.CreatePrefix(numberingSettings);

            var lastInvoiceNumberData = new Domain.SettingsModels.InvoiceNumberData
            {
                RangeIdentifier = prefix,
                InvoiceNumber = 4456
            };

            // Act
            var newInvoiceNumberData = InvoiceNumberGenerator.GenerateNewInvoiceNumber(numberingSettings, lastInvoiceNumberData);

            // Assert
            Assert.That(newInvoiceNumberData.InvoiceNumber, Is.EqualTo(lastInvoiceNumberData.InvoiceNumber + 1));
        }

        [Test]
        public async Task Generate_New_Next_InvoiceNumber_Yearly()
        {
            // Arrange
            var numberingSettings = new Application.Contracts.Settings.InvoiceNumberingSettingsDTO
            {
                InitialNumber = 4000,
                PrefixFormat = "{yy}{mm}",
                RangeFormat = "{yyyy}",
                FullInvoiceNumberFormat = "{prefix}/{number}"
            };

            var prefix = InvoiceNumberGenerator.CreatePrefix(numberingSettings);

            var lastInvoiceNumberData = new Domain.SettingsModels.InvoiceNumberData
            {
                RangeIdentifier = (DateTime.Today.Year).ToString(),
                InvoiceNumber = 4456
            };

            // Act
            var newInvoiceNumberData = InvoiceNumberGenerator.GenerateNewInvoiceNumber(numberingSettings, lastInvoiceNumberData);

            // Assert
            Assert.That(newInvoiceNumberData.InvoiceNumber, Is.EqualTo(lastInvoiceNumberData.InvoiceNumber + 1));

            Assert.That(newInvoiceNumberData.FullInvoiceNumber, Is.EqualTo($"{prefix}/{newInvoiceNumberData.InvoiceNumber}"));
        }
    }
}
