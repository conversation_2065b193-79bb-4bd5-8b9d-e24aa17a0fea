using Moq;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments.Invoices;

namespace NetProGroup.Trust.Tests.Mocks
{
    public class InvoiceMock
    {
        public static Invoice CreateMockInvoice(Guid id)
        {
            // Mock the LegalEntity
            var legalEntityMock = new Mock<LegalEntity>();
            legalEntityMock.SetupGet(le => le.Id).Returns(Guid.NewGuid());
            legalEntityMock.SetupGet(le => le.Name).Returns("Contoso Ltd.");

            // Mock the Currency
            var currencyMock = new Mock<Currency>();
            currencyMock.SetupGet(c => c.Id).Returns(Guid.NewGuid());
            currencyMock.SetupGet(c => c.Symbol).Returns("USD");

            // Mock the Document
            var documentMock = new Mock<Document>();
            documentMock.SetupGet(d => d.Id).Returns(Guid.NewGuid());
            documentMock.SetupGet(d => d.BlobName).Returns("invoice_document.pdf");

            // Create the Invoice instance with mocked dependencies
            var invoice = new Invoice(id)
            {
                LegalEntityId = legalEntityMock.Object.Id,
                LegalEntity = legalEntityMock.Object,
                CurrencyId = currencyMock.Object.Id,
                Currency = currencyMock.Object,
                FinancialYear = 2023,
                Amount = 1500.00M,
                InvoiceNr = "INV-001",
                DocumentId = documentMock.Object.Id,
                Document = documentMock.Object,
                Date = DateTime.UtcNow,
                DateDue = DateTime.UtcNow.AddDays(30),
                Status = DomainShared.Enums.InvoiceStatus.Pending,
                InvoiceLines = new HashSet<InvoiceLine>() // Assuming InvoiceLine is another entity
            };

            return invoice;
        }
    }
}