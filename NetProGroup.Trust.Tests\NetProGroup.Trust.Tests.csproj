﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>

    <IsPackable>false</IsPackable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.Test.json" />
    <None Remove="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.Test.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions" Version="6.12.1" />
    <PackageReference Include="Microsoft.AspNetCore.TestHost" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.10" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="MockQueryable.Moq" Version="7.0.0" />
    <PackageReference Include="Moq.EntityFrameworkCore" Version="*******" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.2.1" />
    <PackageReference Include="NUnit.Analyzers" Version="3.3.0" />
    <PackageReference Include="coverlet.collector" Version="3.1.2" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="System.Resources.Extensions" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="AppService\" />
    <Folder Include="App_Data\" />
    <Folder Include="Domain\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.API\NetProGroup.Trust.API.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Application.Contracts\NetProGroup.Trust.Application.Contracts.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Application\NetProGroup.Trust.Application.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.DataManager\NetProGroup.Trust.DataManager.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.FormBuilder\NetProGroup.Trust.Forms.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Import\NetProGroup.Trust.Import.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Reports\NetProGroup.Trust.Reports.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Repository\NetProGroup.Trust.Domain.Repository.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="App_Data\TestFiles\Documents\Test file.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <PropertyGroup>
    <NoWarn>$(NoWarn);NETSDK1206</NoWarn>
  </PropertyGroup>
  
</Project>
