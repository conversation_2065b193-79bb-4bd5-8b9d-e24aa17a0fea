﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace NetProGroup.Trust.Tests.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("NetProGroup.Trust.Tests.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Byte[].
        /// </summary>
        internal static byte[] BVI___Import_mcc___NEW {
            get {
                object obj = ResourceManager.GetObject("BVI___Import_mcc___NEW", resourceCulture);
                return ((byte[])(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {
        ///	&quot;type&quot;: &quot;microsoft.graph.authenticationEvent.attributeCollectionSubmit&quot;,
        ///	&quot;source&quot;: &quot;/tenants/4053da93-8216-46fd-a82a-a32155693958/applications/00000003-0000-0000-c000-000000000000&quot;,
        ///	&quot;data&quot;: {
        ///		&quot;@odata.type&quot;: &quot;microsoft.graph.onAttributeCollectionSubmitCalloutData&quot;,
        ///		&quot;userSignUpInfo&quot;: {
        ///			&quot;attributes&quot;: {
        ///				&quot;extension_4c89b91d25b24aa3b391f28d9de10e13_MasterClientCode&quot;: {
        ///					&quot;value&quot;: &quot;123456&quot;,
        ///					&quot;@odata.type&quot;: &quot;microsoft.graph.stringDirectoryAttributeValue&quot;,
        ///					&quot;attributeType&quot;: &quot;dire [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string CustomExtensionBody {
            get {
                return ResourceManager.GetString("CustomExtensionBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Byte[].
        /// </summary>
        internal static byte[] NEVIS___Import_companies {
            get {
                object obj = ResourceManager.GetObject("NEVIS - Import companies", resourceCulture);
                return ((byte[])(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Byte[].
        /// </summary>
        internal static byte[] NEVIS___Import_mcc {
            get {
                object obj = ResourceManager.GetObject("NEVIS - Import mcc", resourceCulture);
                return ((byte[])(obj));
            }
        }
    }
}
