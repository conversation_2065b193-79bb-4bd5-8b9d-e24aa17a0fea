﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="BVI___Import_mcc___NEW" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\BVI - Import mcc - NEW.xlsx;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="CustomExtensionBody" xml:space="preserve">
    <value>{
	"type": "microsoft.graph.authenticationEvent.attributeCollectionSubmit",
	"source": "/tenants/4053da93-8216-46fd-a82a-a32155693958/applications/00000003-0000-0000-c000-000000000000",
	"data": {
		"@odata.type": "microsoft.graph.onAttributeCollectionSubmitCalloutData",
		"userSignUpInfo": {
			"attributes": {
				"extension_4c89b91d25b24aa3b391f28d9de10e13_MasterClientCode": {
					"value": "123456",
					"@odata.type": "microsoft.graph.stringDirectoryAttributeValue",
					"attributeType": "directorySchemaExtension"
				}
			},
			"identities": [
				{
					"signInType": "federated",
					"issuer": "mail",
					"issuerAssignedId": "<EMAIL>"
				}
			]
		},
		"tenantId": "4053da93-8216-46fd-a82a-a32155693958",
		"authenticationEventListenerId": "863610fb-6ff3-4c17-9734-0a5192ec64e7",
		"customAuthenticationExtensionId": "c3940c87-1013-4f66-a66e-502d8ccd1333",
		"authenticationContext": {
			"correlationId": "a12334d7-9ea9-4954-86bf-8ed79abc439d",
			"client": {
				"ip": "************",
				"locale": "en-us",
				"market": "en-us"
			},
			"protocol": "OAUTH2.0",
			"clientServicePrincipal": {
				"id": "46a59e92-d197-4e6d-ac28-e4d813d67e1a",
				"appId": "6493b00c-7357-4273-9ea1-8cb4bf70b3c3",
				"appDisplayName": "Marcel Auth Test",
				"displayName": "Marcel Auth Test"
			},
			"resourceServicePrincipal": {
				"id": "2cf46a93-7813-450f-a06a-cb959c90d5a0",
				"appId": "00000003-0000-0000-c000-000000000000",
				"appDisplayName": "Microsoft Graph",
				"displayName": "Microsoft Graph"
			}
		}
	}
}</value>
  </data>
  <data name="NEVIS - Import companies" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\NEVIS - Import companies.xlsx;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="NEVIS - Import mcc" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\NEVIS - Import mcc.xlsx;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>