﻿using ClosedXML.Excel;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Reports;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports.Bahamas.EconomicSubstance
{
    public class EconomicSubstanceGeneratorTests : TestBase
    {
        private ISubmissionsRepository _submissionsRepository;
        private ISubmissionReportsAppService _submissionReportsAppService;
        private IModulesRepository _modulesRepository;
        private IRoleRepository _roleRepository;
        private IUserManager _userManager;
        private Module _module;
        private LegalEntity _legalEntity;

        [SetUp]
        public async Task SetUp()
        {
            _submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();
            _submissionReportsAppService = _server.Services.GetRequiredService<ISubmissionReportsAppService>();
            _modulesRepository = _server.Services.GetRequiredService<IModulesRepository>();
            _roleRepository = _server.Services.GetRequiredService<IRoleRepository>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            await Seed();
        }

        private async Task Seed()
        {
            _module = _modulesRepository.FindFirstOrDefaultByCondition(x => x.Key == ModuleKeyConsts.EconomicSubstanceBahamas);
            var bahamasRole = new ApplicationRole()
            {
                Name = WellKnownRoleNames.Bahamas_Owner,
                DisplayName = WellKnownRoleNames.Bahamas_Owner,
                ObjectId = Guid.NewGuid(),
                NormalizedName = WellKnownRoleNames.Bahamas_Owner.ToUpperInvariant(),
            };

            await _roleRepository.CreateRoleAsync(bahamasRole);
            await _userManager.CreateUserRoleAsync(new Framework.Services.Identity.Models.ApplicationUserRoleDTO
            {
                UserId = ManagementUser.Id,
                RoleId = bahamasRole.Id
            });
        }

        [Test]
        public async Task GenerateEconomicSubstanceReport_ShouldReturnValidReport()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var request = new SubmissionsReportRequestBahamasDTO
            {
                ModuleId = _module.Id,
            };

            // Act
            var result = await _submissionReportsAppService.GenerateSubmissionsReportForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.FileContent.Should().NotBeNull();

            using (var resultWorkbook = new XLWorkbook(result.FileContent))
            {
                var worksheet = resultWorkbook.Worksheet(1);
                worksheet.Should().NotBeNull();
                worksheet.Cell(1, 1).Value.Should().NotBeNull();
            }

        }

        [Test]
        public async Task Report_ShouldContainCorrectEmail()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(1, createdSubmission.CreatedByUser.Email, "Email should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectEntityName()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(2, createdSubmission.LegalEntity.Name, "Entity name should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectEntityCode()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(3, createdSubmission.LegalEntity.LegacyCode, "Entity code should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectMasterClientCode()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(4, createdSubmission.LegalEntity.MasterClient.Code, "Master client code should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectIncorporationNumber()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(5, createdSubmission.LegalEntity.IncorporationNr, "Incorporation number should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectStatus()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(6, createdSubmission.Status.ToString(), "Status should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectCreatedDate()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(7, createdSubmission.CreatedAt.ToString(WellKnownReportConstants.DateFormat), "Created date should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectSubmittedDate()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(8, createdSubmission.SubmittedAt.Value.ToString(WellKnownReportConstants.DateFormat), "Submitted date should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectSubmittedDateAsNull()
        {
            // Arrange
            // Simulate a submission that has not been submitted yet by setting SubmittedAt to null.
            Func<ISubmissionsRepository, Submission, Task> submissionSetup = async (repository, submission) =>
            {
                submission.SubmittedAt = null;
                await repository.UpdateAsync(submission, true);
            };

            var createdSubmission = await CreateSubmission(submissionSetup: submissionSetup);

            // Act and Assert
            await AssertCellValue(8, string.Empty, "Submitted date should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectReOpenDate()
        {
            // Arrange
            // Simulate re-opening the submission by updating the CreatedAt date
            Func<ISubmissionsRepository, Submission, Task> submissionSetup = async (repository, submission) =>
            {
                submission.FormDocument.FormDocumentRevisions.Add(new FormDocumentRevision
                {
                    Status = FormDocumentRevisionStatus.Draft,
                    CreatedAt = DateTime.UtcNow.AddDays(-1),
                });
                await repository.UpdateAsync(submission, true);
            };

            var createdSubmission = await CreateSubmission(submissionSetup: submissionSetup);

            // Act and Assert
            await AssertCellValue(9, createdSubmission.FormDocument.CreatedAt.ToString(WellKnownReportConstants.DateFormat), "Re-open date should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectReSubmittedDate()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(10, createdSubmission.SubmittedAt.Value.ToString(WellKnownReportConstants.DateFormat), "Re-submitted date should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectRfiComments()
        {
            // Arrange
            // Simulate adding a Request for Information (RFI) with comments
            Func<ISubmissionsRepository, Submission, Task> submissionSetup = async (repository, submission) =>
            {
                submission.RequestsForInformation.Add(new RequestForInformation
                {
                    DeadLine = DateTime.UtcNow.AddDays(30),
                    Comments = "RFI comments",
                    Response = "RFI response",
                    Status = RequestForInformationStatus.Completed,
                    CreatedBy = ManagementUser.Id,
                    CompletedAt = DateTime.UtcNow
                });
                await repository.UpdateAsync(submission, true);
            };

            var createdSubmission = await CreateSubmission(submissionSetup: submissionSetup);

            // Act and Assert
            await AssertCellValue(11, createdSubmission.RequestsForInformation.Last().Comments, "RFI comments should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectRfiCompletedDate()
        {
            // Arrange
            // Simulate adding a Request for Information (RFI) with a completed date
            Func<ISubmissionsRepository, Submission, Task> submissionSetup = async (repository, submission) =>
            {
                submission.RequestsForInformation.Add(new RequestForInformation
                {
                    DeadLine = DateTime.UtcNow.AddDays(30),
                    Comments = "RFI comments",
                    Response = "RFI response",
                    Status = RequestForInformationStatus.Completed,
                    CreatedBy = ManagementUser.Id,
                    CompletedAt = DateTime.UtcNow
                });

                await repository.UpdateAsync(submission, true);
            };

            var createdSubmission = await CreateSubmission(submissionSetup: submissionSetup);

            // Act and Assert
            await AssertCellValue(12, createdSubmission.RequestsForInformation.Last().CompletedAt.Value.ToString(WellKnownReportConstants.DateFormat), "RFI completed date should match");
        }

        [Test]
        public async Task Report_ShouldContainCorrectIncorporationDate()
        {
            // Arrange
            var createdSubmission = await CreateSubmission();

            // Act and Assert
            await AssertCellValue(13, createdSubmission.LegalEntity.IncorporationDate.Value.ToString(WellKnownReportConstants.DateFormat), "Incorporation date should match");
        }


        /// <summary>
        /// Helper method to assert a cell value in the Excel report.
        /// </summary>
        /// <param name="columnIndex">The column index (1-based).</param>
        /// <param name="expectedValue">The expected value.</param>
        /// <param name="message">The assertion message.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task AssertCellValue(int columnIndex, string expectedValue, string message)
        {
            // Generate the report
            var result = await GenerateReport();
            result.Should().NotBeNull();

            // Read the generated Excel file to verify the content
            using (var resultWorkbook = new XLWorkbook(result.FileContent))
            {
                var worksheet = resultWorkbook.Worksheet(1);
                var cellValue = worksheet.Cell(2, columnIndex).Value.ToString();

                // Verify that the value is correctly populated
                cellValue.Should().Be(expectedValue, message);
            }
        }

        /// <summary>
        /// Generates the report for the Economic Substance submissions in the Bahamas.
        /// </summary>
        /// <returns>The Generated Report.</returns>
        private async Task<ReportDownloadResponseDTO> GenerateReport()
        {
            var request = new SubmissionsReportRequestBahamasDTO
            {
                ModuleId = _module.Id,
            };

            return await _submissionReportsAppService.GenerateSubmissionsReportForBahamasAsync(request);
        }

        /// <summary>
        /// Creates a submission for testing with customizable properties.
        /// </summary>
        /// <param name="legalEntitySetup">Optional action to customize the legal entity.</param>
        /// <param name="submissionDTOSetup">Optional action to customize the submission DTO.</param>
        /// <param name="dataSet">Optional dataset to update the submission with.</param>
        /// <param name="submissionSetup">Optional action to perform additional setup on the submission after creation.</param>
        /// <param name="markSubmissionAsPaid">Whether to mark the submission as paid.</param>
        /// <returns>The created submission.</returns>
        protected async Task<Submission> CreateSubmission(
            Action<LegalEntity> legalEntitySetup = null,
            Action<StartSubmissionDTO> submissionDTOSetup = null,
            Dictionary<string, string> dataSet = null,
            Func<ISubmissionsRepository, Submission, Task> submissionSetup = null,
            bool markSubmissionAsPaid = true)
        {
            SetWorkContextUser(ManagementUser);
            // Create a legal entity
            _legalEntity = new LegalEntity()
            {
                Name = "Test Legal Entity",
                Code = "TEST_LEGAL_ENTITY",
                JurisdictionId = JurisdictionBahamasId,
                MasterClientId = _masterClient.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                EntityTypeCode = LegalEntityTypes.IBC,
                EntityType = LegalEntityType.Company,
                EntityStatus = LegalEntityStatusNames.Active,
                ExternalUniqueId = "asdf",
                IncorporationNr = "1234",
                LegacyCode = "1234",
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                EntityTypeName = "IBC",
                ReferralOffice = "Test Office"
            };

            // Apply custom setup to the legal entity if provided
            legalEntitySetup?.Invoke(_legalEntity);

            // Insert the legal entity
            _legalEntity = await _server.Services.GetRequiredService<ILegalEntitiesRepository>().InsertAsync(_legalEntity, true);

            // Create a submission
            var submissionsManager = _server.Services.GetService<ISubmissionsManager>();
            var submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            // Create a default submission DTO
            var startSubmissionDTO = new StartSubmissionDTO()
            {
                FinancialYear = 2019,
                ModuleId = _module.Id,
                LegalEntityId = _legalEntity.Id
            };

            // Apply custom setup to the submission DTO if provided
            submissionDTOSetup?.Invoke(startSubmissionDTO);

            // Ensure LegalEntityId is set
            if (startSubmissionDTO.LegalEntityId == Guid.Empty)
            {
                startSubmissionDTO.LegalEntityId = _legalEntity.Id;
            }

            var submission = await submissionsManager.StartSubmissionAsync(startSubmissionDTO);

            // Update submission dataset if provided
            if (dataSet != null)
            {
                await submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO()
                {
                    Id = submission.Id,
                    DataSet = dataSet
                });
            }

            // Submit the submission
            await submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO() { SubmissionId = submission.Id });

            if (markSubmissionAsPaid)
            {
                await submissionsManager.MarkSubmissionsAsPaidAsync([submission.Id], true, [JurisdictionNevisId]);
            }

            // Apply custom setup to the submission if provided
            if (submissionSetup != null)
            {
                var existingSubmission = await submissionsRepository.GetByIdAsync(submission.Id);
                await submissionSetup(submissionsRepository, existingSubmission);
            }

            return await _submissionsRepository.GetByIdAsync(submission.Id);
        }
    }
}
