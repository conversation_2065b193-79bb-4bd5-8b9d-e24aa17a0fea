﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.Nevis.ContactsInfo;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports
{
    public class Test_ContactsInfoReportGenerator : TestBase
    {
        [SetUp]
        public void Setup()
        {
            Seed();
        }

        private void Seed()
        {
            var jurisdictionRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            var legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            var masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();

            var userManager = _server.Services.GetRequiredService<IUserManager>();

            var jurisdictionId = jurisdictionRepository.FindAll().FirstOrDefault(x => x.Code == "Nevis").Id;

            // Get the client user
            var user = ClientUser;
            SetWorkContextUser(user);

            // Create a masterClient
            var masterClient = new MasterClient
            {
                Code = "MCC 1"
            };
            masterClient.MasterClientUsers.Add(new MasterClientUser { UserId = user.Id });
            masterClientsRepository.Insert(masterClient, true);

            // Create a legal entity
            var legalEntity = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = "Company 1",
                Code = "1",
                JurisdictionId = jurisdictionId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = "123456",
                ReferralOffice = "referral office"
            };
            legalEntitiesRepository.Insert(legalEntity, true);
        }

        [Test]
        public void Test_GenerateReportAsync_Succeeds()
        {
            // Arrange
            var contactsInfoReportGenerator = _server.Services.GetRequiredService<IContactsInfoReportGenerator>();

            // Act
            var report = contactsInfoReportGenerator.GenerateReportAsync();

            // Assert
            report.Should().NotBeNull();
        }

    }
}
