﻿using FluentAssertions;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports
{
    public class Test_ReportHelpers : TestBase
    {
        [SetUp]
        public void Setup()
        {

        }

        [Test]
        public void Test_Create_ReportDownloadResponseDTO()
        {
            // Act
            var model = ReportDownloadResponseDTO.Create("test.xlsx", new byte[] { });

            // Assert
            model.Extension.Should().Be("xlsx");
            model.ContentType.Should().Be(ReportContentTypes.Excel);
        }

        [Test]
        public void Test_Create_ReportDownloadResponseDTO_Fails_Extension()
        {
            Action action = () => ReportDownloadResponseDTO.Create("test.docx", new byte[] { });

            // Act/Assert
            action.Should().Throw<ContentTypeNotSupportedException>();
        }
    }
}
