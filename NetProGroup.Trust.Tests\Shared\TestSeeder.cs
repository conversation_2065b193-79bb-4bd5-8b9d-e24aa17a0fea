﻿using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Application.Seeders.JurisdictionSeeders;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Tests.Shared
{
    public class TestSeeder : SeederBase
    {
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ISettingsManager _settingsManager;
        private Jurisdiction _nevisJurisdiction;

        public TestSeeder(ILogger<TestSeeder> logger,
            IServiceProvider serviceProvider,
            IJurisdictionsRepository jurisdictionsRepository,
            ISettingsManager settingsManager) : base(logger, serviceProvider)
        {
            _jurisdictionsRepository = jurisdictionsRepository;
            _settingsManager = settingsManager;
        }

        public async Task RunAsync()
        {
            _nevisJurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Nevis);

            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.SimplifiedTaxReturn, JurisdictionCodes.Nevis);
            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.BODirectors, JurisdictionCodes.Nevis);


            await CreateNevisFeesSettingsAsync();
        }

        /// <summary>
        /// Create the settings for fees.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateNevisFeesSettingsAsync()
        {
            var dirty = false;
            var feeSettings = await _settingsManager.ReadSettingsForJurisdictionAsync<FeeSettingsDTO>(_nevisJurisdiction.Id);

            // STR
            if (!feeSettings.STRSubmissionFee.HasValue)
            {
                feeSettings.STRSubmissionFee = 100;
                dirty = true;
            }

            if (string.IsNullOrEmpty(feeSettings.STRSubmissionFeeInvoiceText))
            {
                feeSettings.STRSubmissionFeeInvoiceText = ConfigurationConsts.STR_INVOICETEXT;
                dirty = true;
            }

            // Save
            if (dirty)
            {
                await _settingsManager.SaveSettingsForJurisdictionAsync(feeSettings, _nevisJurisdiction.Id);
            }
        }
    }
}