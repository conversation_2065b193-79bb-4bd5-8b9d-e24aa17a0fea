﻿using AutoMapper;
using AutoMapper.Extensions.ExpressionMapping;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.DataManager.AutoMapper;

namespace NetProGroup.Trust.Tests.Shared
{
    public class Test_AutoMapper
    {
        private IMapper _mapper;
        private TestServer _server;

        /// <summary>
        /// Set up the test.
        /// </summary>
        /// <returns></returns>
        [SetUp]
        public void Setup()
        {
            _server = new TestServer(
                new WebHostBuilder()
                    .Configure(builder => { })
                    .ConfigureServices(collection =>
                        collection.AddAutoMapper(
                            cfg =>
                            {
                                cfg.AddExpressionMapping();
                            },
                            // Scan for automapper profiles in this assembly
                            typeof(ApplicationProfile).Assembly
                        )));

            _mapper = _server.Services.GetRequiredService<IMapper>();
        }

        /// <summary>
        /// Validate the mapping configuration.
        /// </summary>
        [Test]
        public void ValidateMappingConfigurationTest()
        {
            _mapper.ConfigurationProvider.AssertConfigurationIsValid();
        }
    }
}