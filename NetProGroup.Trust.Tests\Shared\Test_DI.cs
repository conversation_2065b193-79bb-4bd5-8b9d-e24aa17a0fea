﻿using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Tests.Shared
{
    public class Test_DI : TestBase
    {

        [SetUp]
        public void Setup()
        {

        }

        [Test]
        public async Task Create_All_Repository_Services()
        {
            // Act: Try resolving all services registered in DI
            var services = _server.Services.GetServices<IRepositoryService>().ToList();

            // Assert: Check that all services can be resolved
            foreach (var service in services)
            {
                Assert.NotNull(service);
            }
        }

        [Test]
        public async Task Create_All_Transient_Services()
        {
            // Act: Try resolving all services registered in DI
            var services = _server.Services.GetServices<ITransientService>().ToList();

            // Assert: Check that all services can be resolved
            foreach (var service in services)
            {
                Assert.NotNull(service);
            }
        }

        [Test]
        public async Task Create_All_Scoped_Services()
        {
            // Act: Try resolving all services registered in DI
            var services = _server.Services.GetServices<IScopedService>().ToList();

            // Assert: Check that all services can be resolved
            foreach (var service in services)
            {
                Assert.NotNull(service);
            }
        }

        [Test]
        public async Task Create_All_Singleton_Services()
        {
            // Act: Try resolving all services registered in DI
            var services = _server.Services.GetServices<ISingletonService>().ToList();

            // Assert: Check that all services can be resolved
            foreach (var service in services)
            {
                Assert.NotNull(service);
            }
        }
    }
}