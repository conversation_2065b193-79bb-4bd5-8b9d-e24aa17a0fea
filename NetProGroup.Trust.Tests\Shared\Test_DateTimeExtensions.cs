﻿using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Tests.Shared
{
    [TestFixture]
    public class Test_DateTimeExtensions
    {
        [SetUp]
        public void Setup()
        {

        }

        [TestCase("2024-01-06 10:00:00", JurisdictionCodes.Nevis, ExpectedResult = "2024-01-06 06:00")]
        [TestCase("2024-01-06 10:00:00", JurisdictionCodes.Panama, ExpectedResult = "2024-01-06 05:00")]
        [TestCase("2024-01-06 10:00:00", JurisdictionCodes.Bahamas, ExpectedResult = "2024-01-06 05:00")]
        [TestCase("2024-01-06 10:00:00", JurisdictionCodes.BritishVirginIslands, ExpectedResult = "2024-01-06 06:00")]
        public string Test_LocalTime(string value, string jurisdictionCode)
        {
            DateTime? dateTime = DateTime.ParseExact(value, "yyyy-MM-dd HH:mm:ss", null);
            return dateTime.ToLocalTime(jurisdictionCode).Value.ToString("yyyy-MM-dd HH:mm");
        }
    }
}
