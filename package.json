{"name": "netprogroup-trust-api", "private": true, "version": "1.0.2", "scripts": {"release": "commit-and-tag-version -t"}, "devDependencies": {"commit-and-tag-version": "^12.4.1", "xml2js": "^0.6.2"}, "commit-and-tag-version": {"releaseCommitMessageFormat": "chore(release): v{{currentTag}} see changelog for details [skip ci]", "commitUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/commit/{{hash}}", "issueUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/{{id}}", "compareUrlFormat": "https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_git/TT%20PCP%20-%20API/branchCompare?baseVersion=GT{{previousTag}}&targetVersion=GT{{currentTag}}&_a=files"}}